<?php

namespace App\Repositories;

use App\Notifications\GetCustomerNotification;
use App\Services\SmsGetWay;
use App\Verify;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use mysql_xdevapi\Exception;
use TypiCMS\Modules\Clients\Models\Client;
use TypiCMS\Modules\Customers\Models\Customer;

class VerifyRepository
{
    public function setVerifyPhone($request)
    {

        try {
            $code = rand(1000, 9999);
            $str = $request->phone . '|' . $code;
            $token = md5($str);
            $expire_min = env('SMS_EXPIRE_MIN', 5);
            $current_date = Carbon::now()->addMinutes($expire_min)->format('Y-m-d H:i:s');

            $verify                 = new Verify;
            $verify->type           = "phone";
            $verify->code           = $code;
            $verify->contact        = $request->phone;
            $verify->uuid           = $request->token;
            $verify->token          = $token;
            $verify->expired_at     = $current_date;
            $verify->save();


            return $code;
        } catch (\Exception $e) {
            throw new \Exception('Error in data save');
        }
    }

    public function checkVerifyPhone($request)
    {

        $current_date = date('Y-m-d H:i:s');
        $verify = Verify::whereUuid($request->token)
            ->whereCode($request->code)
            ->whereVerified(0)
            ->where('expired_at', '>=', $current_date)
            ->first();

        if ($verify) {
            $verify->update(['verified' => 1]);
            return $verify;
        } else {
            $error['error'] = [
                "message" => "The given data was invalid.",
                "errors" => [
                    "code" => [
                        __("The code is expired or incorrect")
                    ]
                ]
            ];
            return $error;
        }
    }

    public function updateVerifyPhone($token, $messageId)
    {
        try {
            $update_verify = Verify::whereToken($token);
            $update_verify->update(['message' => $messageId]);
            return  true;
        } catch (\Exception $e) {
            Log::channel('api')->error(__METHOD__, ['Message ' => $e->getMessage(), 'Trace ' => $e->getTraceAsString()]);
            throw new \Exception('Error in update messageId');
        }
    }

    public function reSendCode($request)
    {

        $current_date = date('Y-m-d H:i:s');
        $date_now = Carbon::now(); //->format('Y-m-d H:i:s');
        $sms_resend_interval = config('typicms.sms_resend_interval') ?? 1;

        $verify = Verify::whereUuid($request->client_token)
            ->whereVerified(1)
            ->whereType('email')
            ->where('expired_at', '>=', $current_date)
            ->first();

        if ($verify) {
            $date_interval_result = $date_now->get($verify->updated_at->addMinutes($sms_resend_interval));
            if (!$date_interval_result) {
                $error['error'] = __("you can send sms don't greater than 1 sms per minute");
                return $error;
            }
            Log::info('reSendCode action request:', ['request token' => $request->client_token, 'verify_client' => $verify]);

            $client_model = Client::whereUuid($request->client_token)->where('send_mulberry_status', 2)->first();
            if ($client_model) {
                $client_data = json_decode($client_model->data, true);
                $result = $this->createPhoneCode($client_data['phone'], $request->client_token);

                $verify->touch();
                return $result;
            } else {
                $error['error'] = __("this client already sent application");
                return $error;
            }
        } else {
            $error = ["error" => __("Email is not verified or expired")];
            return $error;
        }
    }

    public function setVerifyEmail($data)
    {

        try {
            $code = rand(1000, 9999);
            $str = $data['email'] . '|' . $code;
            $token = md5($str);
            $expire_min = env('EMAIL_EXPIRE_MIN', 120);
            $current_date = Carbon::now()->addMinutes($expire_min)->format('Y-m-d H:i:s');

            $verify                 = new Verify;
            $verify->type           = "email";
            $verify->code           = $code;
            $verify->contact        = $data['email'];
            $verify->uuid           = $data['uuid'];
            $verify->token          = $token;
            $verify->expired_at     = $current_date;
            $verify->save();

            return $verify;
        } catch (\Exception $e) {

            throw new \Exception('Error in data save');
        }
    }

    public function checkVerifyEmail($request)
    {

        $current_date = date('Y-m-d H:i:s');
        $verify = Verify::whereUuid($request->token)
            ->whereContact($request->email)
            ->where('expired_at', '>=', $current_date)
            ->first();

        if ($verify && $verify->verified == 0) {
            $verify->update(['verified' => 1]);
            return $verify;
        } elseif ($verify && $verify->verified != 0) {
            $error['error'] = [
                "message" => "email already verified",
                "errors" => [
                    "code" => [
                        __("email already verified")
                    ]
                ]
            ];
            return $error;
        } else {
            $error['error'] = [
                "message" => "The given data was invalid.",
                "errors" => [
                    "code" => [
                        __("The code is expired or incorrect")
                    ]
                ]
            ];
            return $error;
        }
    }


    public function updateVerifyEmail($token)
    {
        try {
            $update_verify = Verify::whereToken($token);
            return  true;
        } catch (\Exception $e) {
            Log::channel('api')->error(__METHOD__, ['Message ' => $e->getMessage(), 'Trace ' => $e->getTraceAsString()]);
            throw new \Exception('Error in update messageId');
        }
    }


    public function createPhoneCode($phone, $token)
    {

        try {
            $code = rand(1000, 9999);
            $str = $phone . '|' . $code;
            $_token = md5($str);
            $expire_min = env('SMS_EXPIRE_MIN', 5);
            $current_date = Carbon::now()->addMinutes($expire_min)->format('Y-m-d H:i:s');

            $verify                 = new Verify;
            $verify->type           = "phone";
            $verify->code           = $code;
            $verify->contact        = $phone;
            $verify->uuid           = $token;
            $verify->token          = $_token;
            $verify->expired_at     = $current_date;
            $verify->save();

            return $code;
        } catch (\Exception $e) {
            throw new \Exception('Error in data save');
        }
    }
}
