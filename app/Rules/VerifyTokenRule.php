<?php

namespace App\Rules;

use App\Verify;
use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\Hash;

class VerifyTokenRule implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        if(request()->has('phone')) {
            $verify = Verify::whereToken($value)->whereVerified(1)->first();
            if ($verify && $verify->count() > 0) {
                $str = request()->get('phone') . '|' . $verify->code;
                $new_token = md5($str);
                if($new_token == $value) {
                    return true;
                }
            }
        }

        return false;

    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return __('The token is incorrect');
    }
}

