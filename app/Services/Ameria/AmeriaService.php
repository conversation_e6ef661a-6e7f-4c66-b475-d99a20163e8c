<?php

namespace App\Services\Ameria;

use App\Services\Lanbilling\LanbillingService;
use App\Traits\ApiResponserTrait;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use TypiCMS\Modules\Orders\Models\Order;
use TypiCMS\Modules\Orders\Models\Orderitem;
use TypiCMS\Modules\Payments\Models\Ameria;

class AmeriaService
{
    use ApiResponserTrait;
    protected $testMode = true;
    protected $testOrderId = 2471001; //2471001 - 2472000

    /**
     * get payment url
     * @param $orderId
     * @param $recUserId
     * @param $lang
     *
     * @return mixed|string
     */

    //random order id
    protected function getTestOrderId()
    {
        $order_id = Ameria::select('ameria_order_id')->orderBy('id','desc')->first();
        $order_list = [2471143,2471227,2471443,2471419,2471547,2471968,2471911,2471602,2471532,2471595,2471071,2471913,2471965,2471960,2471556,2471556,2471750,2471750,2471870,2471011,2471011,2471013,2471462,2471679,2471983,2471806,2471017,2471018,2471019,2471021,2471022,2471023,2471025,2471471,2471479,2471506,2471507,2471029,2471030];
        if(!$order_id){
            $ameria_order_id = 2471250;
            $i = $ameria_order_id;
        }else{
            $i = $order_id->ameria_order_id + 1;
        }
        for( ; $i <= 2472000 ; $i++){
            if(!in_array($i,$order_list)){
                return $i ;
            }
        }
        //app()['config']->set('ameria.TestOrderId', $i+1);
    }

    /**
     * Redirection to payment page
     * @param $orderId
     * @param $recUserId
     * @param $lang
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector|mixed
     */
    public function createFormAndRedirect($orderId, $recUserId, $lang,$recurring = 0)
    {
        $paymentData = $this->getPaymentData($orderId,1);
        /**
         * @var int    $amount
         * @var object $order
         * @var int    $orderId
         * @var int    $ameriaOrderId
         * @var string $backUrl
         * @var string $productsDescription
         * @var string $clientId
         * @var string $username
         * @var string $password
         * @var string $customerId
         * @var string $recurring
         * @var boolean $save_card
         */
        extract($paymentData);

        try {
            $ameria = AmeriaPurchase::get($clientId, $username, $password)
                ->setAmount($amount)
                ->setBackUrl($backUrl)
                ->setOrderId($ameriaOrderId)
                ->setLocale($lang)
                ->setCurrency('051')
                ->setTestMode($this->testMode)
                ->setRecurring($recurring)
                ->setDescription("Payment Fnet {$orderId}")
                ->getForm()->getPaymentIdAndLink();
        } catch (\Exception $e) {
            Log::channel('ameria')->info(__METHOD__, [
                'errorMessage' => $e->getMessage(),
                'error' => $e->getTraceAsString()
            ]);
            throw new \Exception(__("Ameria create payment error"));
        }

        if (isset($ameria['paymentId'])) {
            $newPayment = Ameria::where('order_id',$orderId)
                ->update([
                'payment_id'      => $ameria['paymentId'],
                'amount'          => $amount,
                'back_url'        => $backUrl,
                'customer_id'     => $customerId,
            ]);

            Log::channel('ameria')->info(' CREATEFormAndRedirect # # # #  ', [
                'ameria response ' => $ameria
            ]);
            if ($newPayment) {
                return response()->json([ 'success' => true,'redirectUrl'=>$ameria['redirectUrl']], 200);
            }
        }
    }

    public function createRecurringPay($orderID,$card_holder_id,$lang){
        $paymentData = $this->getPaymentData($orderID,1);
        /**
         * @var int    $amount
         * @var object $order
         * @var int    $orderId
         * @var int    $ameriaOrderId
         * @var string $backUrl
         * @var string $productsDescription
         * @var string $clientId
         * @var string $username
         * @var string $password
         * @var string $customerId
         * @var string $recurring
         * @var boolean $save_card
         */
        extract($paymentData);
        try {
            $bindinding_payment = AmeriaPurchase::get($clientId, $username, $password)
                ->setAmount($amount)
                ->setBackUrl($backUrl)
                ->setOrderId($ameriaOrderId)
                ->setLocale($lang)
                ->setCurrency('051')
                ->setTestMode($this->testMode)
                ->setRecurring($recurring)
                ->setCardHolderId($card_holder_id)
                ->setDescription("Payment Fnet {$orderId}")
                ->getForm()->getBindingPayment();
        } catch (\Exception $e) {
            Log::channel('ameria')->info(__METHOD__, [
                'errorMessage' => $e->getMessage(),
                'error' => $e->getTraceAsString()
            ]);
            throw new \Exception(__("Recurring Pay error"));
        }

        if (isset($bindinding_payment['PaymentID'])) {
            $newPayment = Ameria::where('order_id',$orderID)
                ->update([
                'payment_id'      => $bindinding_payment['PaymentID'],
                'ameria_order_id' => $ameriaOrderId,
                'order_id'        => $orderId,
                'amount'          => $amount,
                'back_url'        => $backUrl,
                'customer_id'     => $customerId,
                'response_code'   => $bindinding_payment['ResponseCode'],
                'card_number'     => $bindinding_payment['CardNumber'],
                'client_name'     => $bindinding_payment['ClientName'],
                'order_status'    => $bindinding_payment['OrderStatus'],
                'card_holder_id'  => $bindinding_payment['CardHolderID'],
                'binding_id'      => $bindinding_payment['BindingID'],
                'exp_date'        => $bindinding_payment['ExpDate'],
                'data'            => json_encode($bindinding_payment),
            ]);
            Log::channel('ameria')->info(' Create BINDING Payment response # # # #  ', [
                'binding response ' => $bindinding_payment
            ]);
            if($bindinding_payment['ResponseCode'] == '00' && $newPayment){
                if($bindinding_payment['OrderStatus'] == 1){
                    $confirmPayment = AmeriaPurchase::get($clientId, $username, $password)->setTestMode($this->testMode)->getForm()
                        ->confirmPayment($bindinding_payment['PaymentID'],$amount);
                    if ($confirmPayment['ResponseCode'] == '00') {
                        /*$paymentDetailsAfterConfirm = AmeriaPurchase::get($clientId, $username, $password)->setTestMode($this->testMode)->getForm()
                            ->getPaymentDetails($paymentId);*/
                        $response['status'] = 'success';
                        $order->update(['payment_status' => 3, 'billing_status' => 1]);
                        return response()->json([ 'success' => true,'redirect'=>$order->success_url], 200);
                        //return redirect($order->success_url);
                    }else{
                        $response['status']             = 'Fail';
                        $response['message']            = 'Empty payment details';
                        $order->update(['payment_status' => 5, 'billing_status' => 5]);
                        return response()->json([ 'success' => true,'redirect'=>$order->fail_url], 200);
                        //return redirect($order->fail_url);
                    }
                }
                if($bindinding_payment['OrderStatus'] == 2){
                    $order->update(['payment_status' => 3, 'billing_status' => 1]);
                    return response()->json([ 'success' => true,'redirect'=>$order->success_url], 200);
                }

            }
            return response()->json([ 'success' => true,'redirect'=>$order->fail_url], 200);
        }
    }

    /**
     * BackUrl process
     * @param   Request  $request
     *
     * @return array
     */
    public function redirect($request)
    {
        $respOrderId = $request->orderID;
        $paymentData = $this->getPaymentData($respOrderId,0, $respOrderId);

        extract($paymentData);
        $ameria = Ameria::where('order_id', $orderId)->orderBy('id', 'desc')->first();

        $response = [];
        if ($request->has('resposneCode') && $request->resposneCode == 00 && isset($ameria)) {
            $paymentId      = $request->paymentID;
            $resposneCode   = $request->resposneCode;

            $paymentDetails = AmeriaPurchase::get($clientId, $username, $password)->setTestMode($this->testMode)->getForm()
                    ->getPaymentDetails($paymentId);

            $ameria->data               = json_encode($paymentDetails);
            $ameria->exp_date           = $paymentDetails['ExpDate'];
            $ameria->card_number        = $paymentDetails['CardNumber'];
            $ameria->response_code      = $resposneCode;
            $ameria->client_name        = $paymentDetails['ClientName'];
            $ameria->payment_type       = $paymentDetails['PaymentType'];
            $ameria->binding_id         = $paymentDetails['BindingID'];
            $ameria->order_status       = $paymentDetails['OrderStatus'];
            $ameria->card_holder_id     = $paymentDetails['CardHolderID'];
            $ameria->save();

            if ($paymentDetails['ResponseCode'] == 00) {
                if($paymentDetails['OrderStatus'] == 1){
                    if($save_card != 1){
                        $confirmPayment = AmeriaPurchase::get($clientId, $username, $password)->setTestMode($this->testMode)->getForm()
                            ->confirmPayment($paymentId,$amount);
                        if ($confirmPayment['ResponseCode'] == 00) {
                            $paymentDetailsAfterConfirm = AmeriaPurchase::get($clientId, $username, $password)->setTestMode($this->testMode)->getForm()
                                ->getPaymentDetails($paymentId);
                            $ameria->order_status       = $paymentDetailsAfterConfirm['OrderStatus'];
                            $ameria->save();
                            $response['status']         = 'success';
                            $order->update(['payment_status' => 3, 'billing_status' => 3]);
                            //$orderItem = Orderitem::where('order_id',$order->id)->update(['payment_status' => 3, 'billing_status' => 3]);
                            $orderItems = Orderitem::where('order_id',$order->id)->get();
                            foreach ($orderItems as $item){
                                $payment = $this->lanbillingPayment($item->contract->contract_number,$item->price,$item->order_id);
                                $item->billing_status = 5;
                                $item->payment_status = 3;
                                if($payment && count((array)$payment) > 0){
                                    if($payment->ret){
                                        $item->billing_answer = $payment->ret;
                                        $item->billing_status = 3;
                                    }
                                }
                                $item->save();
                            }
                        }else{
                            $response['status']             = 'Fail';
                            $response['message']            = 'Empty payment details';
                            $order->update(['payment_status' => 5, 'billing_status' => 5]);
                            $orderItem = Orderitem::where('order_id',$order->id)->update(['payment_status' => 5, 'billing_status' => 5]);
                        }
                    }else {
                        $cancelPayment = AmeriaPurchase::get($clientId, $username, $password)->setTestMode($this->testMode)->getForm()
                            ->cancelPayment($paymentId);
                        if ($cancelPayment['ResponseCode'] == 00) {
                            $paymentDetailsAfterCancel = AmeriaPurchase::get($clientId, $username, $password)->setTestMode($this->testMode)->getForm()
                                ->getPaymentDetails($paymentId);
                            $ameria->order_status       = $paymentDetailsAfterCancel['OrderStatus'];
                            $ameria->save();
                            $response['status'] = 'success';
                            $order->update(['payment_status' => 2, 'billing_status' => 2]);
                        }else{
                            $response['status']     = 'Fail';
                            $response['message']    = 'Error in cancel payment';
                            $order->update(['payment_status' => 5, 'billing_status' => 5]);
                        }
                    }
                }elseif($paymentDetails['OrderStatus'] == 2){
                    if($save_card != 1) {
                        $response['status'] = 'success';
                        $order->update(['payment_status' => 3, 'billing_status' => 3]);
                        //$orderItem = Orderitem::where('order_id',$order->id)->update(['payment_status' => 3, 'billing_status' => 3]);
                        $orderItems = Orderitem::where('order_id',$order->id)->update(['payment_status' => 3, 'billing_status' => 3]);
                        foreach ($orderItems as $item){
                            $payment = $this->lanbillingPayment($item->contract->contract_number,$item->price,$item->order_id);
                            $item->billing_status = 5;
                            $item->payment_status = 3;
                            if($payment && count((array)$payment) > 0){
                                if($payment->ret){
                                    $item->billing_answer = $payment->ret;
                                    $item->billing_status = 3;
                                }
                            }
                            $item->save();
                        }
                    }else{
                        $cancelPayment = AmeriaPurchase::get($clientId, $username, $password)->setTestMode($this->testMode)->getForm()
                            ->cancelPayment($paymentId);
                        if ($cancelPayment['ResponseCode'] == 00) {
                            $paymentDetailsAfterCancel = AmeriaPurchase::get($clientId, $username, $password)->setTestMode($this->testMode)->getForm()
                                ->getPaymentDetails($paymentId);
                            $ameria->order_status       = $paymentDetailsAfterCancel['OrderStatus'];
                            $ameria->save();

                            $response['status'] = 'success';
                            $response['canceled'] = 1;
                            $order->update(['payment_status' => 2, 'billing_status' => 2]);
                        }else{
                            $response['status']     = 'Fail';
                            $response['message']    = 'Error in cancel payment';
                            $order->update(['payment_status' => 5, 'billing_status' => 5]);
                        }
                    }
                }
            } else {
                $response['status']     = 'fail';
                $response['message']    = 'Empty payment details';
                $order->update(['payment_status' => 5, 'billing_status' => 5]);
            }
        } else {
            $response['status']     = 'Fail';
            $response['message']    = 'Error';
            $response['response']   = 'wrong redirect';
            $order->update(['payment_status' => 5, 'billing_status' => 5]);
        }
        $response['order'] = $order;
        Log::channel('ameria')->info($orderId . ' ' . $response['status'], [
            'response' => $response
        ]);

        return $response;
    }

    public function confirmPayment($orderId)
    {
        $paymentData = $this->getPaymentData($orderId);

        extract($paymentData);

        $ameria = Ameria::where('order_id', $orderId)->orderBy('id', 'desc')->first();

        if (!isset($ameria) || !isset($ameria->payment_id)) {
            return $response['ResponseCode'] = 404;
        }

        $confirmPayment = AmeriaPurchase::get($clientId, $username, $password)
            ->setTestMode($this->testMode)
            ->getForm()
            ->confirmPayment($ameria->payment_id, $amount);

        Log::channel('ameria')->info($orderId . ' Confirm payment', [
            'response' => $confirmPayment
        ]);

        if (isset($confirmPayment) && isset($confirmPayment['ResponseCode']) && $confirmPayment['ResponseCode'] == 00) {
            $order->update([
                'payment_status' => 3, 'paid_status' => 3, 'order_status' => 3,
                'payment_message' => 'Confirmed successfully'
            ]);
        }

        return $confirmPayment;
    }

    protected function getPaymentData($orderId,$create = 0, $testOrderId = null)
    {
        if (isset($testOrderId) && $this->testMode) {
            $ameria = Ameria::where('ameria_order_id', $orderId)->first();
            $order = Order::where('id', $ameria->order_id)->firstOrFail();
            $orderId = $order->id;
        } else {
            $order = Order::whereId($orderId)->firstOrFail();
        }
        $amount = $order->total_price;
        $customerId = $order->customer_id;
        $recurring = $order->is_recurring;
        $save_card = $order->save_card;
        if (intval(ceil($amount)) === 0) {
            abort(422, 'Wrong amount');
        }
        $url = config('app.url');
        //redirect url
        $redirect = $url . '/api/v1/ameria-redirect';
        $clientId = config('ameria.ClientID');
        $username = config('ameria.Username');
        $password = config('ameria.Password');
        $data = [
            'amount' => $amount,
            'order' => $order,
            'ameriaOrderId' => $orderId,
            'orderId' => $orderId,
            'backUrl' => $redirect,
            'productsDescription' => $order->products,
            'clientId' => $clientId,
            'username' => $username,
            'password' => $password,
            'customerId' => $customerId,
            'recurring' => $recurring,
            'save_card' => $save_card,
        ];
        if ($this->testMode) {
            $clientId = config('ameria.Test_ClientID');
            $username = config('ameria.Test_Username');
            $password = config('ameria.Test_Password');
            $data = [
                'amount' => 10,
                'ameriaOrderId' => $this->getTestOrderId(), //2471001 - 2472000
                'clientId' => $clientId,
                'username' => $username,
                'password' => $password,
                'order' => $order,
                'orderId' => $orderId,
                'backUrl' => $redirect,
                'productsDescription' => $order->products,
                'customerId' => $customerId,
                'recurring' => $recurring,
                'save_card' => $save_card,
            ];
        }
        if ($create == 1)
        {
            $newPayment = Ameria::create([
                'ameria_order_id' => $data['ameriaOrderId'],
                'order_id' => $data['orderId'],
                'customer_id' => $data['customerId'],
            ]);
            Log::channel('ameria')->info(' getPaymentData Create Ameria order', [
                'newPayment' => $newPayment
            ]);
        }

        return $data;
    }

    /**
     * Reversal
     * @param $orderId
     *
     * @return bool|mixed|string
     */
    public function cancelpayment($orderId)
    {
        $paymentData = $this->getPaymentData($orderId);
        /**
         * @var int    $amount
         * @var object $order
         * @var int    $ameriaOrderId
         * @var int    $orderId
         * @var string $backUrl
         * @var string $productsDescription
         * @var string $clientId
         * @var string $username
         * @var string $password
         */
        extract($paymentData);

        $ameria = Ameria::where('order_id', $orderId)->first();

        if (null !== $ameria) {
            $cancelPayment = AmeriaPurchase::get('', $username, $password)
                ->setTestMode($this->testMode)
                ->getForm()
                ->cancelPayment($ameria->payment_id);

            Log::channel('ameria')->info($orderId . ' REVERSAL', [
                'cancelPayment' => $cancelPayment
            ]);

            if (isset($cancelPayment['ResponseCode']) && $cancelPayment['ResponseCode'] == 00) {
                $ameria->refunded = '1';
                $ameria->refund   = '1';
                $ameria->save();

                $order->paid_status    = 7;
                $order->payment_status = 7;
                $order->save();
            } else {
                $ameria->refunded = '-1';
            }

            $ameria->reversal = '1';
            $ameria->refund_message = $cancelPayment['ResponseMessage'] ?? '';
            $ameria->save();
            return $cancelPayment;
        }

        return false;
    }

    /**
     * Reversal
     * @param $card_holder_id
     *
     * @return bool|mixed|string
     */
    public function cancelBinding($card_holder_id)
    {
        $clientId = config('ameria.ClientID');
        $username = config('ameria.Username');
        $password = config('ameria.Password');

        if (null !== $card_holder_id) {
            $cancelBinding = AmeriaPurchase::get($clientId, $username, $password)
                ->setTestMode($this->testMode)
                ->getForm()
                ->deactivateBinding($card_holder_id);

            Log::channel('ameria')->info('Ameria cancelBinding # # # ', [
                'cancelBinding' => $cancelBinding
            ]);

            if (isset($cancelBinding['ResponseCode']) && $cancelBinding['ResponseCode'] == 00) {
                return $cancelBinding;
            } elseif($cancelBinding['ResponseCode']) {
                return $cancelBinding;
            }

            return false;
        }

        return false;
    }

    /**
     * Refund
     * @param         $orderId
     * @param   null  $amount
     *
     * @return bool|mixed|string
     */
    public function refund($orderId, $refundAmount = null)
    {
        $paymentData = $this->getPaymentData($orderId);
        /**
         * @var int    $amount
         * @var object $order
         * @var int    $ameriaOrderId
         * @var int    $orderId
         * @var string $backUrl
         * @var string $productsDescription
         * @var string $clientId
         * @var string $username
         * @var string $password
         */
        extract($paymentData);

        $amount = $refundAmount ?? $amount;

        $ameria = Ameria::where('order_id', $orderId)->first();

        if (null !== $ameria) {
            $refund = AmeriaPurchase::get('', $username, $password)
                ->setTestMode($this->testMode)
                ->getForm()
                ->refund($ameria->payment_id, $amount);

            Log::channel('ameria')->info($orderId . ' REFUND', [
                'refund' => $refund
            ]);

            if (isset($refund['ResponseCode']) && $refund['ResponseCode'] == 00) {
                $ameria->refunded = '1';
                $ameria->refund   = '1';
                $ameria->save();

                $order->paid_status    = 6;
                $order->payment_status = 6;
                $order->save();
            } else {
                $ameria->refunded = '-1';
            }

            $ameria->reversal = '0';
            $ameria->refund_message = $refund['ResponseMessage'] ?? '';
            $ameria->save();
            return $refund;
        }

        return false;
    }

    public function charge($orderId, $recUserId)
    {
        // TODO: Implement charge() method.
    }



    public function createAutoPayment($orderID,$card_holder_id,$lang){
        $paymentData = $this->getPaymentData($orderID,1);
        /**
         * @var int    $amount
         * @var object $order
         * @var int    $orderId
         * @var int    $ameriaOrderId
         * @var string $backUrl
         * @var string $productsDescription
         * @var string $clientId
         * @var string $username
         * @var string $password
         * @var string $customerId
         * @var string $recurring
         * @var boolean $save_card
         */
        extract($paymentData);
        try {
            $bindinding_payment = AmeriaPurchase::get($clientId, $username, $password)
                ->setAmount($amount)
                ->setBackUrl($backUrl)
                ->setOrderId($ameriaOrderId)
                ->setLocale($lang)
                ->setCurrency('051')
                ->setTestMode($this->testMode)
                ->setRecurring($recurring)
                ->setCardHolderId($card_holder_id)
                ->setDescription("Payment Fnet {$orderId}")
                ->getForm()->getBindingPayment();
        } catch (\Exception $e) {
            Log::channel('ameria')->info(__METHOD__, [
                'errorMessage' => $e->getMessage(),
                'error' => $e->getTraceAsString()
            ]);
            throw new \Exception(__("createAutoPayment error"));
        }

        if (isset($bindinding_payment['PaymentID'])) {
            $newPayment = Ameria::where('order_id',$orderID)
                ->update([
                    'payment_id'      => $bindinding_payment['PaymentID'],
                    'ameria_order_id' => $ameriaOrderId,
                    'order_id'        => $orderId,
                    'amount'          => $amount,
                    'back_url'        => $backUrl,
                    'customer_id'     => $customerId,
                    'response_code'   => $bindinding_payment['ResponseCode'],
                    'card_number'     => $bindinding_payment['CardNumber'],
                    'client_name'     => $bindinding_payment['ClientName'],
                    'order_status'    => $bindinding_payment['OrderStatus'],
                    'card_holder_id'  => $bindinding_payment['CardHolderID'],
                    'binding_id'      => $bindinding_payment['BindingID'],
                    'exp_date'        => $bindinding_payment['ExpDate'],
                    'data'            => json_encode($bindinding_payment),
                ]);
            Log::channel('ameria')->info(' Create Auto Payment Payment response # # # #  ', [
                'binding response ' => $bindinding_payment
            ]);
            if($bindinding_payment['ResponseCode'] == '00' && $newPayment){
                if($bindinding_payment['OrderStatus'] == 1){
                    $confirmPayment = AmeriaPurchase::get($clientId, $username, $password)->setTestMode($this->testMode)->getForm()
                        ->confirmPayment($bindinding_payment['PaymentID'],$amount);
                    if ($confirmPayment['ResponseCode'] == '00') {
                        $response['status']     = 'success';
                        $response['order_id']   = $order->id;
                        $response['message']    = 'Order auto payment is finished successfully';
                        $order->update(['payment_status' => 3, 'billing_status' => 3]);

                        $orderItems = Orderitem::where('order_id',$order->id)->get();
                        foreach ($orderItems as $item){
                            $payment = $this->lanbillingPayment($item->contract->contract_number,$item->price,$item->order_id);
                            $item->billing_status = 5;
                            $item->payment_status = 3;
                            if($payment && count((array)$payment) > 0){
                                if($payment->ret){
                                    $item->billing_answer = $payment->ret;
                                    $item->billing_status = 3;
                                }
                            }
                            $item->save();
                        }

                        return $response;
                    }else{
                        $response['status']     = 'Fail';
                        $response['order_id']   = $order->id;
                        $response['message']    = 'Order auto payment is finished fail';
                        $order->update(['payment_status' => 5, 'billing_status' => 5]);
                        return $response;
                    }
                }
                if($bindinding_payment['OrderStatus'] == 2){
                    $order->update(['payment_status' => 3, 'billing_status' => 3]);
                    $orderItems = Orderitem::where('order_id',$order->id)->get();
                    foreach ($orderItems as $item){
                        $payment = $this->lanbillingPayment($item->contract->contract_number,$item->price,$item->order_id);
                        $item->billing_status = 5;
                        $item->payment_status = 3;
                        if($payment && count((array)$payment) > 0){
                            if($payment->ret){
                                $item->billing_answer = $payment->ret;
                                $item->billing_status = 3;
                            }
                        }
                        $item->save();
                    }


                    $response['status']     = 'success';
                    $response['order_id']   = $order->id;
                    $response['message']    = 'Order auto payment is finished successfully';
                    return $response;
                }

            }
            $response['status']     = 'Fail';
            $response['order_id']   =  $order->id;
            $response['message']    = 'auto payment order fail';
            return $response;
        }
    }



    public function lanbillingPayment($contract_number,$amount,$receipt){
        $lanBilling = new LanbillingService();
        Log::channel('ameria')->info('lanbillingPayment # # #', [
            'contract_number' => $contract_number,
            'amount' => $amount,
            'receipt' => $receipt
        ]);
        $account = $lanBilling->getAccount($contract_number);
        $res = $lanBilling->Payment($account->ret->agreements->agrmid,$amount,$receipt);
        return $res;
    }
}
