<?php

namespace App\Services;

use App\Repositories\VerifyRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

class VerifyPhone
{

    protected $verifyRepository;
    protected $smsgetway;

    public function __construct(VerifyRepository $verifyRepository , SmsGetWay $smsgetway){
        $this->verifyRepository = $verifyRepository;
        $this->smsgetway = $smsgetway;
    }

    public function sendCode($request){
        try {
            $result = $this->verifyRepository->setVerifyPhone($request);
            //$sms = new SmsGetWay();
            $response = $this->smsgetway->sendCurl($result->contact, $result->code);

            if ($response->successful()) {
                if (Arr::exists($response->json(), 'errorCode')) {
                    throw new \Exception('sms send error');
                } else {
                    $messageid = $response->json()['messageId'];
                    $this->verifyRepository->updateVerifyPhone($result->token, $messageid);
                }
            } else {
                Log::stack(['info'])->info('Sms SendCode Error  ', ['response' => $response]);
                throw new \Exception('Error in Sms send');
            }
            return $result;
        }catch (\Exception $e){
            throw new \Exception('sendCode Throw ' .$e->getMessage());
        }
    }

    public function checkCode($request){


        $result = $this->verifyRepository->checkVerifyPhone($request);

        return $result;
    }
}
