<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SmsGetWay
{
    /**
     * @param $phone
     *
     * @return bool
     */
    protected function checkPhoneNumber($phone)
    {
        $swissNumberStr = $phone;
        $phoneUtil      = \libphonenumber\PhoneNumberUtil::getInstance();
        try {
            $swissNumberProto = $phoneUtil->parse($swissNumberStr);
        } catch (\libphonenumber\NumberParseException $e) {
            abort(422, 'Invalid phone number');
        }

        $isValid = $phoneUtil->isValidNumber($swissNumberProto);

        return $isValid;
    }

    /**
     * @param $id
     * @param $number
     * @param $message
     *
     * @return bool|\Psr\Http\Message\StreamInterface
     * @throws Exception
     */
    public function sendSmsToClient($number, $message)
    {
        $response = $this->sendCurl($number, $message);
        return $response;
    }

    /**
     * @param $number
     * @param $message
     *
     * @return bool|string
     * @throws Exception
     */
    public function sendCurl($number, $message)
    {
        try {
            $url = config('typicms.BEELINE_SEND_URL');//env('BEELINE_SEND_URL');
            $user = config('typicms.BEELINE_LOGIN');//env('BEELINE_LOGIN');
            $pass = config('typicms.BEELINE_PASS');//env('BEELINE_PASS');
            $smsFrom = config('typicms.BEELINE_SMS_FROM');//env('BEELINE_SMS_FROM');
            $smstext = "Your one-time code for authorization is: " . $message;

            $body = [
                'destAddr' => trim($number, '+'),
                'sourceAddr' => $smsFrom,
                'message' => $smstext
            ];

            $response = Http::retry(3, 500)->withoutVerifying()
                ->withBasicAuth($user, $pass)
                ->get($url, $body);

            Log::stack(['info'])->info('BEELINE_SMS info: ', ['response' => $response->body()]);
            if ($response->serverError() || $response->clientError()) {
                throw new \Exception("Http Error # : " . $response->body());
            }
            return $response;
        }catch(\Exception $e){
            throw new \Exception("sendCurl Throw ".$e->getMessage());
        }
    }


}
