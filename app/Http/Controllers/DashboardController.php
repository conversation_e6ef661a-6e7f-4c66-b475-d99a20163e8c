<?php

namespace App\Http\Controllers;

use Exception;
use GuzzleHttp\Client;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use TypiCMS\Modules\Clients\Services\ClientService;
use TypiCMS\Modules\Core\Http\Controllers\BaseAdminController;
use TypiCMS\Modules\Core\Http\Controllers\DashboardAdminController;

class DashboardController extends BaseAdminController
{
    public function index(): RedirectResponse
    {
        return redirect(route('admin::dashboard'));
    }

    public function dashboard(Client $client): View
    {
        $welcomeMessage = config('typicms.welcome_message');
        $url = config('typicms.welcome_message_url');

        $clientsService = new ClientService();

        //CHARTS
        $clientsChart     = $this->getClientsForChart($clientsService);
        $successClientsChart    = $this->getSucessClientsForChart($clientsService);
        $formtypesByCount       = $this->getFormtypesByCount($clientsService);

        //$orderSalesChart = $this->getOrderSalesForChart($ordersService);
        //INFO
        //$clients     = $this->getClients();
        //$topProducts = $this->getTopProducts($ordersService);


        try {
            $response = $client->get($url, ['timeout' => 2]);
            if ($response->getStatusCode() < 400) {
                $welcomeMessage = $response->getBody();
            }
        } catch (Exception $exception) {
            info($exception->getMessage());
        }

        return view('dashboard::show')->with(compact('welcomeMessage', 'clientsChart','successClientsChart','formtypesByCount'));
    }




    protected function getClientsForChart($clientsService)
    {
        $clients = $clientsService->getDateClients();
        $totalClients = [];
        $clients->each(function ($item) use (&$totalClients) {
            $totalClients[$item->date] = $item->clients;
        });
        return $totalClients;
    }

    protected function getSucessClientsForChart($clientsService)
    {
        $clients = $clientsService->getDateSuccessClients();
        $totalClients = [];
        $clients->each(function ($item) use (&$totalClients) {
            $totalClients[$item->date] = $item->clients;
        });
        return $totalClients;
    }
    protected function getFormtypesByCount($clientsService)
    {
        $clients = $clientsService->getFormtypesByCount();
        $totalClients = [];
        $clients->each(function ($item) use (&$totalClients) {
            $key = $item->formtype;
            if($item->formtype == '1'){
                $key = __('APPLICATION PROTEST');
            }elseif($item->formtype == '2'){
                $key = __('OBTAINING INFORMATION / CONSENT');
            }elseif($item->formtype == '3'){
                $key = __('OTHER');
            }elseif($item->formtype == 'business'){
                $key = __("business");
            }elseif($item->formtype == 'agricultural'){
                $key = __('agricultural');
            }

            $totalClients[$key] = $item->clients;
        });
        return $totalClients;
    }

    protected function getClients()
    {
       /* $orders = \TypiCMS\Modules\Clients\Models\Client::select('id', 'first_name', 'last_name', 'email')
            ->orderBy('created_at', 'desc')
            ->get();

        return $orders;*/
    }



}
