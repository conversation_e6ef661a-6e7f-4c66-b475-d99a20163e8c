# ApplicationApiController Documentation

## Overview

The `ApplicationApiController` is a key component of the Clients module in the GForm application. It provides API endpoints for handling application forms, client registration, and tracking the status of submissions through the Mulberry service.

## Location

`Modules/Clients/Http/Controllers/ApplicationApiController.php`

## Class Inheritance

The controller extends `BaseApiController` from the Core module, which provides common functionality for API controllers.

## Dependencies

- `DateTime` - For date/time handling
- `Eluceo\iCal\Domain\ValueObject\Timestamp` - For iCal timestamp handling
- `Illuminate\Support\Str` - For string manipulation and UUID generation
- `Illuminate\Support\Facades\Auth` - For authentication
- `Illuminate\Support\Facades\Log` - For logging
- `TypiCMS\Modules\Clients\Models\Client` - Client model
- `TypiCMS\Modules\Core\Models\User` - User model
- `TypiCMS\Modules\Core\Facades\Menus` - For menu handling
- `TypiCMS\Modules\Core\Facades\Menulinks` - For menu links handling
- `TypiCMS\Modules\Clients\Http\Requests\CashMeRequest` - Request validation for CashMe integration

## Methods

### 1. `formList()`

Retrieves a list of available forms from the application menu.

**Process:**
1. Gets the current locale
2. Retrieves the application menu ('_app_menu')
3. Processes each menu link and its children
4. Formats the data into a structured response

**Returns:**
- An array of form categories and types with their properties

### 2. `formListOld()`

A legacy method that returns a hardcoded list of form types.

**Returns:**
- An array of predefined form categories and types

### 3. `register(CashMeRequest $cashMeRequest)`

Registers a new client application.

**Parameters:**
- `$cashMeRequest` - Validated request data from CashMeRequest

**Process:**
1. Validates the incoming request data
2. Checks if the form type is 'business' or 'agricultural'
3. For other form types:
   - Sets the status to "SUCCESS"
   - Generates a UUID
   - Creates a new Client record with the form data
   - Generates a webhook URL with the client UUID
4. For 'business' or 'agricultural' form types:
   - Generates a webhook URL with the form type

**Returns:**
- An array containing the webhook URL

### 4. `mulberryStatus($tracking)`

Retrieves the status of a submission from the Mulberry service.

**Parameters:**
- `$tracking` - Tracking ID for the submission

**Process:**
1. Finds the client record with the given tracking ID
2. If found, prepares a request to the Mulberry tracking API
3. Sends the request using cURL
4. Processes the response:
   - Extracts status information
   - Maps raw status to application-specific status using a configuration map
   - Formats date and time
   - Extracts title information
5. Returns the formatted status information

**Returns:**
- JSON response with status information if successful
- Error response with appropriate HTTP status code if unsuccessful

## Key Features

### 1. Dynamic Form List

The `formList()` method dynamically generates a list of available forms based on the application menu. This allows administrators to modify the available forms through the menu system without changing the code.

### 2. Client Registration

The `register()` method handles client registration for different form types. It supports both standard forms and specialized forms like 'business' and 'agricultural'.

### 3. Mulberry Integration

The `mulberryStatus()` method provides integration with the Mulberry service for tracking the status of submissions. It includes:

- Error handling for API requests
- Comprehensive logging
- Status mapping from Mulberry to application-specific statuses
- Localization support

## Error Handling

The controller includes comprehensive error handling:

1. **Request Validation:**
   - Uses Laravel's form request validation (CashMeRequest)
   - Ensures all required fields are present and valid

2. **API Error Handling:**
   - Catches exceptions in the Mulberry API request
   - Returns appropriate HTTP status codes (400, 404, 500)
   - Logs detailed error information

3. **Response Formatting:**
   - Consistently formats responses as JSON
   - Includes status and message fields for error responses

## Logging

The controller includes detailed logging for the Mulberry integration:

1. **Request Logging:**
   - Logs the request data sent to the Mulberry API
   - Includes the tracking ID and request URL

2. **Response Logging:**
   - Logs the response from the Mulberry API
   - Includes HTTP status code and any cURL errors

3. **Error Logging:**
   - Logs detailed error information for exceptions
   - Includes stack traces for debugging

## Integration with Other Components

### Integration with Client Model

The controller uses the Client model to:
1. Create new client records during registration
2. Find client records by tracking ID for status checks

### Integration with Menus

The controller uses the Menus facade to:
1. Retrieve the application menu for generating the form list
2. Process menu links and their properties

## Configuration

The controller uses several configuration parameters:

1. **Status Mapping:**
   - `config('typicms.modules.clients.statusmap')` - Maps Mulberry status codes to application-specific statuses

2. **Mulberry API URL:**
   - `config('typicms.mulberry_track_url')` or `config('typicms.mulberry_track')` - URL for the Mulberry tracking API

## Usage Examples

### Example 1: Getting the Form List

```javascript
// JavaScript example
fetch('/api/application/forms')
  .then(response => response.json())
  .then(data => {
    // Process the form list
    console.log(data);
  });
```

### Example 2: Registering a Client

```javascript
// JavaScript example
const formData = {
  formtype: 'CONSUMER-LOAN',
  data: {
    first_name: 'John',
    last_name: 'Doe',
    email: '<EMAIL>',
    phone: '+37495123456',
    // Other client data
  }
};

fetch('/api/application/register', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  body: JSON.stringify(formData)
})
  .then(response => response.json())
  .then(data => {
    // Process the response (contains webhook URL)
    console.log(data.url);
  });
```

### Example 3: Checking Submission Status

```javascript
// JavaScript example
const trackingId = '123456789';

fetch(`/api/application/status/${trackingId}`)
  .then(response => response.json())
  .then(data => {
    if (data.status === 'success') {
      // Process the status information
      console.log(data.data);
    } else {
      // Handle error
      console.error(data.message);
    }
  });
```

## Security Considerations

1. **Input Validation:**
   - Uses Laravel's form request validation (CashMeRequest)
   - Ensures all input is properly validated before processing

2. **Error Handling:**
   - Does not expose sensitive information in error responses
   - Uses appropriate HTTP status codes

3. **Logging:**
   - Logs detailed information for debugging and auditing
   - Avoids logging sensitive information
