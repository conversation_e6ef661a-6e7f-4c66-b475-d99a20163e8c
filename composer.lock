{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "981b04d1edbbf1a425f4077f81d2ace0", "packages": [{"name": "barryvdh/laravel-dompdf", "version": "v2.0.0", "source": {"type": "git", "url": "https://github.com/barryvdh/laravel-dompdf.git", "reference": "1d47648c6cef37f715ecb8bcc5f5a656ad372e27"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/barryvdh/laravel-dompdf/zipball/1d47648c6cef37f715ecb8bcc5f5a656ad372e27", "reference": "1d47648c6cef37f715ecb8bcc5f5a656ad372e27", "shasum": ""}, "require": {"dompdf/dompdf": "^2", "illuminate/support": "^6|^7|^8|^9", "php": "^7.2 || ^8.0"}, "require-dev": {"nunomaduro/larastan": "^1|^2", "orchestra/testbench": "^4|^5|^6|^7", "phpro/grumphp": "^1", "squizlabs/php_codesniffer": "^3.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}, "laravel": {"providers": ["Barryvdh\\DomPDF\\ServiceProvider"], "aliases": {"Pdf": "Barryvdh\\DomPDF\\Facade\\Pdf", "PDF": "Barryvdh\\DomPDF\\Facade\\Pdf"}}}, "autoload": {"psr-4": {"Barryvdh\\DomPDF\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Barry vd. Heuvel", "email": "<EMAIL>"}], "description": "A DOMPDF Wrapper for Laravel", "keywords": ["dompdf", "laravel", "pdf"], "support": {"issues": "https://github.com/barryvdh/laravel-dompdf/issues", "source": "https://github.com/barryvdh/laravel-dompdf/tree/v2.0.0"}, "funding": [{"url": "https://fruitcake.nl", "type": "custom"}, {"url": "https://github.com/barryvdh", "type": "github"}], "time": "2022-07-06T11:12:10+00:00"}, {"name": "bkwld/croppa", "version": "6.0.1", "source": {"type": "git", "url": "https://github.com/BKWLD/croppa.git", "reference": "1dc063c3684019eeaa02b2175d43c0d997659b84"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/BKWLD/croppa/zipball/1dc063c3684019eeaa02b2175d43c0d997659b84", "reference": "1dc063c3684019eeaa02b2175d43c0d997659b84", "shasum": ""}, "require": {"ext-gd": "*", "illuminate/console": "^9.0", "illuminate/routing": "^9.0", "illuminate/support": "^9.0", "intervention/image": "^2.7", "league/flysystem": "^3.0", "php": "^8.0.2", "symfony/http-foundation": "^6.0", "symfony/http-kernel": "^6.0"}, "require-dev": {"mockery/mockery": "^1.4.4", "nunomaduro/larastan": "^2.0", "orchestra/testbench": "^7.5", "phpunit/phpunit": "^9.5.10"}, "suggest": {"ext-exif": "Required if you want to have Croppa auto-rotate images from devices like mobile phones based on exif meta data."}, "type": "library", "extra": {"laravel": {"providers": ["Bkwld\\Croppa\\CroppaServiceProvider"], "aliases": {"Croppa": "Bkwld\\Croppa\\Facades\\Croppa"}}}, "autoload": {"psr-4": {"Bkwld\\Croppa\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "description": "Image thumbnail creation through specially formatted URLs for Laravel", "homepage": "https://github.com/bkwld/croppa", "keywords": ["image", "laravel", "resize", "thumb"], "support": {"issues": "https://github.com/BKWLD/croppa/issues", "source": "https://github.com/BKWLD/croppa/tree/6.0.1"}, "time": "2022-05-30T09:25:12+00:00"}, {"name": "brick/math", "version": "0.10.2", "source": {"type": "git", "url": "https://github.com/brick/math.git", "reference": "459f2781e1a08d52ee56b0b1444086e038561e3f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brick/math/zipball/459f2781e1a08d52ee56b0b1444086e038561e3f", "reference": "459f2781e1a08d52ee56b0b1444086e038561e3f", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.4 || ^8.0"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^9.0", "vimeo/psalm": "4.25.0"}, "type": "library", "autoload": {"psr-4": {"Brick\\Math\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Arbitrary-precision arithmetic library", "keywords": ["Arbitrary-precision", "BigInteger", "BigRational", "arithmetic", "bigdecimal", "bignum", "brick", "math"], "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.10.2"}, "funding": [{"url": "https://github.com/BenMorel", "type": "github"}], "time": "2022-08-10T22:54:19+00:00"}, {"name": "dflydev/dot-access-data", "version": "v3.0.1", "source": {"type": "git", "url": "https://github.com/dflydev/dflydev-dot-access-data.git", "reference": "0992cc19268b259a39e86f296da5f0677841f42c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dflydev/dflydev-dot-access-data/zipball/0992cc19268b259a39e86f296da5f0677841f42c", "reference": "0992cc19268b259a39e86f296da5f0677841f42c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.42", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.3", "scrutinizer/ocular": "1.6.0", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^3.14"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Dflydev\\DotAccessData\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Dragonfly Development Inc.", "email": "<EMAIL>", "homepage": "http://dflydev.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://beausimensen.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/cfrutos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com"}], "description": "Given a deep data structure, access data by dot notation.", "homepage": "https://github.com/dflydev/dflydev-dot-access-data", "keywords": ["access", "data", "dot", "notation"], "support": {"issues": "https://github.com/dflydev/dflydev-dot-access-data/issues", "source": "https://github.com/dflydev/dflydev-dot-access-data/tree/v3.0.1"}, "time": "2021-08-13T13:06:58+00:00"}, {"name": "doctrine/inflector", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "8b7ff3e4b7de6b2c84da85637b59fd2880ecaa89"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/8b7ff3e4b7de6b2c84da85637b59fd2880ecaa89", "reference": "8b7ff3e4b7de6b2c84da85637b59fd2880ecaa89", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^8.2", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "vimeo/psalm": "^4.10"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.4"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2021-10-22T20:16:43+00:00"}, {"name": "doctrine/lexer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/c268e882d4dbdd85e36e4ad69e02dc284f89d229", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.11"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/1.2.3"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2022-02-28T11:07:21+00:00"}, {"name": "dompdf/dompdf", "version": "v2.0.0", "source": {"type": "git", "url": "https://github.com/dompdf/dompdf.git", "reference": "79573d8b8a141ec8a17312515de8740eed014fa9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dompdf/dompdf/zipball/79573d8b8a141ec8a17312515de8740eed014fa9", "reference": "79573d8b8a141ec8a17312515de8740eed014fa9", "shasum": ""}, "require": {"ext-dom": "*", "ext-mbstring": "*", "masterminds/html5": "^2.0", "phenx/php-font-lib": "^0.5.4", "phenx/php-svg-lib": "^0.3.3 || ^0.4.0", "php": "^7.1 || ^8.0"}, "require-dev": {"ext-json": "*", "ext-zip": "*", "mockery/mockery": "^1.3", "phpunit/phpunit": "^7.5 || ^8 || ^9", "squizlabs/php_codesniffer": "^3.5"}, "suggest": {"ext-gd": "Needed to process images", "ext-gmagick": "Improves image processing performance", "ext-imagick": "Improves image processing performance", "ext-zlib": "Needed for pdf stream compression"}, "type": "library", "autoload": {"psr-4": {"Dompdf\\": "src/"}, "classmap": ["lib/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "DOMPDF is a CSS 2.1 compliant HTML to PDF converter", "homepage": "https://github.com/dompdf/dompdf", "support": {"issues": "https://github.com/dompdf/dompdf/issues", "source": "https://github.com/dompdf/dompdf/tree/v2.0.0"}, "time": "2022-06-21T21:14:57+00:00"}, {"name": "dragonmantank/cron-expression", "version": "v3.3.1", "source": {"type": "git", "url": "https://github.com/dragonmantank/cron-expression.git", "reference": "be85b3f05b46c39bbc0d95f6c071ddff669510fa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/be85b3f05b46c39bbc0d95f6c071ddff669510fa", "reference": "be85b3f05b46c39bbc0d95f6c071ddff669510fa", "shasum": ""}, "require": {"php": "^7.2|^8.0", "webmozart/assert": "^1.0"}, "replace": {"mtdowling/cron-expression": "^1.0"}, "require-dev": {"phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.0", "phpstan/phpstan-webmozart-assert": "^1.0", "phpunit/phpunit": "^7.0|^8.0|^9.0"}, "type": "library", "autoload": {"psr-4": {"Cron\\": "src/Cron/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/dragonmantank"}], "description": "CRON for PHP: Calculate the next or previous run date and determine if a CRON expression is due", "keywords": ["cron", "schedule"], "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/v3.3.1"}, "funding": [{"url": "https://github.com/dragonmantank", "type": "github"}], "time": "2022-01-18T15:43:28+00:00"}, {"name": "egulias/email-validator", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "f88dcf4b14af14a98ad96b14b2b317969eab6715"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/f88dcf4b14af14a98ad96b14b2b317969eab6715", "reference": "f88dcf4b14af14a98ad96b14b2b317969eab6715", "shasum": ""}, "require": {"doctrine/lexer": "^1.2", "php": ">=7.2", "symfony/polyfill-intl-idn": "^1.15"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^8.5.8|^9.3.3", "vimeo/psalm": "^4"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/3.2.1"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2022-06-18T20:57:19+00:00"}, {"name": "eluceo/ical", "version": "2.7.0", "source": {"type": "git", "url": "https://github.com/markuspoerschke/iCal.git", "reference": "a10295c70529f5da4304bef145efdc7ad3be46cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/markuspoerschke/iCal/zipball/a10295c70529f5da4304bef145efdc7ad3be46cf", "reference": "a10295c70529f5da4304bef145efdc7ad3be46cf", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=7.4 || ~8.0.0 || ~8.1.0", "symfony/deprecation-contracts": "^2.1 || ^3.0"}, "conflict": {"php": "7.4.6"}, "require-dev": {"ergebnis/composer-normalize": "^2.23.1", "friendsofphp/php-cs-fixer": "^3.4", "infection/infection": "^0.23", "phpmd/phpmd": "^2.11.1", "phpunit/phpunit": "^9.5", "vimeo/psalm": "^4.8"}, "type": "library", "autoload": {"psr-4": {"Eluceo\\iCal\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "The eluceo/iCal package offers an abstraction layer for creating iCalendars. You can easily create iCal files by using PHP objects instead of typing your *.ics file by hand. The output will follow RFC 5545 as best as possible.", "homepage": "https://github.com/markuspoerschke/iCal", "keywords": ["calendar", "iCalendar", "ical", "ics", "php calendar"], "support": {"docs": "https://ical.poerschke.nrw", "forum": "https://github.com/markuspoerschke/iCal/discussions", "issues": "https://github.com/markuspoerschke/iCal/issues", "source": "https://github.com/markuspoerschke/iCal"}, "time": "2022-06-21T14:23:15+00:00"}, {"name": "ezyang/htmlpurifier", "version": "v4.14.0", "source": {"type": "git", "url": "https://github.com/ezyang/htmlpurifier.git", "reference": "12ab42bd6e742c70c0a52f7b82477fcd44e64b75"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/12ab42bd6e742c70c0a52f7b82477fcd44e64b75", "reference": "12ab42bd6e742c70c0a52f7b82477fcd44e64b75", "shasum": ""}, "require": {"php": ">=5.2"}, "type": "library", "autoload": {"files": ["library/HTMLPurifier.composer.php"], "psr-0": {"HTMLPurifier": "library/"}, "exclude-from-classmap": ["/library/HTMLPurifier/Language/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "support": {"issues": "https://github.com/ezyang/htmlpurifier/issues", "source": "https://github.com/ezyang/htmlpurifier/tree/v4.14.0"}, "time": "2021-12-25T01:21:49+00:00"}, {"name": "fruitcake/php-cors", "version": "v1.2.0", "source": {"type": "git", "url": "https://github.com/fruitcake/php-cors.git", "reference": "58571acbaa5f9f462c9c77e911700ac66f446d4e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fruitcake/php-cors/zipball/58571acbaa5f9f462c9c77e911700ac66f446d4e", "reference": "58571acbaa5f9f462c9c77e911700ac66f446d4e", "shasum": ""}, "require": {"php": "^7.4|^8.0", "symfony/http-foundation": "^4.4|^5.4|^6"}, "require-dev": {"phpstan/phpstan": "^1.4", "phpunit/phpunit": "^9", "squizlabs/php_codesniffer": "^3.5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.1-dev"}}, "autoload": {"psr-4": {"Fruitcake\\Cors\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Fruitcake", "homepage": "https://fruitcake.nl"}, {"name": "Barryvdh", "email": "<EMAIL>"}], "description": "Cross-origin resource sharing library for the Symfony HttpFoundation", "homepage": "https://github.com/fruitcake/php-cors", "keywords": ["cors", "laravel", "symfony"], "support": {"issues": "https://github.com/fruitcake/php-cors/issues", "source": "https://github.com/fruitcake/php-cors/tree/v1.2.0"}, "funding": [{"url": "https://fruitcake.nl", "type": "custom"}, {"url": "https://github.com/barryvdh", "type": "github"}], "time": "2022-02-20T15:07:15+00:00"}, {"name": "genealabs/laravel-model-caching", "version": "0.12.5", "source": {"type": "git", "url": "https://github.com/GeneaLabs/laravel-model-caching.git", "reference": "3530b50db0849c74d6ab6ac1cf1f96b8ad08ba1a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/GeneaLabs/laravel-model-caching/zipball/3530b50db0849c74d6ab6ac1cf1f96b8ad08ba1a", "reference": "3530b50db0849c74d6ab6ac1cf1f96b8ad08ba1a", "shasum": ""}, "require": {"genealabs/laravel-pivot-events": "^9.0", "illuminate/cache": "^9.0", "illuminate/config": "^9.0", "illuminate/console": "^9.0", "illuminate/container": "^9.0", "illuminate/database": "^9.0", "illuminate/http": "^9.0", "illuminate/support": "^9.0", "php": "^8.0"}, "require-dev": {"doctrine/dbal": "^3.3", "fakerphp/faker": "^1.11", "laravel/legacy-factories": "^1.3", "laravel/nova": "^3.9", "orchestra/testbench": "^7.0", "orchestra/testbench-browser-kit": "^7.0", "php-coveralls/php-coveralls": "^2.2", "phpmd/phpmd": "^2.11", "phpunit/phpunit": "^9.5", "slevomat/coding-standard": "^7.0", "squizlabs/php_codesniffer": "^3.6", "symfony/thanks": "^1.2"}, "type": "library", "extra": {"laravel": {"providers": ["GeneaLabs\\LaravelModelCaching\\Providers\\Service"]}}, "autoload": {"psr-4": {"GeneaLabs\\LaravelModelCaching\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Automatic caching for Eloquent models.", "support": {"issues": "https://github.com/GeneaLabs/laravel-model-caching/issues", "source": "https://github.com/GeneaLabs/laravel-model-caching/tree/0.12.5"}, "time": "2022-07-30T14:19:41+00:00"}, {"name": "genealabs/laravel-pivot-events", "version": "9.0.4", "source": {"type": "git", "url": "https://github.com/GeneaLabs/laravel-pivot-events.git", "reference": "3e076a8d266baf0833e7496ca4e5eb65d5df4b76"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/GeneaLabs/laravel-pivot-events/zipball/3e076a8d266baf0833e7496ca4e5eb65d5df4b76", "reference": "3e076a8d266baf0833e7496ca4e5eb65d5df4b76", "shasum": ""}, "require": {"illuminate/database": "^8.0|^9.0", "illuminate/support": "^8.0|^9.0"}, "require-dev": {"orchestra/testbench": "^7.0", "symfony/thanks": "^1.0"}, "type": "library", "autoload": {"psr-4": {"GeneaLabs\\LaravelPivotEvents\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://genealabs.com", "role": "Developer"}], "description": "This package introduces new eloquent events for sync(), attach(), detach() or updateExistingPivot() methods on BelongsToMany relation.", "homepage": "https://github.com/GeneaLabs/laravel-pivot", "keywords": ["eloquent events", "eloquent extra events", "laravel BelongsToMany events", "laravel pivot events", "laravel sync events"], "support": {"issues": "https://github.com/GeneaLabs/laravel-pivot/issues", "source": "https://github.com/GeneaLabs/laravel-pivot"}, "time": "2022-03-30T12:50:17+00:00"}, {"name": "giggsey/libphonenumber-for-php", "version": "8.12.53", "source": {"type": "git", "url": "https://github.com/giggsey/libphonenumber-for-php.git", "reference": "2e39201ca4b88c1ec0594cd78128d21e94929d04"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/giggsey/libphonenumber-for-php/zipball/2e39201ca4b88c1ec0594cd78128d21e94929d04", "reference": "2e39201ca4b88c1ec0594cd78128d21e94929d04", "shasum": ""}, "require": {"giggsey/locale": "^1.7|^2.0", "php": ">=5.3.2", "symfony/polyfill-mbstring": "^1.17"}, "require-dev": {"pear/pear-core-minimal": "^1.9", "pear/pear_exception": "^1.0", "pear/versioncontrol_git": "^0.5", "phing/phing": "^2.7", "php-coveralls/php-coveralls": "^1.0|^2.0", "symfony/console": "^2.8|^3.0|^v4.4|^v5.2", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "autoload": {"psr-4": {"libphonenumber\\": "src/"}, "exclude-from-classmap": ["/src/data/", "/src/carrier/data/", "/src/geocoding/data/", "/src/timezone/data/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://giggsey.com/"}], "description": "PHP Port of Google's libphonenumber", "homepage": "https://github.com/giggsey/libphonenumber-for-php", "keywords": ["geocoding", "geolocation", "libphonenumber", "mobile", "phonenumber", "validation"], "support": {"irc": "irc://irc.appliedirc.com/lobby", "issues": "https://github.com/giggsey/libphonenumber-for-php/issues", "source": "https://github.com/giggsey/libphonenumber-for-php"}, "time": "2022-08-08T06:53:30+00:00"}, {"name": "giggsey/locale", "version": "2.2", "source": {"type": "git", "url": "https://github.com/giggsey/Locale.git", "reference": "9c1dca769253f6a3e81f9a5c167f53b6a54ab635"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/giggsey/Locale/zipball/9c1dca769253f6a3e81f9a5c167f53b6a54ab635", "reference": "9c1dca769253f6a3e81f9a5c167f53b6a54ab635", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"ext-json": "*", "pear/pear-core-minimal": "^1.9", "pear/pear_exception": "^1.0", "pear/versioncontrol_git": "^0.5", "phing/phing": "^2.7", "php-coveralls/php-coveralls": "^2.0", "phpunit/phpunit": "^8.5|^9.5", "symfony/console": "^5.0", "symfony/filesystem": "^5.0", "symfony/finder": "^5.0", "symfony/process": "^5.0"}, "type": "library", "autoload": {"psr-4": {"Giggsey\\Locale\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://giggsey.com/"}], "description": "Locale functions required by libphonenumber-for-php", "support": {"issues": "https://github.com/giggsey/Locale/issues", "source": "https://github.com/giggsey/Locale/tree/2.2"}, "time": "2022-04-06T07:33:59+00:00"}, {"name": "graham-campbell/result-type", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/GrahamCampbell/Result-Type.git", "reference": "a878d45c1914464426dc94da61c9e1d36ae262a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/GrahamCampbell/Result-Type/zipball/a878d45c1914464426dc94da61c9e1d36ae262a8", "reference": "a878d45c1914464426dc94da61c9e1d36ae262a8", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9"}, "require-dev": {"phpunit/phpunit": "^8.5.28 || ^9.5.21"}, "type": "library", "autoload": {"psr-4": {"GrahamCampbell\\ResultType\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "An Implementation Of The Result Type", "keywords": ["<PERSON>", "Graham<PERSON><PERSON><PERSON>", "Result Type", "Result-Type", "result"], "support": {"issues": "https://github.com/GrahamCampbell/Result-Type/issues", "source": "https://github.com/GrahamCampbell/Result-Type/tree/v1.1.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/graham-campbell/result-type", "type": "tidelift"}], "time": "2022-07-30T15:56:11+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.4.5", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "1dd98b0564cb3f6bd16ce683cb755f94c10fbd82"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/1dd98b0564cb3f6bd16ce683cb755f94c10fbd82", "reference": "1dd98b0564cb3f6bd16ce683cb755f94c10fbd82", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5", "guzzlehttp/psr7": "^1.9 || ^2.4", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "ext-curl": "*", "php-http/client-integration-tests": "^3.0", "phpunit/phpunit": "^8.5.5 || ^9.3.5", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.4-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.4.5"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2022-06-20T22:16:13+00:00"}, {"name": "guzzlehttp/promises", "version": "1.5.1", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "fe752aedc9fd8fcca3fe7ad05d419d32998a06da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/fe752aedc9fd8fcca3fe7ad05d419d32998a06da", "reference": "fe752aedc9fd8fcca3fe7ad05d419d32998a06da", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/1.5.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2021-10-22T20:56:57+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.4.0", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "13388f00956b1503577598873fffb5ae994b5737"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/13388f00956b1503577598873fffb5ae994b5737", "reference": "13388f00956b1503577598873fffb5ae994b5737", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "http-interop/http-factory-tests": "^0.9", "phpunit/phpunit": "^8.5.8 || ^9.3.10"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.4.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2022-06-20T21:43:11+00:00"}, {"name": "intervention/image", "version": "2.7.2", "source": {"type": "git", "url": "https://github.com/Intervention/image.git", "reference": "04be355f8d6734c826045d02a1079ad658322dad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Intervention/image/zipball/04be355f8d6734c826045d02a1079ad658322dad", "reference": "04be355f8d6734c826045d02a1079ad658322dad", "shasum": ""}, "require": {"ext-fileinfo": "*", "guzzlehttp/psr7": "~1.1 || ^2.0", "php": ">=5.4.0"}, "require-dev": {"mockery/mockery": "~0.9.2", "phpunit/phpunit": "^4.8 || ^5.7 || ^7.5.15"}, "suggest": {"ext-gd": "to use GD library based image processing.", "ext-imagick": "to use Imagick based image processing.", "intervention/imagecache": "Caching extension for the Intervention Image library"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}, "laravel": {"providers": ["Intervention\\Image\\ImageServiceProvider"], "aliases": {"Image": "Intervention\\Image\\Facades\\Image"}}}, "autoload": {"psr-4": {"Intervention\\Image\\": "src/Intervention/Image"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://intervention.io/"}], "description": "Image handling and manipulation library with support for Laravel integration", "homepage": "http://image.intervention.io/", "keywords": ["gd", "image", "imagick", "laravel", "thumbnail", "watermark"], "support": {"issues": "https://github.com/Intervention/image/issues", "source": "https://github.com/Intervention/image/tree/2.7.2"}, "funding": [{"url": "https://paypal.me/interventionio", "type": "custom"}, {"url": "https://github.com/Intervention", "type": "github"}], "time": "2022-05-21T17:30:32+00:00"}, {"name": "laracasts/presenter", "version": "0.2.5", "source": {"type": "git", "url": "https://github.com/laracasts/Presenter.git", "reference": "d9b9050abf0af1d75465284a62b537c3f4ac7d1f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laracasts/Presenter/zipball/d9b9050abf0af1d75465284a62b537c3f4ac7d1f", "reference": "d9b9050abf0af1d75465284a62b537c3f4ac7d1f", "shasum": ""}, "require": {"illuminate/support": "~5.0|~6.0|~7.0|~8.0|^9.0", "php": ">=5.4.0"}, "require-dev": {"mockery/mockery": "~0.9", "phpspec/phpspec": "~2.0"}, "type": "library", "autoload": {"psr-0": {"Laracasts\\Presenter": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Simple view presenters", "keywords": ["laravel", "presenter", "view"], "support": {"issues": "https://github.com/laracasts/Presenter/issues", "source": "https://github.com/laracasts/Presenter/tree/0.2.5"}, "time": "2022-04-12T18:22:42+00:00"}, {"name": "laracasts/utilities", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/laracasts/PHP-Vars-To-Js-Transformer.git", "reference": "cfcda21b2425652e869af253d385d78e2129e3a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laracasts/PHP-Vars-To-Js-Transformer/zipball/cfcda21b2425652e869af253d385d78e2129e3a2", "reference": "cfcda21b2425652e869af253d385d78e2129e3a2", "shasum": ""}, "require": {"illuminate/support": "^5.0|^6.0|^7.0|^8.0|^9.0", "php": ">=5.5.0|>=7.2.5|>=8.0.0"}, "require-dev": {"phpspec/phpspec": ">=2.0"}, "type": "library", "extra": {"laravel": {"providers": ["Laracasts\\Utilities\\JavaScript\\JavaScriptServiceProvider"], "aliases": {"JavaScript": "Laracasts\\Utilities\\JavaScript\\JavaScriptFacade"}}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"Laracasts\\Utilities\\JavaScript\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Transform your PHP to JavaScript", "keywords": ["javascript", "laravel"], "support": {"issues": "https://github.com/laracasts/PHP-Vars-To-Js-Transformer/issues", "source": "https://github.com/laracasts/PHP-Vars-To-Js-Transformer/tree/3.2.1"}, "time": "2022-02-09T14:09:45+00:00"}, {"name": "laravel/framework", "version": "v9.25.1", "source": {"type": "git", "url": "https://github.com/laravel/framework.git", "reference": "e8af8c2212e3717757ea7f459a655a2e9e771109"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/framework/zipball/e8af8c2212e3717757ea7f459a655a2e9e771109", "reference": "e8af8c2212e3717757ea7f459a655a2e9e771109", "shasum": ""}, "require": {"doctrine/inflector": "^2.0", "dragonmantank/cron-expression": "^3.1", "egulias/email-validator": "^3.1", "ext-mbstring": "*", "ext-openssl": "*", "fruitcake/php-cors": "^1.2", "laravel/serializable-closure": "^1.0", "league/commonmark": "^2.2", "league/flysystem": "^3.0.16", "monolog/monolog": "^2.0", "nesbot/carbon": "^2.53.1", "nunomaduro/termwind": "^1.13", "php": "^8.0.2", "psr/container": "^1.1.1|^2.0.1", "psr/log": "^1.0|^2.0|^3.0", "psr/simple-cache": "^1.0|^2.0|^3.0", "ramsey/uuid": "^4.2.2", "symfony/console": "^6.0.3", "symfony/error-handler": "^6.0", "symfony/finder": "^6.0", "symfony/http-foundation": "^6.0", "symfony/http-kernel": "^6.0", "symfony/mailer": "^6.0", "symfony/mime": "^6.0", "symfony/process": "^6.0", "symfony/routing": "^6.0", "symfony/var-dumper": "^6.0", "tijsverkoyen/css-to-inline-styles": "^2.2.2", "vlucas/phpdotenv": "^5.4.1", "voku/portable-ascii": "^2.0"}, "conflict": {"tightenco/collect": "<5.5.33"}, "provide": {"psr/container-implementation": "1.1|2.0", "psr/simple-cache-implementation": "1.0|2.0|3.0"}, "replace": {"illuminate/auth": "self.version", "illuminate/broadcasting": "self.version", "illuminate/bus": "self.version", "illuminate/cache": "self.version", "illuminate/collections": "self.version", "illuminate/conditionable": "self.version", "illuminate/config": "self.version", "illuminate/console": "self.version", "illuminate/container": "self.version", "illuminate/contracts": "self.version", "illuminate/cookie": "self.version", "illuminate/database": "self.version", "illuminate/encryption": "self.version", "illuminate/events": "self.version", "illuminate/filesystem": "self.version", "illuminate/hashing": "self.version", "illuminate/http": "self.version", "illuminate/log": "self.version", "illuminate/macroable": "self.version", "illuminate/mail": "self.version", "illuminate/notifications": "self.version", "illuminate/pagination": "self.version", "illuminate/pipeline": "self.version", "illuminate/queue": "self.version", "illuminate/redis": "self.version", "illuminate/routing": "self.version", "illuminate/session": "self.version", "illuminate/support": "self.version", "illuminate/testing": "self.version", "illuminate/translation": "self.version", "illuminate/validation": "self.version", "illuminate/view": "self.version"}, "require-dev": {"aws/aws-sdk-php": "^3.198.1", "doctrine/dbal": "^2.13.3|^3.1.4", "fakerphp/faker": "^1.9.2", "guzzlehttp/guzzle": "^7.2", "league/flysystem-aws-s3-v3": "^3.0", "league/flysystem-ftp": "^3.0", "league/flysystem-sftp-v3": "^3.0", "mockery/mockery": "^1.4.4", "orchestra/testbench-core": "^7.1", "pda/pheanstalk": "^4.0", "phpstan/phpstan": "^1.4.7", "phpunit/phpunit": "^9.5.8", "predis/predis": "^1.1.9|^2.0", "symfony/cache": "^6.0"}, "suggest": {"ably/ably-php": "Required to use the Ably broadcast driver (^1.0).", "aws/aws-sdk-php": "Required to use the SQS queue driver, DynamoDb failed job storage, and SES mail driver (^3.198.1).", "brianium/paratest": "Required to run tests in parallel (^6.0).", "doctrine/dbal": "Required to rename columns and drop SQLite columns (^2.13.3|^3.1.4).", "ext-bcmath": "Required to use the multiple_of validation rule.", "ext-ftp": "Required to use the Flysystem FTP driver.", "ext-gd": "Required to use Illuminate\\Http\\Testing\\FileFactory::image().", "ext-memcached": "Required to use the memcache cache driver.", "ext-pcntl": "Required to use all features of the queue worker.", "ext-posix": "Required to use all features of the queue worker.", "ext-redis": "Required to use the Redis cache and queue drivers (^4.0|^5.0).", "fakerphp/faker": "Required to use the eloquent factory builder (^1.9.1).", "filp/whoops": "Required for friendly error pages in development (^2.14.3).", "guzzlehttp/guzzle": "Required to use the HTTP Client and the ping methods on schedules (^7.2).", "laravel/tinker": "Required to use the tinker console command (^2.0).", "league/flysystem-aws-s3-v3": "Required to use the Flysystem S3 driver (^3.0).", "league/flysystem-ftp": "Required to use the Flysystem FTP driver (^3.0).", "league/flysystem-sftp-v3": "Required to use the Flysystem SFTP driver (^3.0).", "mockery/mockery": "Required to use mocking (^1.4.4).", "nyholm/psr7": "Required to use PSR-7 bridging features (^1.2).", "pda/pheanstalk": "Required to use the beanstalk queue driver (^4.0).", "phpunit/phpunit": "Required to use assertions and run tests (^9.5.8).", "predis/predis": "Required to use the predis connector (^1.1.9|^2.0).", "psr/http-message": "Required to allow Storage::put to accept a StreamInterface (^1.0).", "pusher/pusher-php-server": "Required to use the <PERSON><PERSON><PERSON> broadcast driver (^6.0|^7.0).", "symfony/cache": "Required to PSR-6 cache bridge (^6.0).", "symfony/filesystem": "Required to enable support for relative symbolic links (^6.0).", "symfony/http-client": "Required to enable support for the Symfony API mail transports (^6.0).", "symfony/mailgun-mailer": "Required to enable support for the Mailgun mail transport (^6.0).", "symfony/postmark-mailer": "Required to enable support for the Postmark mail transport (^6.0).", "symfony/psr-http-message-bridge": "Required to use PSR-7 bridging features (^2.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "autoload": {"files": ["src/Illuminate/Collections/helpers.php", "src/Illuminate/Events/functions.php", "src/Illuminate/Foundation/helpers.php", "src/Illuminate/Support/helpers.php"], "psr-4": {"Illuminate\\": "src/Illuminate/", "Illuminate\\Support\\": ["src/Illuminate/Macroable/", "src/Illuminate/Collections/", "src/Illuminate/Conditionable/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Laravel Framework.", "homepage": "https://laravel.com", "keywords": ["framework", "laravel"], "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2022-08-16T16:36:05+00:00"}, {"name": "laravel/sanctum", "version": "v2.15.1", "source": {"type": "git", "url": "https://github.com/laravel/sanctum.git", "reference": "31fbe6f85aee080c4dc2f9b03dc6dd5d0ee72473"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/sanctum/zipball/31fbe6f85aee080c4dc2f9b03dc6dd5d0ee72473", "reference": "31fbe6f85aee080c4dc2f9b03dc6dd5d0ee72473", "shasum": ""}, "require": {"ext-json": "*", "illuminate/console": "^6.9|^7.0|^8.0|^9.0", "illuminate/contracts": "^6.9|^7.0|^8.0|^9.0", "illuminate/database": "^6.9|^7.0|^8.0|^9.0", "illuminate/support": "^6.9|^7.0|^8.0|^9.0", "php": "^7.2|^8.0"}, "require-dev": {"mockery/mockery": "^1.0", "orchestra/testbench": "^4.0|^5.0|^6.0|^7.0", "phpunit/phpunit": "^8.0|^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}, "laravel": {"providers": ["Laravel\\Sanctum\\SanctumServiceProvider"]}}, "autoload": {"psr-4": {"Laravel\\Sanctum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Laravel Sanctum provides a featherweight authentication system for SPAs and simple APIs.", "keywords": ["auth", "laravel", "sanctum"], "support": {"issues": "https://github.com/laravel/sanctum/issues", "source": "https://github.com/laravel/sanctum"}, "time": "2022-04-08T13:39:49+00:00"}, {"name": "laravel/serializable-closure", "version": "v1.2.0", "source": {"type": "git", "url": "https://github.com/laravel/serializable-closure.git", "reference": "09f0e9fb61829f628205b7c94906c28740ff9540"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/serializable-closure/zipball/09f0e9fb61829f628205b7c94906c28740ff9540", "reference": "09f0e9fb61829f628205b7c94906c28740ff9540", "shasum": ""}, "require": {"php": "^7.3|^8.0"}, "require-dev": {"pestphp/pest": "^1.18", "phpstan/phpstan": "^0.12.98", "symfony/var-dumper": "^5.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Laravel\\SerializableClosure\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Laravel Serializable Closure provides an easy and secure way to serialize closures in PHP.", "keywords": ["closure", "laravel", "serializable"], "support": {"issues": "https://github.com/laravel/serializable-closure/issues", "source": "https://github.com/laravel/serializable-closure"}, "time": "2022-05-16T17:09:47+00:00"}, {"name": "laravel/tinker", "version": "v2.7.2", "source": {"type": "git", "url": "https://github.com/laravel/tinker.git", "reference": "dff39b661e827dae6e092412f976658df82dbac5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/tinker/zipball/dff39b661e827dae6e092412f976658df82dbac5", "reference": "dff39b661e827dae6e092412f976658df82dbac5", "shasum": ""}, "require": {"illuminate/console": "^6.0|^7.0|^8.0|^9.0", "illuminate/contracts": "^6.0|^7.0|^8.0|^9.0", "illuminate/support": "^6.0|^7.0|^8.0|^9.0", "php": "^7.2.5|^8.0", "psy/psysh": "^0.10.4|^0.11.1", "symfony/var-dumper": "^4.3.4|^5.0|^6.0"}, "require-dev": {"mockery/mockery": "~1.3.3|^1.4.2", "phpunit/phpunit": "^8.5.8|^9.3.3"}, "suggest": {"illuminate/database": "The Illuminate Database package (^6.0|^7.0|^8.0|^9.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}, "laravel": {"providers": ["Laravel\\Tinker\\TinkerServiceProvider"]}}, "autoload": {"psr-4": {"Laravel\\Tinker\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful REPL for the Laravel framework.", "keywords": ["REPL", "Tinker", "laravel", "psysh"], "support": {"issues": "https://github.com/laravel/tinker/issues", "source": "https://github.com/laravel/tinker/tree/v2.7.2"}, "time": "2022-03-23T12:38:24+00:00"}, {"name": "laravel/ui", "version": "v3.4.6", "source": {"type": "git", "url": "https://github.com/laravel/ui.git", "reference": "65ec5c03f7fee2c8ecae785795b829a15be48c2c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/ui/zipball/65ec5c03f7fee2c8ecae785795b829a15be48c2c", "reference": "65ec5c03f7fee2c8ecae785795b829a15be48c2c", "shasum": ""}, "require": {"illuminate/console": "^8.42|^9.0", "illuminate/filesystem": "^8.42|^9.0", "illuminate/support": "^8.82|^9.0", "illuminate/validation": "^8.42|^9.0", "php": "^7.3|^8.0"}, "require-dev": {"orchestra/testbench": "^6.23|^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}, "laravel": {"providers": ["Laravel\\Ui\\UiServiceProvider"]}}, "autoload": {"psr-4": {"Laravel\\Ui\\": "src/", "Illuminate\\Foundation\\Auth\\": "auth-backend/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Laravel UI utilities and presets.", "keywords": ["laravel", "ui"], "support": {"source": "https://github.com/laravel/ui/tree/v3.4.6"}, "time": "2022-05-20T13:38:08+00:00"}, {"name": "league/commonmark", "version": "2.3.5", "source": {"type": "git", "url": "https://github.com/thephpleague/commonmark.git", "reference": "84d74485fdb7074f4f9dd6f02ab957b1de513257"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/commonmark/zipball/84d74485fdb7074f4f9dd6f02ab957b1de513257", "reference": "84d74485fdb7074f4f9dd6f02ab957b1de513257", "shasum": ""}, "require": {"ext-mbstring": "*", "league/config": "^1.1.1", "php": "^7.4 || ^8.0", "psr/event-dispatcher": "^1.0", "symfony/deprecation-contracts": "^2.1 || ^3.0", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"cebe/markdown": "^1.0", "commonmark/cmark": "0.30.0", "commonmark/commonmark.js": "0.30.0", "composer/package-versions-deprecated": "^1.8", "embed/embed": "^4.4", "erusev/parsedown": "^1.0", "ext-json": "*", "github/gfm": "0.29.0", "michelf/php-markdown": "^1.4", "nyholm/psr7": "^1.5", "phpstan/phpstan": "^1.8.2", "phpunit/phpunit": "^9.5.21", "scrutinizer/ocular": "^1.8.1", "symfony/finder": "^5.3 | ^6.0", "symfony/yaml": "^2.3 | ^3.0 | ^4.0 | ^5.0 | ^6.0", "unleashedtech/php-coding-standard": "^3.1.1", "vimeo/psalm": "^4.24.0"}, "suggest": {"symfony/yaml": "v2.3+ required if using the Front Matter extension"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.4-dev"}}, "autoload": {"psr-4": {"League\\CommonMark\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com", "role": "Lead Developer"}], "description": "Highly-extensible PHP Markdown parser which fully supports the CommonMark spec and GitHub-Flavored Markdown (GFM)", "homepage": "https://commonmark.thephpleague.com", "keywords": ["commonmark", "flavored", "gfm", "github", "github-flavored", "markdown", "md", "parser"], "support": {"docs": "https://commonmark.thephpleague.com/", "forum": "https://github.com/thephpleague/commonmark/discussions", "issues": "https://github.com/thephpleague/commonmark/issues", "rss": "https://github.com/thephpleague/commonmark/releases.atom", "source": "https://github.com/thephpleague/commonmark"}, "funding": [{"url": "https://www.colinodell.com/sponsor", "type": "custom"}, {"url": "https://www.paypal.me/colinpodell/10.00", "type": "custom"}, {"url": "https://github.com/colinodell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/commonmark", "type": "tidelift"}], "time": "2022-07-29T10:59:45+00:00"}, {"name": "league/config", "version": "v1.1.1", "source": {"type": "git", "url": "https://github.com/thephpleague/config.git", "reference": "a9d39eeeb6cc49d10a6e6c36f22c4c1f4a767f3e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/config/zipball/a9d39eeeb6cc49d10a6e6c36f22c4c1f4a767f3e", "reference": "a9d39eeeb6cc49d10a6e6c36f22c4c1f4a767f3e", "shasum": ""}, "require": {"dflydev/dot-access-data": "^3.0.1", "nette/schema": "^1.2", "php": "^7.4 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.90", "phpunit/phpunit": "^9.5.5", "scrutinizer/ocular": "^1.8.1", "unleashedtech/php-coding-standard": "^3.1", "vimeo/psalm": "^4.7.3"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.2-dev"}}, "autoload": {"psr-4": {"League\\Config\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com", "role": "Lead Developer"}], "description": "Define configuration arrays with strict schemas and access values with dot notation", "homepage": "https://config.thephpleague.com", "keywords": ["array", "config", "configuration", "dot", "dot-access", "nested", "schema"], "support": {"docs": "https://config.thephpleague.com/", "issues": "https://github.com/thephpleague/config/issues", "rss": "https://github.com/thephpleague/config/releases.atom", "source": "https://github.com/thephpleague/config"}, "funding": [{"url": "https://www.colinodell.com/sponsor", "type": "custom"}, {"url": "https://www.paypal.me/colinpodell/10.00", "type": "custom"}, {"url": "https://github.com/colinodell", "type": "github"}], "time": "2021-08-14T12:15:32+00:00"}, {"name": "league/flysystem", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem.git", "reference": "81aea9e5217084c7850cd36e1587ee4aad721c6b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem/zipball/81aea9e5217084c7850cd36e1587ee4aad721c6b", "reference": "81aea9e5217084c7850cd36e1587ee4aad721c6b", "shasum": ""}, "require": {"league/mime-type-detection": "^1.0.0", "php": "^8.0.2"}, "conflict": {"aws/aws-sdk-php": "3.209.31 || 3.210.0", "guzzlehttp/guzzle": "<7.0", "guzzlehttp/ringphp": "<1.1.1", "symfony/http-client": "<5.2"}, "require-dev": {"async-aws/s3": "^1.5", "async-aws/simple-s3": "^1.0", "aws/aws-sdk-php": "^3.198.1", "composer/semver": "^3.0", "ext-fileinfo": "*", "ext-ftp": "*", "ext-zip": "*", "friendsofphp/php-cs-fixer": "^3.5", "google/cloud-storage": "^1.23", "microsoft/azure-storage-blob": "^1.1", "phpseclib/phpseclib": "^2.0", "phpstan/phpstan": "^0.12.26", "phpunit/phpunit": "^9.5.11", "sabre/dav": "^4.3.1"}, "type": "library", "autoload": {"psr-4": {"League\\Flysystem\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "File storage abstraction for PHP", "keywords": ["WebDAV", "aws", "cloud", "file", "files", "filesystem", "filesystems", "ftp", "s3", "sftp", "storage"], "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.2.1"}, "funding": [{"url": "https://offset.earth/frankdejonge", "type": "custom"}, {"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2022-08-14T20:48:34+00:00"}, {"name": "league/mime-type-detection", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/thephpleague/mime-type-detection.git", "reference": "ff6248ea87a9f116e78edd6002e39e5128a0d4dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/ff6248ea87a9f116e78edd6002e39e5128a0d4dd", "reference": "ff6248ea87a9f116e78edd6002e39e5128a0d4dd", "shasum": ""}, "require": {"ext-fileinfo": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "phpstan/phpstan": "^0.12.68", "phpunit/phpunit": "^8.5.8 || ^9.3"}, "type": "library", "autoload": {"psr-4": {"League\\MimeTypeDetection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Mime-type detection for Flysystem", "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.11.0"}, "funding": [{"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2022-04-17T13:12:02+00:00"}, {"name": "maatwebsite/excel", "version": "3.1.40", "source": {"type": "git", "url": "https://github.com/SpartnerNL/Laravel-Excel.git", "reference": "8a54972e3d616c74687c3cbff15765555761885c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/SpartnerNL/Laravel-Excel/zipball/8a54972e3d616c74687c3cbff15765555761885c", "reference": "8a54972e3d616c74687c3cbff15765555761885c", "shasum": ""}, "require": {"ext-json": "*", "illuminate/support": "5.8.*|^6.0|^7.0|^8.0|^9.0", "php": "^7.0|^8.0", "phpoffice/phpspreadsheet": "^1.18"}, "require-dev": {"orchestra/testbench": "^6.0|^7.0", "predis/predis": "^1.1"}, "type": "library", "extra": {"laravel": {"providers": ["Maatwebsite\\Excel\\ExcelServiceProvider"], "aliases": {"Excel": "Maatwebsite\\Excel\\Facades\\Excel"}}}, "autoload": {"psr-4": {"Maatwebsite\\Excel\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Supercharged Excel exports and imports in Laravel", "keywords": ["PHPExcel", "batch", "csv", "excel", "export", "import", "laravel", "php", "phpspreadsheet"], "support": {"issues": "https://github.com/SpartnerNL/Laravel-Excel/issues", "source": "https://github.com/SpartnerNL/Laravel-Excel/tree/3.1.40"}, "funding": [{"url": "https://laravel-excel.com/commercial-support", "type": "custom"}, {"url": "https://github.com/patrickbrouwers", "type": "github"}], "time": "2022-05-02T13:50:01+00:00"}, {"name": "maatwebsite/laravel-sidebar", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/TypiCMS/Laravel-Sidebar.git", "reference": "452f789e5b34c1416c2dd55930b5782f5e129d73"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TypiCMS/Laravel-Sidebar/zipball/452f789e5b34c1416c2dd55930b5782f5e129d73", "reference": "452f789e5b34c1416c2dd55930b5782f5e129d73", "shasum": ""}, "require": {"illuminate/contracts": ">=5.0", "illuminate/routing": ">=5.0", "illuminate/support": ">=5.0", "illuminate/view": ">=5.0", "php": ">=5.4.0"}, "require-dev": {"mockery/mockery": "~1.0", "phpunit/phpunit": "~5.0"}, "type": "library", "autoload": {"psr-4": {"Maatwebsite\\Sidebar\\": "src/Maatwebsite/Sidebar"}}, "license": ["LGPL"], "authors": [{"name": "Maatwebsite.nl", "email": "<EMAIL>"}], "description": "A sidebar builder for Laravel", "keywords": ["acp", "laravel"], "support": {"source": "https://github.com/TypiCMS/Laravel-Sidebar/tree/1.2.3"}, "time": "2022-04-19T11:58:24+00:00"}, {"name": "maennchen/zipstream-php", "version": "2.2.1", "source": {"type": "git", "url": "https://github.com/maennchen/ZipStream-PHP.git", "reference": "211e9ba1530ea5260b45d90c9ea252f56ec52729"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maennchen/ZipStream-PHP/zipball/211e9ba1530ea5260b45d90c9ea252f56ec52729", "reference": "211e9ba1530ea5260b45d90c9ea252f56ec52729", "shasum": ""}, "require": {"myclabs/php-enum": "^1.5", "php": "^7.4 || ^8.0", "psr/http-message": "^1.0", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"ext-zip": "*", "guzzlehttp/guzzle": "^6.5.3 || ^7.2.0", "mikey179/vfsstream": "^1.6", "php-coveralls/php-coveralls": "^2.4", "phpunit/phpunit": "^8.5.8 || ^9.4.2", "vimeo/psalm": "^4.1"}, "type": "library", "autoload": {"psr-4": {"ZipStream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Jonatan Männchen", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "ZipStream is a library for dynamically streaming dynamic zip files from PHP without writing to the disk at all on the server.", "keywords": ["stream", "zip"], "support": {"issues": "https://github.com/maennchen/ZipStream-PHP/issues", "source": "https://github.com/maennchen/ZipStream-PHP/tree/2.2.1"}, "funding": [{"url": "https://opencollective.com/zipstream", "type": "open_collective"}], "time": "2022-05-18T15:52:06+00:00"}, {"name": "markbaker/complex", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "ab8bc271e404909db09ff2d5ffa1e538085c0f22"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/ab8bc271e404909db09ff2d5ffa1e538085c0f22", "reference": "ab8bc271e404909db09ff2d5ffa1e538085c0f22", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "phpcompatibility/php-compatibility": "^9.0", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.3", "squizlabs/php_codesniffer": "^3.4"}, "type": "library", "autoload": {"psr-4": {"Complex\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "support": {"issues": "https://github.com/MarkBaker/PHPComplex/issues", "source": "https://github.com/MarkBaker/PHPComplex/tree/3.0.1"}, "time": "2021-06-29T15:32:53+00:00"}, {"name": "markbaker/matrix", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "c66aefcafb4f6c269510e9ac46b82619a904c576"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/c66aefcafb4f6c269510e9ac46b82619a904c576", "reference": "c66aefcafb4f6c269510e9ac46b82619a904c576", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "phpcompatibility/php-compatibility": "^9.0", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.3", "sebastian/phpcpd": "^4.0", "squizlabs/php_codesniffer": "^3.4"}, "type": "library", "autoload": {"psr-4": {"Matrix\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "support": {"issues": "https://github.com/MarkBaker/PHPMatrix/issues", "source": "https://github.com/MarkBaker/PHPMatrix/tree/3.0.0"}, "time": "2021-07-01T19:01:15+00:00"}, {"name": "masterminds/html5", "version": "2.7.5", "source": {"type": "git", "url": "https://github.com/Masterminds/html5-php.git", "reference": "f640ac1bdddff06ea333a920c95bbad8872429ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Masterminds/html5-php/zipball/f640ac1bdddff06ea333a920c95bbad8872429ab", "reference": "f640ac1bdddff06ea333a920c95bbad8872429ab", "shasum": ""}, "require": {"ext-ctype": "*", "ext-dom": "*", "ext-libxml": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7.21 || ^6 || ^7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Masterminds\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An HTML5 parser and serializer.", "homepage": "http://masterminds.github.io/html5-php", "keywords": ["HTML5", "dom", "html", "parser", "querypath", "serializer", "xml"], "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/2.7.5"}, "time": "2021-07-01T14:25:37+00:00"}, {"name": "monolog/monolog", "version": "2.8.0", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "720488632c590286b88b80e62aa3d3d551ad4a50"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/720488632c590286b88b80e62aa3d3d551ad4a50", "reference": "720488632c590286b88b80e62aa3d3d551ad4a50", "shasum": ""}, "require": {"php": ">=7.2", "psr/log": "^1.0.1 || ^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "1.0.0 || 2.0.0 || 3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "ext-json": "*", "graylog2/gelf-php": "^1.4.2", "guzzlehttp/guzzle": "^7.4", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "phpspec/prophecy": "^1.15", "phpstan/phpstan": "^0.12.91", "phpunit/phpunit": "^8.5.14", "predis/predis": "^1.1 || ^2.0", "rollbar/rollbar": "^1.3 || ^2 || ^3", "ruflin/elastica": "^7", "swiftmailer/swiftmailer": "^5.3|^6.0", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.8.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2022-07-24T11:55:47+00:00"}, {"name": "msurguy/honeypot", "version": "1.1.7", "source": {"type": "git", "url": "https://github.com/msurguy/Honeypot.git", "reference": "6e0d37201f936a8d8f3e2825dc038666d140fcd7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/msurguy/Honeypot/zipball/6e0d37201f936a8d8f3e2825dc038666d140fcd7", "reference": "6e0d37201f936a8d8f3e2825dc038666d140fcd7", "shasum": ""}, "require": {"illuminate/config": "4.*|5.*|6.*|7.*|8.*|9.*", "illuminate/support": "4.*|5.*|6.*|7.*|8.*|9.*", "illuminate/translation": "4.*|5.*|6.*|7.*|8.*|9.*", "php": ">=5.3.0"}, "require-dev": {"mockery/mockery": "0.9.*", "phpunit/phpunit": "4.0.*"}, "type": "library", "extra": {"laravel": {"providers": ["Msurguy\\Honeypot\\HoneypotServiceProvider"], "aliases": {"Honeypot": "Msurguy\\Honeypot\\HoneypotFacade"}}}, "autoload": {"psr-0": {"Msurguy\\Honeypot\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Honeypot spam prevention", "keywords": ["Forms", "Honeypot", "laravel", "spam"], "support": {"issues": "https://github.com/msurguy/Honeypot/issues", "source": "https://github.com/msurguy/Honeypot/tree/1.1.7"}, "time": "2022-02-14T17:19:26+00:00"}, {"name": "myclabs/php-enum", "version": "1.8.4", "source": {"type": "git", "url": "https://github.com/myclabs/php-enum.git", "reference": "a867478eae49c9f59ece437ae7f9506bfaa27483"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/php-enum/zipball/a867478eae49c9f59ece437ae7f9506bfaa27483", "reference": "a867478eae49c9f59ece437ae7f9506bfaa27483", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.3 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^9.5", "squizlabs/php_codesniffer": "1.*", "vimeo/psalm": "^4.6.2"}, "type": "library", "autoload": {"psr-4": {"MyCLabs\\Enum\\": "src/"}, "classmap": ["stubs/Stringable.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP Enum contributors", "homepage": "https://github.com/myclabs/php-enum/graphs/contributors"}], "description": "PHP Enum implementation", "homepage": "http://github.com/myclabs/php-enum", "keywords": ["enum"], "support": {"issues": "https://github.com/myclabs/php-enum/issues", "source": "https://github.com/myclabs/php-enum/tree/1.8.4"}, "funding": [{"url": "https://github.com/mnapoli", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/myclabs/php-enum", "type": "tidelift"}], "time": "2022-08-04T09:53:51+00:00"}, {"name": "nesbot/carbon", "version": "2.61.0", "source": {"type": "git", "url": "https://github.com/briannesbitt/Carbon.git", "reference": "bdf4f4fe3a3eac4de84dbec0738082a862c68ba6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/briannesbitt/Carbon/zipball/bdf4f4fe3a3eac4de84dbec0738082a862c68ba6", "reference": "bdf4f4fe3a3eac4de84dbec0738082a862c68ba6", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.1.8 || ^8.0", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation": "^3.4 || ^4.0 || ^5.0 || ^6.0"}, "require-dev": {"doctrine/dbal": "^2.0 || ^3.0", "doctrine/orm": "^2.7", "friendsofphp/php-cs-fixer": "^3.0", "kylekatarnls/multi-tester": "^2.0", "ondrejmirtes/better-reflection": "*", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.99 || ^1.7.14", "phpunit/php-file-iterator": "^2.0.5 || ^3.0.6", "phpunit/phpunit": "^7.5.20 || ^8.5.26 || ^9.5.20", "squizlabs/php_codesniffer": "^3.4"}, "bin": ["bin/carbon"], "type": "library", "extra": {"branch-alias": {"dev-3.x": "3.x-dev", "dev-master": "2.x-dev"}, "laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}}, "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://markido.com"}, {"name": "kylekatarnls", "homepage": "https://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "https://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"docs": "https://carbon.nesbot.com/docs", "issues": "https://github.com/briannesbitt/Carbon/issues", "source": "https://github.com/briannesbitt/Carbon"}, "funding": [{"url": "https://github.com/sponsors/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon#sponsor", "type": "opencollective"}, {"url": "https://tidelift.com/subscription/pkg/packagist-nesbot-carbon?utm_source=packagist-nesbot-carbon&utm_medium=referral&utm_campaign=readme", "type": "tidelift"}], "time": "2022-08-06T12:41:24+00:00"}, {"name": "nette/schema", "version": "v1.2.2", "source": {"type": "git", "url": "https://github.com/nette/schema.git", "reference": "9a39cef03a5b34c7de64f551538cbba05c2be5df"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/schema/zipball/9a39cef03a5b34c7de64f551538cbba05c2be5df", "reference": "9a39cef03a5b34c7de64f551538cbba05c2be5df", "shasum": ""}, "require": {"nette/utils": "^2.5.7 || ^3.1.5 ||  ^4.0", "php": ">=7.1 <8.2"}, "require-dev": {"nette/tester": "^2.3 || ^2.4", "phpstan/phpstan-nette": "^0.12", "tracy/tracy": "^2.7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "📐 Nette Schema: validating data structures against a given Schema.", "homepage": "https://nette.org", "keywords": ["config", "nette"], "support": {"issues": "https://github.com/nette/schema/issues", "source": "https://github.com/nette/schema/tree/v1.2.2"}, "time": "2021-10-15T11:40:02+00:00"}, {"name": "nette/utils", "version": "v3.2.7", "source": {"type": "git", "url": "https://github.com/nette/utils.git", "reference": "0af4e3de4df9f1543534beab255ccf459e7a2c99"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/utils/zipball/0af4e3de4df9f1543534beab255ccf459e7a2c99", "reference": "0af4e3de4df9f1543534beab255ccf459e7a2c99", "shasum": ""}, "require": {"php": ">=7.2 <8.2"}, "conflict": {"nette/di": "<3.0.6"}, "require-dev": {"nette/tester": "~2.0", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.3"}, "suggest": {"ext-gd": "to use Image", "ext-iconv": "to use Strings::webalize(), to<PERSON>cii(), chr() and reverse()", "ext-intl": "to use Strings::webalize(), toAscii(), normalize() and compare()", "ext-json": "to use Nette\\Utils\\Json", "ext-mbstring": "to use Strings::lower() etc...", "ext-tokenizer": "to use Nette\\Utils\\Reflection::getUseStatements()", "ext-xml": "to use Strings::length() etc. when mbstring is not available"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🛠  Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "homepage": "https://nette.org", "keywords": ["array", "core", "datetime", "images", "json", "nette", "paginator", "password", "slugify", "string", "unicode", "utf-8", "utility", "validation"], "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v3.2.7"}, "time": "2022-01-24T11:29:14+00:00"}, {"name": "nikic/php-parser", "version": "v4.14.0", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "34bea19b6e03d8153165d8f30bba4c3be86184c1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/34bea19b6e03d8153165d8f30bba4c3be86184c1", "reference": "34bea19b6e03d8153165d8f30bba4c3be86184c1", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.0"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^6.5 || ^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v4.14.0"}, "time": "2022-05-31T20:59:12+00:00"}, {"name": "nunomaduro/termwind", "version": "v1.14.0", "source": {"type": "git", "url": "https://github.com/nunomaduro/termwind.git", "reference": "10065367baccf13b6e30f5e9246fa4f63a79eb1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nunomaduro/termwind/zipball/10065367baccf13b6e30f5e9246fa4f63a79eb1d", "reference": "10065367baccf13b6e30f5e9246fa4f63a79eb1d", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "^8.0", "symfony/console": "^5.3.0|^6.0.0"}, "require-dev": {"ergebnis/phpstan-rules": "^1.0.", "illuminate/console": "^8.0|^9.0", "illuminate/support": "^8.0|^9.0", "laravel/pint": "^1.0.0", "pestphp/pest": "^1.21.0", "pestphp/pest-plugin-mock": "^1.0", "phpstan/phpstan": "^1.4.6", "phpstan/phpstan-strict-rules": "^1.1.0", "symfony/var-dumper": "^5.2.7|^6.0.0", "thecodingmachine/phpstan-strict-rules": "^1.0.0"}, "type": "library", "extra": {"laravel": {"providers": ["Termwind\\Laravel\\TermwindServiceProvider"]}}, "autoload": {"files": ["src/Functions.php"], "psr-4": {"Termwind\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Its like Tailwind CSS, but for the console.", "keywords": ["cli", "console", "css", "package", "php", "style"], "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.14.0"}, "funding": [{"url": "https://www.paypal.com/paypalme/enunomaduro", "type": "custom"}, {"url": "https://github.com/nunomaduro", "type": "github"}, {"url": "https://github.com/xiCO2k", "type": "github"}], "time": "2022-08-01T11:03:24+00:00"}, {"name": "phenx/php-font-lib", "version": "0.5.4", "source": {"type": "git", "url": "https://github.com/dompdf/php-font-lib.git", "reference": "dd448ad1ce34c63d09baccd05415e361300c35b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dompdf/php-font-lib/zipball/dd448ad1ce34c63d09baccd05415e361300c35b4", "reference": "dd448ad1ce34c63d09baccd05415e361300c35b4", "shasum": ""}, "require": {"ext-mbstring": "*"}, "require-dev": {"symfony/phpunit-bridge": "^3 || ^4 || ^5"}, "type": "library", "autoload": {"psr-4": {"FontLib\\": "src/FontLib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A library to read, parse, export and make subsets of different types of font files.", "homepage": "https://github.com/PhenX/php-font-lib", "support": {"issues": "https://github.com/dompdf/php-font-lib/issues", "source": "https://github.com/dompdf/php-font-lib/tree/0.5.4"}, "time": "2021-12-17T19:44:54+00:00"}, {"name": "phenx/php-svg-lib", "version": "0.4.1", "source": {"type": "git", "url": "https://github.com/dompdf/php-svg-lib.git", "reference": "4498b5df7b08e8469f0f8279651ea5de9626ed02"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dompdf/php-svg-lib/zipball/4498b5df7b08e8469f0f8279651ea5de9626ed02", "reference": "4498b5df7b08e8469f0f8279651ea5de9626ed02", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "^7.1 || ^7.2 || ^7.3 || ^7.4 || ^8.0", "sabberworm/php-css-parser": "^8.4"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.5 || ^9.5"}, "type": "library", "autoload": {"psr-4": {"Svg\\": "src/Svg"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A library to read, parse and export to PDF SVG files.", "homepage": "https://github.com/PhenX/php-svg-lib", "support": {"issues": "https://github.com/dompdf/php-svg-lib/issues", "source": "https://github.com/dompdf/php-svg-lib/tree/0.4.1"}, "time": "2022-03-07T12:52:04+00:00"}, {"name": "phpoffice/phpspreadsheet", "version": "1.24.1", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "69991111e05fca3ff7398e1e7fca9ebed33efec6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/69991111e05fca3ff7398e1e7fca9ebed33efec6", "reference": "69991111e05fca3ff7398e1e7fca9ebed33efec6", "shasum": ""}, "require": {"ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "ezyang/htmlpurifier": "^4.13", "maennchen/zipstream-php": "^2.1", "markbaker/complex": "^3.0", "markbaker/matrix": "^3.0", "php": "^7.3 || ^8.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/simple-cache": "^1.0 || ^2.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "dompdf/dompdf": "^1.0 || ^2.0", "friendsofphp/php-cs-fixer": "^3.2", "jpgraph/jpgraph": "^4.0", "mpdf/mpdf": "8.1.1", "phpcompatibility/php-compatibility": "^9.3", "phpstan/phpstan": "^1.1", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^8.5 || ^9.0", "squizlabs/php_codesniffer": "^3.7", "tecnickcom/tcpdf": "^6.4"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer (doesn't yet support PHP8)", "jpgraph/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer (doesn't yet support PHP8)"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "support": {"issues": "https://github.com/PHPOffice/PhpSpreadsheet/issues", "source": "https://github.com/PHPOffice/PhpSpreadsheet/tree/1.24.1"}, "time": "2022-07-18T19:50:48+00:00"}, {"name": "phpoption/phpoption", "version": "1.9.0", "source": {"type": "git", "url": "https://github.com/schmittjoh/php-option.git", "reference": "dc5ff11e274a90cc1c743f66c9ad700ce50db9ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-option/zipball/dc5ff11e274a90cc1c743f66c9ad700ce50db9ab", "reference": "dc5ff11e274a90cc1c743f66c9ad700ce50db9ab", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8", "phpunit/phpunit": "^8.5.28 || ^9.5.21"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": true}, "branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"PhpOption\\": "src/PhpOption/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.9.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpoption/phpoption", "type": "tidelift"}], "time": "2022-07-30T15:51:26+00:00"}, {"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client/tree/master"}, "time": "2020-06-29T06:28:15+00:00"}, {"name": "psr/http-factory", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "12ac7fcd07e5b077433f5f2bee95b3a771bf61be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/12ac7fcd07e5b077433f5f2bee95b3a771bf61be", "reference": "12ac7fcd07e5b077433f5f2bee95b3a771bf61be", "shasum": ""}, "require": {"php": ">=7.0.0", "psr/http-message": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory/tree/master"}, "time": "2019-04-30T12:38:16+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/master"}, "time": "2016-08-06T14:39:51+00:00"}, {"name": "psr/log", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "fe5ea303b0887d5caefd3d431c3e61ad47037001"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/fe5ea303b0887d5caefd3d431c3e61ad47037001", "reference": "fe5ea303b0887d5caefd3d431c3e61ad47037001", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.0"}, "time": "2021-07-14T16:46:02+00:00"}, {"name": "psr/simple-cache", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "8707bf3cea6f710bf6ef05491234e3ab06f6432a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/8707bf3cea6f710bf6ef05491234e3ab06f6432a", "reference": "8707bf3cea6f710bf6ef05491234e3ab06f6432a", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/2.0.0"}, "time": "2021-10-29T13:22:09+00:00"}, {"name": "psy/psysh", "version": "v0.11.8", "source": {"type": "git", "url": "https://github.com/bobthecow/psysh.git", "reference": "f455acf3645262ae389b10e9beba0c358aa6994e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bobthecow/psysh/zipball/f455acf3645262ae389b10e9beba0c358aa6994e", "reference": "f455acf3645262ae389b10e9beba0c358aa6994e", "shasum": ""}, "require": {"ext-json": "*", "ext-tokenizer": "*", "nikic/php-parser": "^4.0 || ^3.1", "php": "^8.0 || ^7.0.8", "symfony/console": "^6.0 || ^5.0 || ^4.0 || ^3.4", "symfony/var-dumper": "^6.0 || ^5.0 || ^4.0 || ^3.4"}, "conflict": {"symfony/console": "4.4.37 || 5.3.14 || 5.3.15 || 5.4.3 || 5.4.4 || 6.0.3 || 6.0.4"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.2"}, "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)", "ext-pdo-sqlite": "The doc command requires SQLite to work.", "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well.", "ext-readline": "Enables support for arrow-key history navigation, and showing and manipulating command history."}, "bin": ["bin/psysh"], "type": "library", "extra": {"branch-alias": {"dev-main": "0.11.x-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Psy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "An interactive shell for modern PHP.", "homepage": "http://psysh.org", "keywords": ["REPL", "console", "interactive", "shell"], "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.11.8"}, "time": "2022-07-28T14:25:11+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "ramsey/collection", "version": "1.2.2", "source": {"type": "git", "url": "https://github.com/ramsey/collection.git", "reference": "cccc74ee5e328031b15640b51056ee8d3bb66c0a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/collection/zipball/cccc74ee5e328031b15640b51056ee8d3bb66c0a", "reference": "cccc74ee5e328031b15640b51056ee8d3bb66c0a", "shasum": ""}, "require": {"php": "^7.3 || ^8", "symfony/polyfill-php81": "^1.23"}, "require-dev": {"captainhook/captainhook": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "ergebnis/composer-normalize": "^2.6", "fakerphp/faker": "^1.5", "hamcrest/hamcrest-php": "^2", "jangregor/phpstan-prophecy": "^0.8", "mockery/mockery": "^1.3", "phpspec/prophecy-phpunit": "^2.0", "phpstan/extension-installer": "^1", "phpstan/phpstan": "^0.12.32", "phpstan/phpstan-mockery": "^0.12.5", "phpstan/phpstan-phpunit": "^0.12.11", "phpunit/phpunit": "^8.5 || ^9", "psy/psysh": "^0.10.4", "slevomat/coding-standard": "^6.3", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.4"}, "type": "library", "autoload": {"psr-4": {"Ramsey\\Collection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}], "description": "A PHP library for representing and manipulating collections.", "keywords": ["array", "collection", "hash", "map", "queue", "set"], "support": {"issues": "https://github.com/ramsey/collection/issues", "source": "https://github.com/ramsey/collection/tree/1.2.2"}, "funding": [{"url": "https://github.com/ramsey", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ramsey/collection", "type": "tidelift"}], "time": "2021-10-10T03:01:02+00:00"}, {"name": "ramsey/uuid", "version": "4.4.0", "source": {"type": "git", "url": "https://github.com/ramsey/uuid.git", "reference": "373f7bacfcf3de038778ff27dcce5672ddbf4c8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/uuid/zipball/373f7bacfcf3de038778ff27dcce5672ddbf4c8a", "reference": "373f7bacfcf3de038778ff27dcce5672ddbf4c8a", "shasum": ""}, "require": {"brick/math": "^0.8 || ^0.9 || ^0.10", "ext-ctype": "*", "ext-json": "*", "php": "^8.0", "ramsey/collection": "^1.0"}, "replace": {"rhumsaa/uuid": "self.version"}, "require-dev": {"captainhook/captainhook": "^5.10", "captainhook/plugin-composer": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "doctrine/annotations": "^1.8", "ergebnis/composer-normalize": "^2.15", "mockery/mockery": "^1.3", "paragonie/random-lib": "^2", "php-mock/php-mock": "^2.2", "php-mock/php-mock-mockery": "^1.3", "php-parallel-lint/php-parallel-lint": "^1.1", "phpbench/phpbench": "^1.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-mockery": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpunit/phpunit": "^8.5 || ^9", "slevomat/coding-standard": "^7.0", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.9"}, "suggest": {"ext-bcmath": "Enables faster math with arbitrary-precision integers using BCMath.", "ext-ctype": "Enables faster processing of character classification using ctype functions.", "ext-gmp": "Enables faster math with arbitrary-precision integers using GMP.", "ext-uuid": "Enables the use of PeclUuidTimeGenerator and PeclUuidRandomGenerator.", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "type": "library", "extra": {"captainhook": {"force-install": true}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Ramsey\\Uuid\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A PHP library for generating and working with universally unique identifiers (UUIDs).", "keywords": ["guid", "identifier", "uuid"], "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.4.0"}, "funding": [{"url": "https://github.com/ramsey", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ramsey/uuid", "type": "tidelift"}], "time": "2022-08-05T17:58:37+00:00"}, {"name": "sabberworm/php-css-parser", "version": "8.4.0", "source": {"type": "git", "url": "https://github.com/sabberworm/PHP-CSS-Parser.git", "reference": "e41d2140031d533348b2192a83f02d8dd8a71d30"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sabberworm/PHP-CSS-Parser/zipball/e41d2140031d533348b2192a83f02d8dd8a71d30", "reference": "e41d2140031d533348b2192a83f02d8dd8a71d30", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=5.6.20"}, "require-dev": {"codacy/coverage": "^1.4", "phpunit/phpunit": "^4.8.36"}, "suggest": {"ext-mbstring": "for parsing UTF-8 CSS"}, "type": "library", "autoload": {"psr-4": {"Sabberworm\\CSS\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Parser for CSS Files written in PHP", "homepage": "https://www.sabberworm.com/blog/2010/6/10/php-css-parser", "keywords": ["css", "parser", "stylesheet"], "support": {"issues": "https://github.com/sabberworm/PHP-CSS-Parser/issues", "source": "https://github.com/sabberworm/PHP-CSS-Parser/tree/8.4.0"}, "time": "2021-12-11T13:40:54+00:00"}, {"name": "spatie/eloquent-sortable", "version": "4.0.1", "source": {"type": "git", "url": "https://github.com/spatie/eloquent-sortable.git", "reference": "64a3365c0d5a7b4a1837b2f29d01ee4c578c416a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/eloquent-sortable/zipball/64a3365c0d5a7b4a1837b2f29d01ee4c578c416a", "reference": "64a3365c0d5a7b4a1837b2f29d01ee4c578c416a", "shasum": ""}, "require": {"illuminate/database": "^8.0|^9.0", "illuminate/support": "^8.0|^9.0", "php": "^8.0", "spatie/laravel-package-tools": "^1.9"}, "require-dev": {"orchestra/testbench": "^6.0|^7.0", "phpunit/phpunit": "^9.5"}, "type": "library", "extra": {"laravel": {"providers": ["Spatie\\EloquentSortable\\EloquentSortableServiceProvider"]}}, "autoload": {"psr-4": {"Spatie\\EloquentSortable\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Sortable behaviour for eloquent models", "homepage": "https://github.com/spatie/eloquent-sortable", "keywords": ["behaviour", "eloquent", "laravel", "model", "sort", "sortable"], "support": {"issues": "https://github.com/spatie/eloquent-sortable/issues", "source": "https://github.com/spatie/eloquent-sortable/tree/4.0.1"}, "funding": [{"url": "https://spatie.be/open-source/support-us", "type": "custom"}, {"url": "https://github.com/spatie", "type": "github"}], "time": "2022-01-21T08:32:41+00:00"}, {"name": "spatie/laravel-feed", "version": "4.1.4", "source": {"type": "git", "url": "https://github.com/spatie/laravel-feed.git", "reference": "8cf053e3ac7ed10363ded43c0c61c54d756b43cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/laravel-feed/zipball/8cf053e3ac7ed10363ded43c0c61c54d756b43cf", "reference": "8cf053e3ac7ed10363ded43c0c61c54d756b43cf", "shasum": ""}, "require": {"illuminate/contracts": "^8.0|^9.0", "illuminate/http": "^8.0|^9.0", "illuminate/support": "^8.0|^9.0", "php": "^8.0", "spatie/laravel-package-tools": "^1.9"}, "require-dev": {"orchestra/testbench": "^6.23|^7.0", "phpunit/phpunit": "^9.5", "spatie/phpunit-snapshot-assertions": "^4.2.7", "spatie/test-time": "^1.2"}, "type": "library", "extra": {"laravel": {"providers": ["Spatie\\Feed\\FeedServiceProvider"]}}, "autoload": {"psr-4": {"Spatie\\Feed\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}, {"name": "<PERSON>", "homepage": "https://github.com/patinthehat", "role": "Developer"}], "description": "Generate rss feeds", "homepage": "https://github.com/spatie/laravel-feed", "keywords": ["laravel", "laravel-feed", "rss", "spatie"], "support": {"source": "https://github.com/spatie/laravel-feed/tree/4.1.4"}, "funding": [{"url": "https://spatie.be/open-source/support-us", "type": "custom"}, {"url": "https://github.com/spatie", "type": "github"}], "time": "2022-05-27T08:51:00+00:00"}, {"name": "spatie/laravel-package-tools", "version": "1.12.1", "source": {"type": "git", "url": "https://github.com/spatie/laravel-package-tools.git", "reference": "09f80fa240d44fafb1c70657c74ee44ffa929357"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/laravel-package-tools/zipball/09f80fa240d44fafb1c70657c74ee44ffa929357", "reference": "09f80fa240d44fafb1c70657c74ee44ffa929357", "shasum": ""}, "require": {"illuminate/contracts": "^7.0|^8.0|^9.0", "php": "^7.4|^8.0"}, "require-dev": {"mockery/mockery": "^1.4", "orchestra/testbench": "^5.0|^6.23|^7.0", "phpunit/phpunit": "^9.4", "spatie/test-time": "^1.2"}, "type": "library", "autoload": {"psr-4": {"Spatie\\LaravelPackageTools\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Tools for creating Laravel packages", "homepage": "https://github.com/spatie/laravel-package-tools", "keywords": ["laravel-package-tools", "spatie"], "support": {"issues": "https://github.com/spatie/laravel-package-tools/issues", "source": "https://github.com/spatie/laravel-package-tools/tree/1.12.1"}, "funding": [{"url": "https://github.com/spatie", "type": "github"}], "time": "2022-06-28T14:29:26+00:00"}, {"name": "spatie/laravel-permission", "version": "5.5.5", "source": {"type": "git", "url": "https://github.com/spatie/laravel-permission.git", "reference": "f2303a70be60919811ca8afc313e8244fda00974"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/laravel-permission/zipball/f2303a70be60919811ca8afc313e8244fda00974", "reference": "f2303a70be60919811ca8afc313e8244fda00974", "shasum": ""}, "require": {"illuminate/auth": "^7.0|^8.0|^9.0", "illuminate/container": "^7.0|^8.0|^9.0", "illuminate/contracts": "^7.0|^8.0|^9.0", "illuminate/database": "^7.0|^8.0|^9.0", "php": "^7.3|^8.0|^8.1"}, "require-dev": {"orchestra/testbench": "^5.0|^6.0|^7.0", "phpunit/phpunit": "^9.4", "predis/predis": "^1.1"}, "type": "library", "extra": {"laravel": {"providers": ["Spatie\\Permission\\PermissionServiceProvider"]}, "branch-alias": {"dev-main": "5.x-dev", "dev-master": "5.x-dev"}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"Spatie\\Permission\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "description": "Permission handling for Laravel 6.0 and up", "homepage": "https://github.com/spatie/laravel-permission", "keywords": ["acl", "laravel", "permission", "permissions", "rbac", "roles", "security", "spatie"], "support": {"issues": "https://github.com/spatie/laravel-permission/issues", "source": "https://github.com/spatie/laravel-permission/tree/5.5.5"}, "funding": [{"url": "https://github.com/spatie", "type": "github"}], "time": "2022-06-29T23:11:42+00:00"}, {"name": "spatie/laravel-query-builder", "version": "5.0.3", "source": {"type": "git", "url": "https://github.com/spatie/laravel-query-builder.git", "reference": "2243e3d60fc184ef20ad2b19765bc7006fa9dbfc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/laravel-query-builder/zipball/2243e3d60fc184ef20ad2b19765bc7006fa9dbfc", "reference": "2243e3d60fc184ef20ad2b19765bc7006fa9dbfc", "shasum": ""}, "require": {"illuminate/database": "^9.0", "illuminate/http": "^9.0", "illuminate/support": "^9.0", "php": "^8.0", "spatie/laravel-package-tools": "^1.11"}, "require-dev": {"ext-json": "*", "mockery/mockery": "^1.4", "orchestra/testbench": "^7.0", "pestphp/pest": "^1.20", "spatie/laravel-ray": "^1.28"}, "type": "library", "extra": {"laravel": {"providers": ["Spatie\\QueryBuilder\\QueryBuilderServiceProvider"]}}, "autoload": {"psr-4": {"Spatie\\QueryBuilder\\": "src", "Spatie\\QueryBuilder\\Database\\Factories\\": "database/factories"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "description": "Easily build Eloquent queries from API requests", "homepage": "https://github.com/spatie/laravel-query-builder", "keywords": ["laravel-query-builder", "spatie"], "support": {"issues": "https://github.com/spatie/laravel-query-builder/issues", "source": "https://github.com/spatie/laravel-query-builder"}, "funding": [{"url": "https://spatie.be/open-source/support-us", "type": "custom"}], "time": "2022-07-29T14:19:59+00:00"}, {"name": "spatie/laravel-translatable", "version": "6.0.0", "source": {"type": "git", "url": "https://github.com/spatie/laravel-translatable.git", "reference": "c4c2bef702738f26562b6dc37b66bdac545610e1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/laravel-translatable/zipball/c4c2bef702738f26562b6dc37b66bdac545610e1", "reference": "c4c2bef702738f26562b6dc37b66bdac545610e1", "shasum": ""}, "require": {"illuminate/database": "^9.0", "illuminate/support": "^9.0", "php": "^8.0", "spatie/laravel-package-tools": "^1.11"}, "require-dev": {"mockery/mockery": "^1.4", "orchestra/testbench": "^7.0"}, "type": "library", "extra": {"laravel": {"providers": ["Spatie\\Translatable\\TranslatableServiceProvider"]}, "aliases": {"Translatable": "Spatie\\Translatable\\Facades\\Translatable"}}, "autoload": {"psr-4": {"Spatie\\Translatable\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "description": "A trait to make an Eloquent model hold translations", "homepage": "https://github.com/spatie/laravel-translatable", "keywords": ["eloquent", "i8n", "laravel-translatable", "model", "multilingual", "spatie", "translate"], "support": {"issues": "https://github.com/spatie/laravel-translatable/issues", "source": "https://github.com/spatie/laravel-translatable/tree/6.0.0"}, "funding": [{"url": "https://github.com/spatie", "type": "github"}], "time": "2022-03-07T13:50:51+00:00"}, {"name": "studioone/arca", "version": "1.0.7", "source": {"type": "git", "url": "ssh://************************:2234/finacnes/arca.git", "reference": "5b5153c95e94e643ae1596c7f36e10f23d12824b"}, "type": "library", "autoload": {"psr-4": {"Studioone\\Arca\\": "src/"}}, "authors": [{"name": "Armen", "email": "<EMAIL>"}], "description": "Arca desc", "time": "2021-03-29T13:42:54+00:00"}, {"name": "symfony/console", "version": "v6.0.11", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "09b8e50f09bf0e5bbde9b61b19d7f53751114725"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/09b8e50f09bf0e5bbde9b61b19d7f53751114725", "reference": "09b8e50f09bf0e5bbde9b61b19d7f53751114725", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/string": "^5.4|^6.0"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/dotenv": "<5.4", "symfony/event-dispatcher": "<5.4", "symfony/lock": "<5.4", "symfony/process": "<5.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/event-dispatcher": "^5.4|^6.0", "symfony/lock": "^5.4|^6.0", "symfony/process": "^5.4|^6.0", "symfony/var-dumper": "^5.4|^6.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v6.0.11"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-22T14:17:38+00:00"}, {"name": "symfony/css-selector", "version": "v6.0.11", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "ab2746acddc4f03a7234c8441822ac5d5c63efe9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/ab2746acddc4f03a7234c8441822ac5d5c63efe9", "reference": "ab2746acddc4f03a7234c8441822ac5d5c63efe9", "shasum": ""}, "require": {"php": ">=8.0.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts CSS selectors to XPath expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/css-selector/tree/v6.0.11"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-06-27T17:10:44+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.0.2", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "26954b3d62a6c5fd0ea8a2a00c0353a14978d05c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/26954b3d62a6c5fd0ea8a2a00c0353a14978d05c", "reference": "26954b3d62a6c5fd0ea8a2a00c0353a14978d05c", "shasum": ""}, "require": {"php": ">=8.0.2"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.0.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:55:41+00:00"}, {"name": "symfony/error-handler", "version": "v6.0.11", "source": {"type": "git", "url": "https://github.com/symfony/error-handler.git", "reference": "cb302377e1b862540436f22be9ff07079a5836ae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/error-handler/zipball/cb302377e1b862540436f22be9ff07079a5836ae", "reference": "cb302377e1b862540436f22be9ff07079a5836ae", "shasum": ""}, "require": {"php": ">=8.0.2", "psr/log": "^1|^2|^3", "symfony/var-dumper": "^5.4|^6.0"}, "require-dev": {"symfony/deprecation-contracts": "^2.1|^3", "symfony/http-kernel": "^5.4|^6.0", "symfony/serializer": "^5.4|^6.0"}, "bin": ["Resources/bin/patch-type-declarations"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ErrorHandler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to manage errors and ease debugging PHP code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/error-handler/tree/v6.0.11"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-29T07:39:48+00:00"}, {"name": "symfony/event-dispatcher", "version": "v6.0.9", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "5c85b58422865d42c6eb46f7693339056db098a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/5c85b58422865d42c6eb46f7693339056db098a8", "reference": "5c85b58422865d42c6eb46f7693339056db098a8", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/event-dispatcher-contracts": "^2|^3"}, "conflict": {"symfony/dependency-injection": "<5.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/error-handler": "^5.4|^6.0", "symfony/expression-language": "^5.4|^6.0", "symfony/http-foundation": "^5.4|^6.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/stopwatch": "^5.4|^6.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.0.9"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-05T16:45:52+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.0.2", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "7bc61cc2db649b4637d331240c5346dcc7708051"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/7bc61cc2db649b4637d331240c5346dcc7708051", "reference": "7bc61cc2db649b4637d331240c5346dcc7708051", "shasum": ""}, "require": {"php": ">=8.0.2", "psr/event-dispatcher": "^1"}, "suggest": {"symfony/event-dispatcher-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.0.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:55:41+00:00"}, {"name": "symfony/finder", "version": "v6.0.11", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "09cb683ba5720385ea6966e5e06be2a34f2568b1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/09cb683ba5720385ea6966e5e06be2a34f2568b1", "reference": "09cb683ba5720385ea6966e5e06be2a34f2568b1", "shasum": ""}, "require": {"php": ">=8.0.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v6.0.11"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-29T07:39:48+00:00"}, {"name": "symfony/http-client", "version": "v6.0.11", "source": {"type": "git", "url": "https://github.com/symfony/http-client.git", "reference": "49bef7df70f84a4f5d516eb268963779ca80320d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client/zipball/49bef7df70f84a4f5d516eb268963779ca80320d", "reference": "49bef7df70f84a4f5d516eb268963779ca80320d", "shasum": ""}, "require": {"php": ">=8.0.2", "psr/log": "^1|^2|^3", "symfony/http-client-contracts": "^3", "symfony/service-contracts": "^1.0|^2|^3"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "3.0"}, "require-dev": {"amphp/amp": "^2.5", "amphp/http-client": "^4.2.1", "amphp/http-tunnel": "^1.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.4", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/http-kernel": "^5.4|^6.0", "symfony/process": "^5.4|^6.0", "symfony/stopwatch": "^5.4|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpClient\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides powerful methods to fetch HTTP resources synchronously or asynchronously", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-client/tree/v6.0.11"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-28T13:39:17+00:00"}, {"name": "symfony/http-client-contracts", "version": "v3.0.2", "source": {"type": "git", "url": "https://github.com/symfony/http-client-contracts.git", "reference": "4184b9b63af1edaf35b6a7974c6f1f9f33294129"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/4184b9b63af1edaf35b6a7974c6f1f9f33294129", "reference": "4184b9b63af1edaf35b6a7974c6f1f9f33294129", "shasum": ""}, "require": {"php": ">=8.0.2"}, "suggest": {"symfony/http-client-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\HttpClient\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to HTTP clients", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v3.0.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-04-12T16:11:42+00:00"}, {"name": "symfony/http-foundation", "version": "v6.0.11", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "69302fb4a7d65f6373c60b6d9ca89b91d2c9e0e6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/69302fb4a7d65f6373c60b6d9ca89b91d2c9e0e6", "reference": "69302fb4a7d65f6373c60b6d9ca89b91d2c9e0e6", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.1"}, "require-dev": {"predis/predis": "~1.0", "symfony/cache": "^5.4|^6.0", "symfony/expression-language": "^5.4|^6.0", "symfony/mime": "^5.4|^6.0"}, "suggest": {"symfony/mime": "To use the file extension guesser"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v6.0.11"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-22T16:13:32+00:00"}, {"name": "symfony/http-kernel", "version": "v6.0.11", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "961268a36f3fa4bda9fde1400d2ae7004318b717"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/961268a36f3fa4bda9fde1400d2ae7004318b717", "reference": "961268a36f3fa4bda9fde1400d2ae7004318b717", "shasum": ""}, "require": {"php": ">=8.0.2", "psr/log": "^1|^2|^3", "symfony/error-handler": "^5.4|^6.0", "symfony/event-dispatcher": "^5.4|^6.0", "symfony/http-foundation": "^5.4|^6.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/browser-kit": "<5.4", "symfony/cache": "<5.4", "symfony/config": "<5.4", "symfony/console": "<5.4", "symfony/dependency-injection": "<5.4", "symfony/doctrine-bridge": "<5.4", "symfony/form": "<5.4", "symfony/http-client": "<5.4", "symfony/mailer": "<5.4", "symfony/messenger": "<5.4", "symfony/translation": "<5.4", "symfony/twig-bridge": "<5.4", "symfony/validator": "<5.4", "twig/twig": "<2.13"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/browser-kit": "^5.4|^6.0", "symfony/config": "^5.4|^6.0", "symfony/console": "^5.4|^6.0", "symfony/css-selector": "^5.4|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/dom-crawler": "^5.4|^6.0", "symfony/expression-language": "^5.4|^6.0", "symfony/finder": "^5.4|^6.0", "symfony/http-client-contracts": "^1.1|^2|^3", "symfony/process": "^5.4|^6.0", "symfony/routing": "^5.4|^6.0", "symfony/stopwatch": "^5.4|^6.0", "symfony/translation": "^5.4|^6.0", "symfony/translation-contracts": "^1.1|^2|^3", "twig/twig": "^2.13|^3.0.4"}, "suggest": {"symfony/browser-kit": "", "symfony/config": "", "symfony/console": "", "symfony/dependency-injection": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a structured process for converting a Request into a Response", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-kernel/tree/v6.0.11"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-29T12:37:40+00:00"}, {"name": "symfony/mailer", "version": "v6.0.11", "source": {"type": "git", "url": "https://github.com/symfony/mailer.git", "reference": "4a787a257addd412eac53157d459f87f8e335037"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mailer/zipball/4a787a257addd412eac53157d459f87f8e335037", "reference": "4a787a257addd412eac53157d459f87f8e335037", "shasum": ""}, "require": {"egulias/email-validator": "^2.1.10|^3", "php": ">=8.0.2", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^5.4|^6.0", "symfony/mime": "^5.4|^6.0", "symfony/service-contracts": "^1.1|^2|^3"}, "conflict": {"symfony/http-kernel": "<5.4"}, "require-dev": {"symfony/http-client-contracts": "^1.1|^2|^3", "symfony/messenger": "^5.4|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mailer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps sending emails", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/mailer/tree/v6.0.11"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-27T15:50:26+00:00"}, {"name": "symfony/mailgun-mailer", "version": "v6.0.7", "source": {"type": "git", "url": "https://github.com/symfony/mailgun-mailer.git", "reference": "f0d032c26683b26f4bc26864e09b1e08fa55226e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mailgun-mailer/zipball/f0d032c26683b26f4bc26864e09b1e08fa55226e", "reference": "f0d032c26683b26f4bc26864e09b1e08fa55226e", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/mailer": "^5.4|^6.0"}, "require-dev": {"symfony/http-client": "^5.4|^6.0"}, "type": "symfony-mailer-bridge", "autoload": {"psr-4": {"Symfony\\Component\\Mailer\\Bridge\\Mailgun\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Mailgun Mailer Bridge", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/mailgun-mailer/tree/v6.0.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-03-24T17:11:42+00:00"}, {"name": "symfony/mime", "version": "v6.0.11", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "c6f16f6789587348f6518b193d3499c0e1f5e5c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/c6f16f6789587348f6518b193d3499c0e1f5e5c5", "reference": "c6f16f6789587348f6518b193d3499c0e1f5e5c5", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<5.4"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/property-access": "^5.4|^6.0", "symfony/property-info": "^5.4|^6.0", "symfony/serializer": "^5.4|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v6.0.11"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-20T13:45:53+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "6fd1b9a79f6e3cf65f9e679b23af304cd9e010d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/6fd1b9a79f6e3cf65f9e679b23af304cd9e010d4", "reference": "6fd1b9a79f6e3cf65f9e679b23af304cd9e010d4", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.26.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "433d05519ce6990bf3530fba6957499d327395c2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/433d05519ce6990bf3530fba6957499d327395c2", "reference": "433d05519ce6990bf3530fba6957499d327395c2", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.26.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "59a8d271f00dd0e4c2e518104cc7963f655a1aa8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/59a8d271f00dd0e4c2e518104cc7963f655a1aa8", "reference": "59a8d271f00dd0e4c2e518104cc7963f655a1aa8", "shasum": ""}, "require": {"php": ">=7.1", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.26.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "219aa369ceff116e673852dce47c3a41794c14bd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/219aa369ceff116e673852dce47c3a41794c14bd", "reference": "219aa369ceff116e673852dce47c3a41794c14bd", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.26.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "9344f9cb97f3b19424af1a21a3b0e75b0a7d8d7e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/9344f9cb97f3b19424af1a21a3b0e75b0a7d8d7e", "reference": "9344f9cb97f3b19424af1a21a3b0e75b0a7d8d7e", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.26.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "bf44a9fd41feaac72b074de600314a93e2ae78e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/bf44a9fd41feaac72b074de600314a93e2ae78e2", "reference": "bf44a9fd41feaac72b074de600314a93e2ae78e2", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php72\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php72/tree/v1.26.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "cfa0ae98841b9e461207c13ab093d76b0fa7bace"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/cfa0ae98841b9e461207c13ab093d76b0fa7bace", "reference": "cfa0ae98841b9e461207c13ab093d76b0fa7bace", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.26.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-10T07:21:04+00:00"}, {"name": "symfony/polyfill-php81", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php81.git", "reference": "13f6d1271c663dc5ae9fb843a8f16521db7687a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php81/zipball/13f6d1271c663dc5ae9fb843a8f16521db7687a1", "reference": "13f6d1271c663dc5ae9fb843a8f16521db7687a1", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php81/tree/v1.26.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/process", "version": "v6.0.11", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "44270a08ccb664143dede554ff1c00aaa2247a43"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/44270a08ccb664143dede554ff1c00aaa2247a43", "reference": "44270a08ccb664143dede554ff1c00aaa2247a43", "shasum": ""}, "require": {"php": ">=8.0.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v6.0.11"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-06-27T17:10:44+00:00"}, {"name": "symfony/routing", "version": "v6.0.11", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "434b64f7d3a582ec33fcf69baaf085473e67d639"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/434b64f7d3a582ec33fcf69baaf085473e67d639", "reference": "434b64f7d3a582ec33fcf69baaf085473e67d639", "shasum": ""}, "require": {"php": ">=8.0.2"}, "conflict": {"doctrine/annotations": "<1.12", "symfony/config": "<5.4", "symfony/dependency-injection": "<5.4", "symfony/yaml": "<5.4"}, "require-dev": {"doctrine/annotations": "^1.12", "psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/expression-language": "^5.4|^6.0", "symfony/http-foundation": "^5.4|^6.0", "symfony/yaml": "^5.4|^6.0"}, "suggest": {"symfony/config": "For using the all-in-one router or any loader", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Maps an HTTP request to a set of configuration variables", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "support": {"source": "https://github.com/symfony/routing/tree/v6.0.11"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-20T13:45:53+00:00"}, {"name": "symfony/service-contracts", "version": "v3.0.2", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "d78d39c1599bd1188b8e26bb341da52c3c6d8a66"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/d78d39c1599bd1188b8e26bb341da52c3c6d8a66", "reference": "d78d39c1599bd1188b8e26bb341da52c3c6d8a66", "shasum": ""}, "require": {"php": ">=8.0.2", "psr/container": "^2.0"}, "conflict": {"ext-psr": "<1.1|>=2"}, "suggest": {"symfony/service-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.0.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-30T19:17:58+00:00"}, {"name": "symfony/string", "version": "v6.0.11", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "042b6bf0f6ccca6d456a0572eb788cfb8b1ff809"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/042b6bf0f6ccca6d456a0572eb788cfb8b1ff809", "reference": "042b6bf0f6ccca6d456a0572eb788cfb8b1ff809", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.0"}, "require-dev": {"symfony/error-handler": "^5.4|^6.0", "symfony/http-client": "^5.4|^6.0", "symfony/translation-contracts": "^2.0|^3.0", "symfony/var-exporter": "^5.4|^6.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v6.0.11"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-27T15:50:26+00:00"}, {"name": "symfony/translation", "version": "v6.0.11", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "55ffbe4b690156100af1ae42e1f94c5873085bca"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/55ffbe4b690156100af1ae42e1f94c5873085bca", "reference": "55ffbe4b690156100af1ae42e1f94c5873085bca", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^2.3|^3.0"}, "conflict": {"symfony/config": "<5.4", "symfony/console": "<5.4", "symfony/dependency-injection": "<5.4", "symfony/http-kernel": "<5.4", "symfony/twig-bundle": "<5.4", "symfony/yaml": "<5.4"}, "provide": {"symfony/translation-implementation": "2.3|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0", "symfony/console": "^5.4|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/finder": "^5.4|^6.0", "symfony/http-client-contracts": "^1.1|^2.0|^3.0", "symfony/http-kernel": "^5.4|^6.0", "symfony/intl": "^5.4|^6.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/service-contracts": "^1.1.2|^2|^3", "symfony/yaml": "^5.4|^6.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v6.0.11"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-20T13:45:53+00:00"}, {"name": "symfony/translation-contracts", "version": "v3.0.2", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "acbfbb274e730e5a0236f619b6168d9dedb3e282"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/acbfbb274e730e5a0236f619b6168d9dedb3e282", "reference": "acbfbb274e730e5a0236f619b6168d9dedb3e282", "shasum": ""}, "require": {"php": ">=8.0.2"}, "suggest": {"symfony/translation-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.0.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-06-27T17:10:44+00:00"}, {"name": "symfony/var-dumper", "version": "v6.0.11", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "2672bdc01c1971e3d8879ce153ec4c3621be5f07"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/2672bdc01c1971e3d8879ce153ec4c3621be5f07", "reference": "2672bdc01c1971e3d8879ce153ec4c3621be5f07", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"phpunit/phpunit": "<5.4.3", "symfony/console": "<5.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^5.4|^6.0", "symfony/process": "^5.4|^6.0", "symfony/uid": "^5.4|^6.0", "twig/twig": "^2.13|^3.0.4"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v6.0.11"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-20T13:45:53+00:00"}, {"name": "tijsverkoyen/css-to-inline-styles", "version": "2.2.4", "source": {"type": "git", "url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "reference": "da444caae6aca7a19c0c140f68c6182e337d5b1c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/da444caae6aca7a19c0c140f68c6182e337d5b1c", "reference": "da444caae6aca7a19c0c140f68c6182e337d5b1c", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^5.5 || ^7.0 || ^8.0", "symfony/css-selector": "^2.7 || ^3.0 || ^4.0 || ^5.0 || ^6.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0 || ^7.5 || ^8.5.21 || ^9.5.10"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}}, "autoload": {"psr-4": {"TijsVerkoyen\\CssToInlineStyles\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "CssToInlineStyles is a class that enables you to convert HTML-pages/files into HTML-pages/files with inline styles. This is very useful when you're sending emails.", "homepage": "https://github.com/tijsverkoyen/CssToInlineStyles", "support": {"issues": "https://github.com/tijsverkoyen/CssToInlineStyles/issues", "source": "https://github.com/tijsverkoyen/CssToInlineStyles/tree/2.2.4"}, "time": "2021-12-08T09:12:39+00:00"}, {"name": "typicms/bootforms", "version": "4.0.0", "source": {"type": "git", "url": "https://github.com/TypiCMS/bootforms.git", "reference": "5f291ad7017b1a5262e8717c3573dc1c9808f88a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TypiCMS/bootforms/zipball/5f291ad7017b1a5262e8717c3573dc1c9808f88a", "reference": "5f291ad7017b1a5262e8717c3573dc1c9808f88a", "shasum": ""}, "require": {"php": "^8.0.2", "typicms/form": "^3.0.0"}, "require-dev": {"mockery/mockery": "^1.4.4", "nunomaduro/larastan": "^2.0", "orchestra/testbench": "^7.4", "php-coveralls/php-coveralls": "~2.5.2", "phpunit/phpunit": "^9.5.10"}, "type": "library", "extra": {"laravel": {"providers": ["TypiCMS\\BootForms\\BootFormsServiceProvider"]}}, "autoload": {"psr-4": {"TypiCMS\\BootForms\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Just a Formbuilder with some Bootstrap 5 specific conveniences. Remembers old input, retrieves error messages and handles all your boilerplate Bootstrap markup automatically.", "support": {"issues": "https://github.com/TypiCMS/bootforms/issues", "source": "https://github.com/TypiCMS/bootforms/tree/4.0.0"}, "funding": [{"url": "https://github.com/typicms", "type": "github"}], "time": "2022-04-26T07:49:23+00:00"}, {"name": "typicms/core", "version": "10.0.20", "source": {"type": "git", "url": "https://github.com/TypiCMS/Core.git", "reference": "715f7992cbd5752daefac2d0ac7eff2e16a51193"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TypiCMS/Core/zipball/715f7992cbd5752daefac2d0ac7eff2e16a51193", "reference": "715f7992cbd5752daefac2d0ac7eff2e16a51193", "shasum": ""}, "require": {"laravel/framework": "~9.0"}, "type": "library", "autoload": {"psr-4": {"TypiCMS\\Modules\\Core\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "support": {"issues": "https://github.com/TypiCMS/Core/issues", "source": "https://github.com/TypiCMS/Core/tree/10.0.20"}, "funding": [{"url": "https://github.com/typicms", "type": "github"}], "time": "2022-07-14T19:15:03+00:00"}, {"name": "typicms/form", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/TypiCMS/form.git", "reference": "5a1296f8441a25eeacef7df63496693b32503f7d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TypiCMS/form/zipball/5a1296f8441a25eeacef7df63496693b32503f7d", "reference": "5a1296f8441a25eeacef7df63496693b32503f7d", "shasum": ""}, "require": {"php": "^8.0.2"}, "require-dev": {"illuminate/support": ">=9.0", "mockery/mockery": "^1.4.4", "nunomaduro/larastan": "^2.0", "orchestra/testbench": "^7.4", "php-coveralls/php-coveralls": "~2.5.2", "phpunit/php-code-coverage": "^9.2", "phpunit/phpunit": "^9.5.10"}, "type": "library", "extra": {"laravel": {"providers": ["TypiCMS\\Form\\FormServiceProvider"]}}, "autoload": {"psr-4": {"TypiCMS\\Form\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A basic framework agnostic form building package with a few extra niceties like remembering old input and retrieving error messages.", "support": {"issues": "https://github.com/TypiCMS/form/issues", "source": "https://github.com/TypiCMS/form/tree/3.0.0"}, "funding": [{"url": "https://github.com/typicms", "type": "github"}], "time": "2022-04-26T07:54:16+00:00"}, {"name": "typicms/laravel-translatable-bootforms", "version": "7.0.0", "source": {"type": "git", "url": "https://github.com/TypiCMS/Laravel-Translatable-Bootforms.git", "reference": "0189c6baded6c80a922d1dc38129f3f35eacf6aa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TypiCMS/Laravel-Translatable-Bootforms/zipball/0189c6baded6c80a922d1dc38129f3f35eacf6aa", "reference": "0189c6baded6c80a922d1dc38129f3f35eacf6aa", "shasum": ""}, "require": {"illuminate/support": "~9.2", "php": "^8.0.2", "spatie/laravel-translatable": "^6.0", "typicms/bootforms": "^4.0.0", "typicms/form": "^3.0.0"}, "require-dev": {"nunomaduro/larastan": "^2.0", "orchestra/testbench": "~7.4.0", "php-coveralls/php-coveralls": "~2.5.2", "phpunit/phpunit": "^9.5.10"}, "type": "library", "extra": {"laravel": {"providers": ["TypiCMS\\LaravelTranslatableBootForms\\TranslatableBootFormsServiceProvider"]}}, "autoload": {"psr-4": {"TypiCMS\\LaravelTranslatableBootForms\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Propaganistas", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Empowers typicms/bootforms with spatie/laravel-translatable.", "keywords": ["bootforms", "form", "i18n", "laravel", "translatable"], "support": {"issues": "https://github.com/TypiCMS/Laravel-Translatable-Bootforms/issues", "source": "https://github.com/TypiCMS/Laravel-Translatable-Bootforms/tree/7.0.0"}, "funding": [{"url": "https://github.com/typicms", "type": "github"}], "time": "2022-04-26T07:57:39+00:00"}, {"name": "typicms/nestablecollection", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/TypiCMS/NestableCollection.git", "reference": "046b150b5154a943a504bdac195f174a7551a31f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TypiCMS/NestableCollection/zipball/046b150b5154a943a504bdac195f174a7551a31f", "reference": "046b150b5154a943a504bdac195f174a7551a31f", "shasum": ""}, "require": {"illuminate/database": "~9.0", "illuminate/support": "~9.0"}, "require-dev": {"illuminate/database": "~9.0", "illuminate/support": "~9.0", "nunomaduro/larastan": "^2.0", "orchestra/testbench": "^7.4"}, "type": "library", "autoload": {"psr-4": {"TypiCMS\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A Laravel Package that extends Collection to handle unlimited nested items following adjacency list model.", "keywords": ["TypiCMS", "adjacency list", "collection", "eloquent", "laravel", "nested set", "tree"], "support": {"issues": "https://github.com/TypiCMS/NestableCollection/issues", "source": "https://github.com/TypiCMS/NestableCollection/tree/2.0.0"}, "funding": [{"url": "https://github.com/typicms", "type": "github"}], "time": "2022-04-26T07:07:44+00:00"}, {"name": "typicms/things", "version": "10.0.2", "source": {"type": "git", "url": "https://github.com/TypiCMS/Things.git", "reference": "b85cbb60ac0e83ba1aa1e2a1da97051ec11878fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TypiCMS/Things/zipball/b85cbb60ac0e83ba1aa1e2a1da97051ec11878fc", "reference": "b85cbb60ac0e83ba1aa1e2a1da97051ec11878fc", "shasum": ""}, "require": {"laravel/framework": "~9.0"}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "support": {"issues": "https://github.com/TypiCMS/Things/issues", "source": "https://github.com/TypiCMS/Things/tree/10.0.2"}, "funding": [{"url": "https://github.com/typicms", "type": "github"}], "time": "2022-07-12T16:03:12+00:00"}, {"name": "typidesign/laravel-artisan-translations", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/typidesign/laravel-artisan-translations.git", "reference": "a9f0a6dab980c19f899ec8952a431000140befb6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/typidesign/laravel-artisan-translations/zipball/a9f0a6dab980c19f899ec8952a431000140befb6", "reference": "a9f0a6dab980c19f899ec8952a431000140befb6", "shasum": ""}, "require": {"illuminate/console": "^9.0", "illuminate/filesystem": "^9.0", "php": "^8.0.2"}, "type": "library", "extra": {"laravel": {"providers": ["Typidesign\\Translations\\ArtisanTranslationsServiceProvider"]}}, "autoload": {"psr-4": {"Typidesign\\Translations\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://typi.be", "role": "Developer"}], "description": "An artisan command to update json translation files.", "homepage": "https://github.com/typidesign/laravel-artisan-translations", "keywords": ["laravel-artisan-translations", "typidesign"], "support": {"issues": "https://github.com/typidesign/laravel-artisan-translations/issues", "source": "https://github.com/typidesign/laravel-artisan-translations/tree/2.0.0"}, "time": "2022-04-20T10:11:54+00:00"}, {"name": "ultrono/laravel-sitemap", "version": "9.1.0", "source": {"type": "git", "url": "https://github.com/ultrono/laravel-sitemap.git", "reference": "27489bd4d0bfa6e017dad143bc1c3d120d1cd8de"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ultrono/laravel-sitemap/zipball/27489bd4d0bfa6e017dad143bc1c3d120d1cd8de", "reference": "27489bd4d0bfa6e017dad143bc1c3d120d1cd8de", "shasum": ""}, "require": {"illuminate/filesystem": "^8.0 || ^9.0", "illuminate/support": "^8.0 || ^9.0", "php": "^8.0"}, "require-dev": {"laravel/framework": "^8.0 || ^9.0", "orchestra/testbench-core": "^6.0 || ^7.0", "php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"laravel": {"providers": ["Ultrono\\Sitemap\\SitemapServiceProvider"]}}, "autoload": {"psr-0": {"Ultrono\\Sitemap": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://darumen.com", "role": "Original Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://f9web.co.uk", "role": "Developer"}], "description": "PHP 8 sitemap generator for Laravel 8 and 9", "keywords": ["Sitemap", "generator", "google-news", "html", "laravel", "php", "xml"], "support": {"issues": "https://github.com/ultrono/laravel-sitemap/issues", "source": "https://github.com/ultrono/laravel-sitemap"}, "time": "2022-03-11T10:11:30+00:00"}, {"name": "vlucas/phpdotenv", "version": "v5.4.1", "source": {"type": "git", "url": "https://github.com/vlucas/phpdotenv.git", "reference": "264dce589e7ce37a7ba99cb901eed8249fbec92f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/264dce589e7ce37a7ba99cb901eed8249fbec92f", "reference": "264dce589e7ce37a7ba99cb901eed8249fbec92f", "shasum": ""}, "require": {"ext-pcre": "*", "graham-campbell/result-type": "^1.0.2", "php": "^7.1.3 || ^8.0", "phpoption/phpoption": "^1.8", "symfony/polyfill-ctype": "^1.23", "symfony/polyfill-mbstring": "^1.23.1", "symfony/polyfill-php80": "^1.23.1"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "ext-filter": "*", "phpunit/phpunit": "^7.5.20 || ^8.5.21 || ^9.5.10"}, "suggest": {"ext-filter": "Required to use the boolean validator."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/vlucas"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v5.4.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/vlucas/phpdotenv", "type": "tidelift"}], "time": "2021-12-12T23:22:04+00:00"}, {"name": "voku/portable-ascii", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/voku/portable-ascii.git", "reference": "b56450eed252f6801410d810c8e1727224ae0743"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/portable-ascii/zipball/b56450eed252f6801410d810c8e1727224ae0743", "reference": "b56450eed252f6801410d810c8e1727224ae0743", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"phpunit/phpunit": "~6.0 || ~7.0 || ~9.0"}, "suggest": {"ext-intl": "Use Intl for transliterator_transliterate() support"}, "type": "library", "autoload": {"psr-4": {"voku\\": "src/voku/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://www.moelleken.org/"}], "description": "Portable ASCII library - performance optimized (ascii) string functions for php.", "homepage": "https://github.com/voku/portable-ascii", "keywords": ["ascii", "clean", "php"], "support": {"issues": "https://github.com/voku/portable-ascii/issues", "source": "https://github.com/voku/portable-ascii/tree/2.0.1"}, "funding": [{"url": "https://www.paypal.me/moelleken", "type": "custom"}, {"url": "https://github.com/voku", "type": "github"}, {"url": "https://opencollective.com/portable-ascii", "type": "open_collective"}, {"url": "https://www.patreon.com/voku", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/voku/portable-ascii", "type": "tidelift"}], "time": "2022-03-08T17:03:00+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "time": "2022-06-03T18:03:27+00:00"}], "packages-dev": [{"name": "barryvdh/laravel-debugbar", "version": "v3.7.0", "source": {"type": "git", "url": "https://github.com/barryvdh/laravel-debugbar.git", "reference": "3372ed65e6d2039d663ed19aa699956f9d346271"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/barryvdh/laravel-debugbar/zipball/3372ed65e6d2039d663ed19aa699956f9d346271", "reference": "3372ed65e6d2039d663ed19aa699956f9d346271", "shasum": ""}, "require": {"illuminate/routing": "^7|^8|^9", "illuminate/session": "^7|^8|^9", "illuminate/support": "^7|^8|^9", "maximebf/debugbar": "^1.17.2", "php": ">=7.2.5", "symfony/finder": "^5|^6"}, "require-dev": {"mockery/mockery": "^1.3.3", "orchestra/testbench-dusk": "^5|^6|^7", "phpunit/phpunit": "^8.5|^9.0", "squizlabs/php_codesniffer": "^3.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.6-dev"}, "laravel": {"providers": ["Barryvdh\\Debugbar\\ServiceProvider"], "aliases": {"Debugbar": "Barryvdh\\Debugbar\\Facades\\Debugbar"}}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"Barryvdh\\Debugbar\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Barry vd. Heuvel", "email": "<EMAIL>"}], "description": "PHP Debugbar integration for Laravel", "keywords": ["debug", "debugbar", "laravel", "profiler", "webprofiler"], "support": {"issues": "https://github.com/barryvdh/laravel-debugbar/issues", "source": "https://github.com/barryvdh/laravel-debugbar/tree/v3.7.0"}, "funding": [{"url": "https://fruitcake.nl", "type": "custom"}, {"url": "https://github.com/barryvdh", "type": "github"}], "time": "2022-07-11T09:26:42+00:00"}, {"name": "doctrine/instantiator", "version": "1.4.1", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "10dcfce151b967d20fde1b34ae6640712c3891bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/10dcfce151b967d20fde1b34ae6640712c3891bc", "reference": "10dcfce151b967d20fde1b34ae6640712c3891bc", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.16 || ^1", "phpstan/phpstan": "^1.4", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.22"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.4.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2022-03-03T08:28:38+00:00"}, {"name": "facade/ignition-contracts", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/facade/ignition-contracts.git", "reference": "3c921a1cdba35b68a7f0ccffc6dffc1995b18267"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/facade/ignition-contracts/zipball/3c921a1cdba35b68a7f0ccffc6dffc1995b18267", "reference": "3c921a1cdba35b68a7f0ccffc6dffc1995b18267", "shasum": ""}, "require": {"php": "^7.3|^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^v2.15.8", "phpunit/phpunit": "^9.3.11", "vimeo/psalm": "^3.17.1"}, "type": "library", "autoload": {"psr-4": {"Facade\\IgnitionContracts\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://flareapp.io", "role": "Developer"}], "description": "Solution contracts for Ignition", "homepage": "https://github.com/facade/ignition-contracts", "keywords": ["contracts", "flare", "ignition"], "support": {"issues": "https://github.com/facade/ignition-contracts/issues", "source": "https://github.com/facade/ignition-contracts/tree/1.0.2"}, "time": "2020-10-16T08:27:54+00:00"}, {"name": "fakerphp/faker", "version": "v1.20.0", "source": {"type": "git", "url": "https://github.com/FakerPHP/Faker.git", "reference": "37f751c67a5372d4e26353bd9384bc03744ec77b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FakerPHP/Faker/zipball/37f751c67a5372d4e26353bd9384bc03744ec77b", "reference": "37f751c67a5372d4e26353bd9384bc03744ec77b", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "psr/container": "^1.0 || ^2.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "conflict": {"fzaninotto/faker": "*"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "doctrine/persistence": "^1.3 || ^2.0", "ext-intl": "*", "symfony/phpunit-bridge": "^4.4 || ^5.2"}, "suggest": {"doctrine/orm": "Required to use Faker\\ORM\\Doctrine", "ext-curl": "Required by Faker\\Provider\\Image to download images.", "ext-dom": "Required by Faker\\Provider\\HtmlLorem for generating random HTML.", "ext-iconv": "Required by Faker\\Provider\\ru_RU\\Text::realText() for generating real Russian text.", "ext-mbstring": "Required for multibyte Unicode string functionality."}, "type": "library", "extra": {"branch-alias": {"dev-main": "v1.20-dev"}}, "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["data", "faker", "fixtures"], "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.20.0"}, "time": "2022-07-20T13:12:54+00:00"}, {"name": "filp/whoops", "version": "2.14.5", "source": {"type": "git", "url": "https://github.com/filp/whoops.git", "reference": "a63e5e8f26ebbebf8ed3c5c691637325512eb0dc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/filp/whoops/zipball/a63e5e8f26ebbebf8ed3c5c691637325512eb0dc", "reference": "a63e5e8f26ebbebf8ed3c5c691637325512eb0dc", "shasum": ""}, "require": {"php": "^5.5.9 || ^7.0 || ^8.0", "psr/log": "^1.0.1 || ^2.0 || ^3.0"}, "require-dev": {"mockery/mockery": "^0.9 || ^1.0", "phpunit/phpunit": "^4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.3", "symfony/var-dumper": "^2.6 || ^3.0 || ^4.0 || ^5.0"}, "suggest": {"symfony/var-dumper": "Pretty print complex values better with var-dumper available", "whoops/soap": "Formats errors as SOAP responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Whoops\\": "src/Whoops/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://github.com/filp", "role": "Developer"}], "description": "php error handling for cool kids", "homepage": "https://filp.github.io/whoops/", "keywords": ["error", "exception", "handling", "library", "throwable", "whoops"], "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.14.5"}, "funding": [{"url": "https://github.com/denis-so<PERSON><PERSON>", "type": "github"}], "time": "2022-01-07T12:00:00+00:00"}, {"name": "hamcrest/hamcrest-php", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/hamcrest/hamcrest-php.git", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hamcrest/hamcrest-php/zipball/8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "shasum": ""}, "require": {"php": "^5.3|^7.0|^8.0"}, "replace": {"cordoval/hamcrest-php": "*", "davedevelopment/hamcrest-php": "*", "kodova/hamcrest-php": "*"}, "require-dev": {"phpunit/php-file-iterator": "^1.4 || ^2.0", "phpunit/phpunit": "^4.8.36 || ^5.7 || ^6.5 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["hamcrest"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "This is the PHP port of Hamcrest Matchers", "keywords": ["test"], "support": {"issues": "https://github.com/hamcrest/hamcrest-php/issues", "source": "https://github.com/hamcrest/hamcrest-php/tree/v2.0.1"}, "time": "2020-07-09T08:09:16+00:00"}, {"name": "maximebf/debugbar", "version": "v1.18.0", "source": {"type": "git", "url": "https://github.com/maximebf/php-debugbar.git", "reference": "0d44b75f3b5d6d41ae83b79c7a4bceae7fbc78b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maximebf/php-debugbar/zipball/0d44b75f3b5d6d41ae83b79c7a4bceae7fbc78b6", "reference": "0d44b75f3b5d6d41ae83b79c7a4bceae7fbc78b6", "shasum": ""}, "require": {"php": "^7.1|^8", "psr/log": "^1|^2|^3", "symfony/var-dumper": "^2.6|^3|^4|^5|^6"}, "require-dev": {"phpunit/phpunit": "^7.5.20 || ^9.4.2", "twig/twig": "^1.38|^2.7|^3.0"}, "suggest": {"kriswallsmith/assetic": "The best way to manage assets", "monolog/monolog": "Log using Monolog", "predis/predis": "Redis storage"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"psr-4": {"DebugBar\\": "src/DebugBar/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "maxime.bouroume<PERSON>@gmail.com", "homepage": "http://maximebf.com"}, {"name": "Barry vd. Heuvel", "email": "<EMAIL>"}], "description": "Debug bar in the browser for php application", "homepage": "https://github.com/maximebf/php-debugbar", "keywords": ["debug", "debugbar"], "support": {"issues": "https://github.com/maximebf/php-debugbar/issues", "source": "https://github.com/maximebf/php-debugbar/tree/v1.18.0"}, "time": "2021-12-27T18:49:48+00:00"}, {"name": "mockery/mockery", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/mockery/mockery.git", "reference": "c10a5f6e06fc2470ab1822fa13fa2a7380f8fbac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mockery/mockery/zipball/c10a5f6e06fc2470ab1822fa13fa2a7380f8fbac", "reference": "c10a5f6e06fc2470ab1822fa13fa2a7380f8fbac", "shasum": ""}, "require": {"hamcrest/hamcrest-php": "^2.0.1", "lib-pcre": ">=7.0", "php": "^7.3 || ^8.0"}, "conflict": {"phpunit/phpunit": "<8.0"}, "require-dev": {"phpunit/phpunit": "^8.5 || ^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-0": {"Mockery": "library/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://blog.astrumfutura.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://davedevelopment.co.uk"}], "description": "Mockery is a simple yet flexible PHP mock object framework", "homepage": "https://github.com/mockery/mockery", "keywords": ["BDD", "TDD", "library", "mock", "mock objects", "mockery", "stub", "test", "test double", "testing"], "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/1.5.0"}, "time": "2022-01-20T13:18:17+00:00"}, {"name": "myclabs/deep-copy", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "14daed4296fae74d9e3201d2c4925d1acb7aa614"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/14daed4296fae74d9e3201d2c4925d1acb7aa614", "reference": "14daed4296fae74d9e3201d2c4925d1acb7aa614", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3,<3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.11.0"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2022-03-03T13:19:32+00:00"}, {"name": "nunomaduro/collision", "version": "v6.2.1", "source": {"type": "git", "url": "https://github.com/nunomaduro/collision.git", "reference": "5f058f7e39278b701e455b3c82ec5298cf001d89"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nunomaduro/collision/zipball/5f058f7e39278b701e455b3c82ec5298cf001d89", "reference": "5f058f7e39278b701e455b3c82ec5298cf001d89", "shasum": ""}, "require": {"facade/ignition-contracts": "^1.0.2", "filp/whoops": "^2.14.5", "php": "^8.0.0", "symfony/console": "^6.0.2"}, "require-dev": {"brianium/paratest": "^6.4.1", "laravel/framework": "^9.7", "laravel/pint": "^0.2.1", "nunomaduro/larastan": "^1.0.2", "nunomaduro/mock-final-classes": "^1.1.0", "orchestra/testbench": "^7.3.0", "phpunit/phpunit": "^9.5.11"}, "type": "library", "extra": {"branch-alias": {"dev-develop": "6.x-dev"}, "laravel": {"providers": ["NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider"]}}, "autoload": {"psr-4": {"NunoMaduro\\Collision\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Cli error handling for console/command-line PHP applications.", "keywords": ["artisan", "cli", "command-line", "console", "error", "handling", "laravel", "laravel-zero", "php", "symfony"], "support": {"issues": "https://github.com/nunomaduro/collision/issues", "source": "https://github.com/nunomaduro/collision"}, "funding": [{"url": "https://www.paypal.com/paypalme/enunomaduro", "type": "custom"}, {"url": "https://github.com/nunomaduro", "type": "github"}, {"url": "https://www.patreon.com/nunomaduro", "type": "patreon"}], "time": "2022-06-27T16:11:16+00:00"}, {"name": "phar-io/manifest", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "97803eca37d319dfa7826cc2437fc020857acb53"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/97803eca37d319dfa7826cc2437fc020857acb53", "reference": "97803eca37d319dfa7826cc2437fc020857acb53", "shasum": ""}, "require": {"ext-dom": "*", "ext-phar": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1", "php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/2.0.3"}, "time": "2021-07-20T11:28:43+00:00"}, {"name": "phar-io/version", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.2.1"}, "time": "2022-02-21T01:04:05+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "support": {"issues": "https://github.com/phpDocumentor/ReflectionCommon/issues", "source": "https://github.com/phpDocumentor/ReflectionCommon/tree/2.x"}, "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.3.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "622548b623e81ca6d78b721c5e029f4ce664f170"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/622548b623e81ca6d78b721c5e029f4ce664f170", "reference": "622548b623e81ca6d78b721c5e029f4ce664f170", "shasum": ""}, "require": {"ext-filter": "*", "php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.2", "phpdocumentor/type-resolver": "^1.3", "webmozart/assert": "^1.9.1"}, "require-dev": {"mockery/mockery": "~1.3.2", "psalm/phar": "^4.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.3.0"}, "time": "2021-10-19T17:43:47+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.6.1", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "77a32518733312af16a44300404e945338981de3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/77a32518733312af16a44300404e945338981de3", "reference": "77a32518733312af16a44300404e945338981de3", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.0"}, "require-dev": {"ext-tokenizer": "*", "psalm/phar": "^4.8"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.6.1"}, "time": "2022-03-15T21:29:03+00:00"}, {"name": "phpspec/prophecy", "version": "v1.15.0", "source": {"type": "git", "url": "https://github.com/phpspec/prophecy.git", "reference": "bbcd7380b0ebf3961ee21409db7b38bc31d69a13"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/prophecy/zipball/bbcd7380b0ebf3961ee21409db7b38bc31d69a13", "reference": "bbcd7380b0ebf3961ee21409db7b38bc31d69a13", "shasum": ""}, "require": {"doctrine/instantiator": "^1.2", "php": "^7.2 || ~8.0, <8.2", "phpdocumentor/reflection-docblock": "^5.2", "sebastian/comparator": "^3.0 || ^4.0", "sebastian/recursion-context": "^3.0 || ^4.0"}, "require-dev": {"phpspec/phpspec": "^6.0 || ^7.0", "phpunit/phpunit": "^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Prophecy\\": "src/Prophecy"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Highly opinionated mocking framework for PHP 5.3+", "homepage": "https://github.com/phpspec/prophecy", "keywords": ["Double", "Dummy", "fake", "mock", "spy", "stub"], "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/v1.15.0"}, "time": "2021-12-08T12:19:24+00:00"}, {"name": "phpunit/php-code-coverage", "version": "9.2.15", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "2e9da11878c4202f97915c1cb4bb1ca318a63f5f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/2e9da11878c4202f97915c1cb4bb1ca318a63f5f", "reference": "2e9da11878c4202f97915c1cb4bb1ca318a63f5f", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.13.0", "php": ">=7.3", "phpunit/php-file-iterator": "^3.0.3", "phpunit/php-text-template": "^2.0.2", "sebastian/code-unit-reverse-lookup": "^2.0.2", "sebastian/complexity": "^2.0", "sebastian/environment": "^5.1.2", "sebastian/lines-of-code": "^1.0.3", "sebastian/version": "^3.0.1", "theseer/tokenizer": "^1.2.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "suggest": {"ext-pcov": "*", "ext-xdebug": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.15"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-03-07T09:28:20+00:00"}, {"name": "phpunit/php-file-iterator", "version": "3.0.6", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf", "reference": "cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/3.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2021-12-02T12:48:52+00:00"}, {"name": "phpunit/php-invoker", "version": "3.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "reference": "5a10147d0aaf65b58940a0b72f71c9ac0423cc67"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/5a10147d0aaf65b58940a0b72f71c9ac0423cc67", "reference": "5a10147d0aaf65b58940a0b72f71c9ac0423cc67", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^9.3"}, "suggest": {"ext-pcntl": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Invoke callables with a timeout", "homepage": "https://github.com/sebastian<PERSON>mann/php-invoker/", "keywords": ["process"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/3.1.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T05:58:55+00:00"}, {"name": "phpunit/php-text-template", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28", "reference": "5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/2.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T05:33:50+00:00"}, {"name": "phpunit/php-timer", "version": "5.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-timer/zipball/5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2", "reference": "5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-timer/issues", "source": "https://github.com/sebastian<PERSON>mann/php-timer/tree/5.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:16:10+00:00"}, {"name": "phpunit/phpunit", "version": "9.5.21", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "0e32b76be457de00e83213528f6bb37e2a38fcb1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/phpunit/zipball/0e32b76be457de00e83213528f6bb37e2a38fcb1", "reference": "0e32b76be457de00e83213528f6bb37e2a38fcb1", "shasum": ""}, "require": {"doctrine/instantiator": "^1.3.1", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.10.1", "phar-io/manifest": "^2.0.3", "phar-io/version": "^3.0.2", "php": ">=7.3", "phpspec/prophecy": "^1.12.1", "phpunit/php-code-coverage": "^9.2.13", "phpunit/php-file-iterator": "^3.0.5", "phpunit/php-invoker": "^3.1.1", "phpunit/php-text-template": "^2.0.3", "phpunit/php-timer": "^5.0.2", "sebastian/cli-parser": "^1.0.1", "sebastian/code-unit": "^1.0.6", "sebastian/comparator": "^4.0.5", "sebastian/diff": "^4.0.3", "sebastian/environment": "^5.1.3", "sebastian/exporter": "^4.0.3", "sebastian/global-state": "^5.0.1", "sebastian/object-enumerator": "^4.0.3", "sebastian/resource-operations": "^3.0.3", "sebastian/type": "^3.0", "sebastian/version": "^3.0.2"}, "require-dev": {"phpspec/prophecy-phpunit": "^2.0.1"}, "suggest": {"ext-soap": "*", "ext-xdebug": "*"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "9.5-dev"}}, "autoload": {"files": ["src/Framework/Assert/Functions.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "source": "https://github.com/sebastian<PERSON>mann/phpunit/tree/9.5.21"}, "funding": [{"url": "https://phpunit.de/sponsors.html", "type": "custom"}, {"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-06-19T12:14:25+00:00"}, {"name": "sebastian/cli-parser", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/cli-parser.git", "reference": "442e7c7e687e42adc03470c7b668bc4b2402c0b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/cli-parser/zipball/442e7c7e687e42adc03470c7b668bc4b2402c0b2", "reference": "442e7c7e687e42adc03470c7b668bc4b2402c0b2", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for parsing CLI options", "homepage": "https://github.com/sebastian<PERSON>mann/cli-parser", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/tree/1.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T06:08:49+00:00"}, {"name": "sebastian/code-unit", "version": "1.0.8", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "reference": "1fc9f64c0927627ef78ba436c9b17d967e68e120"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/1fc9f64c0927627ef78ba436c9b17d967e68e120", "reference": "1fc9f64c0927627ef78ba436c9b17d967e68e120", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/code-unit", "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/1.0.8"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:08:54+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5", "reference": "ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/tree/2.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T05:30:19+00:00"}, {"name": "sebastian/comparator", "version": "4.0.6", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "55f4261989e546dc112258c7a75935a81a7ce382"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/55f4261989e546dc112258c7a75935a81a7ce382", "reference": "55f4261989e546dc112258c7a75935a81a7ce382", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/diff": "^4.0", "sebastian/exporter": "^4.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/comparator/tree/4.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T15:49:45+00:00"}, {"name": "sebastian/complexity", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/complexity.git", "reference": "739b35e53379900cc9ac327b2147867b8b6efd88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/complexity/zipball/739b35e53379900cc9ac327b2147867b8b6efd88", "reference": "739b35e53379900cc9ac327b2147867b8b6efd88", "shasum": ""}, "require": {"nikic/php-parser": "^4.7", "php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for calculating the complexity of PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/complexity", "support": {"issues": "https://github.com/sebastian<PERSON>mann/complexity/issues", "source": "https://github.com/sebastian<PERSON>mann/complexity/tree/2.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T15:52:27+00:00"}, {"name": "sebastian/diff", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "3461e3fccc7cfdfc2720be910d3bd73c69be590d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/diff/zipball/3461e3fccc7cfdfc2720be910d3bd73c69be590d", "reference": "3461e3fccc7cfdfc2720be910d3bd73c69be590d", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3", "symfony/process": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/4.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:10:38+00:00"}, {"name": "sebastian/environment", "version": "5.1.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "1b5dff7bb151a4db11d49d90e5408e4e938270f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/1b5dff7bb151a4db11d49d90e5408e4e938270f7", "reference": "1b5dff7bb151a4db11d49d90e5408e4e938270f7", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/5.1.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-04-03T09:37:03+00:00"}, {"name": "sebastian/exporter", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "65e8b7db476c5dd267e65eea9cab77584d3cfff9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/65e8b7db476c5dd267e65eea9cab77584d3cfff9", "reference": "65e8b7db476c5dd267e65eea9cab77584d3cfff9", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/recursion-context": "^4.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "https://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/4.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2021-11-11T14:18:36+00:00"}, {"name": "sebastian/global-state", "version": "5.0.5", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "0ca8db5a5fc9c8646244e629625ac486fa286bf2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/0ca8db5a5fc9c8646244e629625ac486fa286bf2", "reference": "0ca8db5a5fc9c8646244e629625ac486fa286bf2", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/object-reflector": "^2.0", "sebastian/recursion-context": "^4.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^9.3"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/5.0.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-02-14T08:28:10+00:00"}, {"name": "sebastian/lines-of-code", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "reference": "c1c2e997aa3146983ed888ad08b15470a2e22ecc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/lines-of-code/zipball/c1c2e997aa3146983ed888ad08b15470a2e22ecc", "reference": "c1c2e997aa3146983ed888ad08b15470a2e22ecc", "shasum": ""}, "require": {"nikic/php-parser": "^4.6", "php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for counting the lines of code in PHP source code", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/1.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-28T06:42:11+00:00"}, {"name": "sebastian/object-enumerator", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "5c9eeac41b290a3712d88851518825ad78f45c71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/5c9eeac41b290a3712d88851518825ad78f45c71", "reference": "5c9eeac41b290a3712d88851518825ad78f45c71", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/object-reflector": "^2.0", "sebastian/recursion-context": "^4.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/4.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:12:34+00:00"}, {"name": "sebastian/object-reflector", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "b4f479ebdbf63ac605d183ece17d8d7fe49c15c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/b4f479ebdbf63ac605d183ece17d8d7fe49c15c7", "reference": "b4f479ebdbf63ac605d183ece17d8d7fe49c15c7", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/2.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:14:26+00:00"}, {"name": "sebastian/recursion-context", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "cd9d8cf3c5804de4341c283ed787f099f5506172"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/cd9d8cf3c5804de4341c283ed787f099f5506172", "reference": "cd9d8cf3c5804de4341c283ed787f099f5506172", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/4.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:17:30+00:00"}, {"name": "sebastian/resource-operations", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "0f4443cb3a1d92ce809899753bc0d5d5a8dd19a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/resource-operations/zipball/0f4443cb3a1d92ce809899753bc0d5d5a8dd19a8", "reference": "0f4443cb3a1d92ce809899753bc0d5d5a8dd19a8", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "support": {"issues": "https://github.com/sebastian<PERSON>mann/resource-operations/issues", "source": "https://github.com/sebastian<PERSON>mann/resource-operations/tree/3.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T06:45:17+00:00"}, {"name": "sebastian/type", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/type.git", "reference": "b233b84bc4465aff7b57cf1c4bc75c86d00d6dad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/type/zipball/b233b84bc4465aff7b57cf1c4bc75c86d00d6dad", "reference": "b233b84bc4465aff7b57cf1c4bc75c86d00d6dad", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "support": {"issues": "https://github.com/sebastian<PERSON>mann/type/issues", "source": "https://github.com/sebastian<PERSON>mann/type/tree/3.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-03-15T09:54:48+00:00"}, {"name": "sebastian/version", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "c6c1022351a901512170118436c764e473f6de8c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/c6c1022351a901512170118436c764e473f6de8c", "reference": "c6c1022351a901512170118436c764e473f6de8c", "shasum": ""}, "require": {"php": ">=7.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/3.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T06:39:44+00:00"}, {"name": "spatie/backtrace", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/spatie/backtrace.git", "reference": "4ee7d41aa5268107906ea8a4d9ceccde136dbd5b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/backtrace/zipball/4ee7d41aa5268107906ea8a4d9ceccde136dbd5b", "reference": "4ee7d41aa5268107906ea8a4d9ceccde136dbd5b", "shasum": ""}, "require": {"php": "^7.3|^8.0"}, "require-dev": {"ext-json": "*", "phpunit/phpunit": "^9.3", "symfony/var-dumper": "^5.1"}, "type": "library", "autoload": {"psr-4": {"Spatie\\Backtrace\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "description": "A better backtrace", "homepage": "https://github.com/spatie/backtrace", "keywords": ["Backtrace", "spatie"], "support": {"issues": "https://github.com/spatie/backtrace/issues", "source": "https://github.com/spatie/backtrace/tree/1.2.1"}, "funding": [{"url": "https://github.com/sponsors/spatie", "type": "github"}, {"url": "https://spatie.be/open-source/support-us", "type": "other"}], "time": "2021-11-09T10:57:15+00:00"}, {"name": "spatie/flare-client-php", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/spatie/flare-client-php.git", "reference": "b1b974348750925b717fa8c8b97a0db0d1aa40ca"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/flare-client-php/zipball/b1b974348750925b717fa8c8b97a0db0d1aa40ca", "reference": "b1b974348750925b717fa8c8b97a0db0d1aa40ca", "shasum": ""}, "require": {"illuminate/pipeline": "^8.0|^9.0", "php": "^8.0", "spatie/backtrace": "^1.2", "symfony/http-foundation": "^5.0|^6.0", "symfony/mime": "^5.2|^6.0", "symfony/process": "^5.2|^6.0", "symfony/var-dumper": "^5.2|^6.0"}, "require-dev": {"dms/phpunit-arraysubset-asserts": "^0.3.0", "pestphp/pest": "^1.20", "phpstan/extension-installer": "^1.1", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1.0", "spatie/phpunit-snapshot-assertions": "^4.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.1.x-dev"}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"Spatie\\FlareClient\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Send PHP errors to <PERSON><PERSON><PERSON>", "homepage": "https://github.com/spatie/flare-client-php", "keywords": ["exception", "flare", "reporting", "spatie"], "support": {"issues": "https://github.com/spatie/flare-client-php/issues", "source": "https://github.com/spatie/flare-client-php/tree/1.3.0"}, "funding": [{"url": "https://github.com/spatie", "type": "github"}], "time": "2022-08-08T10:10:20+00:00"}, {"name": "spatie/ignition", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/spatie/ignition.git", "reference": "997363fbcce809b1e55f571997d49017f9c623d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/ignition/zipball/997363fbcce809b1e55f571997d49017f9c623d9", "reference": "997363fbcce809b1e55f571997d49017f9c623d9", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "monolog/monolog": "^2.0", "php": "^8.0", "spatie/flare-client-php": "^1.1", "symfony/console": "^5.4|^6.0", "symfony/var-dumper": "^5.4|^6.0"}, "require-dev": {"mockery/mockery": "^1.4", "pestphp/pest": "^1.20", "phpstan/extension-installer": "^1.1", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1.0", "symfony/process": "^5.4|^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.2.x-dev"}}, "autoload": {"psr-4": {"Spatie\\Ignition\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A beautiful error page for PHP applications.", "homepage": "https://flareapp.io/ignition", "keywords": ["error", "flare", "laravel", "page"], "support": {"docs": "https://flareapp.io/docs/ignition-for-laravel/introduction", "forum": "https://twitter.com/flareappio", "issues": "https://github.com/spatie/ignition/issues", "source": "https://github.com/spatie/ignition"}, "funding": [{"url": "https://github.com/spatie", "type": "github"}], "time": "2022-05-16T13:16:07+00:00"}, {"name": "spatie/laravel-ignition", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/spatie/laravel-ignition.git", "reference": "fe37a0eafe6ea040804255c70e9808af13314f87"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/laravel-ignition/zipball/fe37a0eafe6ea040804255c70e9808af13314f87", "reference": "fe37a0eafe6ea040804255c70e9808af13314f87", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "illuminate/support": "^8.77|^9.0", "monolog/monolog": "^2.3", "php": "^8.0", "spatie/flare-client-php": "^1.0.1", "spatie/ignition": "^1.2.4", "symfony/console": "^5.0|^6.0", "symfony/var-dumper": "^5.0|^6.0"}, "require-dev": {"filp/whoops": "^2.14", "livewire/livewire": "^2.8|dev-develop", "mockery/mockery": "^1.4", "nunomaduro/larastan": "^1.0", "orchestra/testbench": "^6.23|^7.0", "pestphp/pest": "^1.20", "phpstan/extension-installer": "^1.1", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1.0", "spatie/laravel-ray": "^1.27"}, "type": "library", "extra": {"laravel": {"providers": ["Spatie\\LaravelIgnition\\IgnitionServiceProvider"], "aliases": {"Flare": "Spatie\\LaravelIgnition\\Facades\\Flare"}}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"Spatie\\LaravelIgnition\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A beautiful error page for Laravel applications.", "homepage": "https://flareapp.io/ignition", "keywords": ["error", "flare", "laravel", "page"], "support": {"docs": "https://flareapp.io/docs/ignition-for-laravel/introduction", "forum": "https://twitter.com/flareappio", "issues": "https://github.com/spatie/laravel-ignition/issues", "source": "https://github.com/spatie/laravel-ignition"}, "funding": [{"url": "https://github.com/spatie", "type": "github"}], "time": "2022-06-17T06:28:57+00:00"}, {"name": "theseer/tokenizer", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "34a41e998c2183e22995f158c581e7b5e755ab9e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/34a41e998c2183e22995f158c581e7b5e755ab9e", "reference": "34a41e998c2183e22995f158c581e7b5e755ab9e", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/1.2.1"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2021-07-28T10:34:58+00:00"}], "aliases": [], "minimum-stability": "dev", "stability-flags": [], "prefer-stable": true, "prefer-lowest": false, "platform": {"php": "^8.0.2"}, "platform-dev": [], "plugin-api-version": "2.3.0"}