{"name": "typicms/base", "type": "project", "description": "A multilingual CMS built with Laravel", "keywords": ["cms", "multilingual", "laravel", "typi"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "repositories": [{"type": "vcs", "url": "https://github.com/TypiCMS/Laravel-Sidebar.git"}, {"type": "git", "url": "ssh://************************:2234/finacnes/arca.git"}], "require": {"php": "^8.0.2", "barryvdh/laravel-dompdf": "^2.0", "bkwld/croppa": "^6.0", "eluceo/ical": "^2.5", "genealabs/laravel-model-caching": "^0.12.0", "giggsey/libphonenumber-for-php": "^8.12", "guzzlehttp/guzzle": "^7.2", "laracasts/presenter": "^0.2.5", "laracasts/utilities": "^3.2", "laravel/framework": "^9.2", "laravel/sanctum": "^2.15", "laravel/tinker": "^2.7", "laravel/ui": "^3.0", "maatwebsite/excel": "^3.1", "maatwebsite/laravel-sidebar": "~1.2.1", "msurguy/honeypot": "~1.1.5", "spatie/eloquent-sortable": "^4.0", "spatie/laravel-feed": "^4.1", "spatie/laravel-permission": "^5.1", "spatie/laravel-query-builder": "^5.0", "spatie/laravel-translatable": "^6.0", "studioone/arca": "^1.0", "symfony/http-client": "^6.0", "symfony/mailgun-mailer": "^6.0", "typicms/core": "^10.0", "typicms/laravel-translatable-bootforms": "^7.0", "typicms/nestablecollection": "^2.0", "typicms/things": "^10.0", "typidesign/laravel-artisan-translations": "~2.0", "ultrono/laravel-sitemap": "^9.0"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.6", "fakerphp/faker": "^1.9.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ignition": "^1.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "TypiCMS\\Modules\\": "Mo<PERSON>les/", "Studioone\\Arca\\": "vendor/studioone/arca/src"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php artisan storage:link"]}}