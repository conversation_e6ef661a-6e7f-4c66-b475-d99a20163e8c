{"codeToName": {"32": "space", "33": "exclam", "34": "quotedbl", "35": "numbersign", "36": "dollar", "37": "percent", "38": "ampersand.1", "39": "<PERSON><PERSON><PERSON>", "40": "parenleft", "41": "parenright", "42": "asterisk", "43": "plus", "44": "comma", "45": "hyphen", "46": "period", "47": "slash", "48": "zero", "49": "one", "50": "two", "51": "three", "52": "four", "53": "five", "54": "six", "55": "seven", "56": "eight", "57": "nine", "58": "colon", "59": "semicolon", "60": "less", "61": "equal", "62": "greater", "63": "question", "64": "at", "65": "A", "66": "B", "67": "C", "68": "D", "69": "E", "70": "F", "71": "G", "72": "H", "73": "I", "74": "J", "75": "K", "76": "L", "77": "M", "78": "N", "79": "O", "80": "P", "81": "Q", "82": "R", "83": "S", "84": "T", "85": "U", "86": "V", "87": "W", "88": "X", "89": "Y", "90": "Z", "91": "bracketleft", "92": "backslash", "93": "bracketright", "94": "asciicircum", "95": "underscore", "96": "grave", "97": "a", "98": "b", "99": "c", "100": "d", "101": "e", "102": "f", "103": "g", "104": "h", "105": "i", "106": "j", "107": "k", "108": "l", "109": "m", "110": "n", "111": "o", "112": "p", "113": "q", "114": "r", "115": "s", "116": "t", "117": "u", "118": "v", "119": "w", "120": "x", "121": "y", "122": "z", "123": "braceleft", "124": "bar", "125": "braceright", "126": "asciitilde", "160": "nbspace", "161": "exclamdown", "168": "<PERSON><PERSON><PERSON>", "171": "guillemotleft", "172": "logicalnot", "173": "sfthyphen", "175": "macron", "176": "degree", "177": "plus<PERSON>us", "178": "twosuperior", "179": "threesuperior", "180": "acute", "183": "periodcentered", "184": "cedilla", "185": "onesuperior", "186": "ordmasculine", "187": "guil<PERSON><PERSON><PERSON>", "191": "questiondown", "192": "<PERSON><PERSON>", "193": "Aacute", "194": "Acircumflex", "195": "<PERSON><PERSON>", "196": "Adieresis", "197": "<PERSON><PERSON>", "198": "AE", "199": "Ccedilla", "200": "<PERSON><PERSON>", "201": "Eacute", "202": "Ecircumflex", "203": "Edieresis", "204": "<PERSON><PERSON>", "205": "Iacute", "206": "Icircumflex", "207": "Idieresis", "208": "Eth", "209": "Ntilde", "210": "<PERSON><PERSON>", "211": "Oacute", "212": "Ocircumflex", "213": "<PERSON><PERSON><PERSON>", "214": "Odieresis", "215": "multiply", "216": "<PERSON><PERSON><PERSON>", "217": "<PERSON><PERSON>", "218": "Uacute", "219": "Ucircumflex", "220": "Udieresis", "221": "Ya<PERSON>", "222": "Thorn", "223": "germandbls", "224": "agrave", "225": "aacute", "226": "acircumflex", "227": "atilde", "228": "adieresis", "229": "aring", "230": "ae", "231": "ccedilla", "232": "egrave", "233": "eacute", "234": "ecircumflex", "235": "edieresis", "236": "igrave", "237": "iacute", "238": "icircumflex", "239": "idieresis", "240": "eth", "241": "ntilde", "242": "ograve", "243": "oacute", "244": "ocircumflex", "245": "otilde", "246": "odieresis", "247": "divide", "248": "oslash", "249": "ugrave", "250": "uacute", "251": "ucircumflex", "252": "udieresis", "253": "yacute", "254": "thorn", "255": "ydieresis", "304": "Idot", "305": "dotlessi", "338": "OE", "339": "oe", "352": "<PERSON><PERSON><PERSON>", "353": "scaron", "376": "Ydieresis", "381": "<PERSON><PERSON><PERSON>", "382": "z<PERSON>on", "402": "florin", "710": "circumflex", "711": "caron", "730": "ring", "732": "tilde", "1040": "afii10017", "1041": "afii10018", "1042": "afii10019", "1043": "afii10020", "1044": "afii10021", "1045": "afii10022", "1046": "afii10024", "1047": "afii10025", "1048": "afii10026", "1049": "afii10027", "1050": "afii10028", "1051": "afii10029", "1052": "afii10030", "1053": "afii10031", "1054": "afii10032", "1055": "afii10033", "1056": "afii10034", "1057": "afii10035", "1058": "afii10036", "1059": "afii10037", "1060": "afii10038", "1061": "afii10039", "1062": "afii10040", "1063": "afii10041", "1064": "afii10042", "1065": "afii10043", "1066": "afii10044", "1067": "afii10045", "1068": "afii10046", "1069": "afii10047", "1070": "afii10048", "1071": "afii10049", "1072": "afii10065", "1073": "afii10066", "1074": "afii10067", "1075": "afii10068", "1076": "afii10069", "1077": "afii10070", "1078": "afii10072", "1079": "afii10073", "1080": "afii10074", "1081": "afii10075", "1082": "afii10076", "1083": "afii10077", "1084": "afii10078", "1085": "afii10079", "1086": "afii10080", "1087": "afii10081", "1088": "afii10082", "1089": "afii10083", "1090": "afii10084", "1091": "afii10085", "1092": "afii10086", "1093": "afii10087", "1094": "afii10088", "1095": "afii10089", "1096": "afii10090", "1097": "afii10091", "1098": "afii10092", "1099": "afii10093", "1100": "afii10094", "1101": "afii10095", "1102": "afii10096", "1103": "afii10097", "1107": "afii10100", "1109": "afii10102", "1110": "afii10103", "1111": "afii10104", "1112": "afii10105", "1116": "afii10109", "1118": "afii10110", "1123": "afii10194", "1329": "armenianAYB", "1330": "armenianBEN", "1331": "armenianGIM", "1332": "armenianDA", "1333": "armenianECH", "1334": "armenianZA", "1335": "armenianEH", "1336": "armenianET", "1337": "armenianTO", "1338": "armenianZHE", "1339": "armenianINI", "1340": "armenianLIWN", "1341": "armenianXEH", "1342": "armenianCA", "1343": "armenianKEN", "1344": "armenianHO", "1345": "armenianJA", "1346": "armenianLAD", "1347": "armenianCHEH", "1348": "armenianMEN", "1349": "armenianYI", "1350": "armenianNOW", "1351": "armenianSHA", "1352": "armenianVO", "1353": "armenianCHA", "1354": "armenianPEH", "1355": "armenianJHEH", "1356": "armenianRA", "1357": "armenianSEH", "1358": "armenianVEW", "1359": "armenianTIWN", "1360": "armenianREH", "1361": "armenianCO", "1362": "armenianYIWN", "1363": "armenianPIWR", "1364": "armenianKEH", "1365": "armenianOH", "1366": "armenianFEH", "1370": "armapostrophe", "1371": "armemphasis", "1372": "armexclam", "1373": "armsep", "1374": "armquestion", "1377": "armenianAyb", "1378": "armenianBen", "1379": "armenianGim", "1380": "armenianDa", "1381": "armenianEch", "1382": "armenianZa", "1383": "armenianEh", "1384": "armenianEt", "1385": "armenianTo", "1386": "armenianZhe", "1387": "armenianIni", "1388": "armenianLiwn", "1389": "armenianXeh", "1390": "armenianCa", "1391": "armenianKen", "1392": "armenianHo", "1393": "armenianJa", "1394": "armenianLad", "1395": "armenianCheh", "1396": "armenianMen", "1397": "armenianYi", "1398": "armenianNow", "1399": "armenianSha", "1400": "armenianVo", "1401": "armenianCha", "1402": "armenianPeh", "1403": "armenian<PERSON>heh", "1404": "armenianRa", "1405": "armenianSeh", "1406": "armenianVew", "1407": "armenianTyun", "1408": "armenianReh", "1409": "armenianCo", "1410": "armenianYiwn", "1411": "armenianPiwr", "1412": "armenianKeh", "1413": "armenianOh", "1414": "armenianFeh", "1415": "armenianEv", "1417": "armfullstop", "1418": "armhyphen", "7922": "<PERSON><PERSON>", "7923": "ygrave", "8208": "uni2010", "8211": "endash", "8212": "emdash", "8213": "afii00208", "8216": "quoteleft", "8217": "quoteright", "8218": "quotesinglbase", "8219": "quoteright.001", "8220": "quotedblleft", "8221": "<PERSON><PERSON><PERSON><PERSON>", "8222": "quotedblbase", "8224": "dagger", "8226": "bullet", "8228": "onedotenleader", "8230": "ellipsis", "8242": "minute", "8243": "second", "8249": "guil<PERSON>lle<PERSON>", "8250": "guil<PERSON><PERSON><PERSON>", "8470": "numero", "9679": "H18533", "9702": "openbullet", "61443": "uF003", "61444": "uF004", "61445": "uF005", "64275": "uniFB13", "64276": "uniFB14", "64277": "uniFB15", "64278": "uniFB16", "64279": "uniFB17"}, "isUnicode": true, "EncodingScheme": "FontSpecific", "FontName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ian_U", "FullName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ian_U", "Version": "Version 1.000 2005 initial release", "PostScriptName": "HovhannesTumanianU", "Weight": "Medium", "ItalicAngle": "0", "IsFixedPitch": "false", "UnderlineThickness": "85", "UnderlinePosition": "-120", "FontHeightOffset": "12", "Ascender": "757", "Descender": "-439", "FontBBox": ["-592", "-446", "1105", "800"], "StartCharMetrics": "426", "C": {"32": 189, "33": 342, "34": 253, "35": 342, "36": 542, "37": 421, "38": 396, "39": 251, "40": 333, "41": 333, "42": 272, "43": 383, "44": 195, "45": 242, "46": 249, "47": 342, "48": 415, "49": 415, "50": 415, "51": 415, "52": 415, "53": 415, "54": 415, "55": 415, "56": 415, "57": 415, "58": 299, "59": 299, "60": 300, "61": 383, "62": 300, "63": 425, "64": 931, "65": 491, "66": 483, "67": 407, "68": 313, "69": 334, "70": 470, "71": 355, "72": 575, "73": 261, "74": 251, "75": 418, "76": 282, "77": 867, "78": 439, "79": 501, "80": 480, "81": 376, "82": 512, "83": 460, "84": 407, "85": 616, "86": 324, "87": 564, "88": 408, "89": 445, "90": 407, "91": 333, "92": 342, "93": 333, "94": 350, "95": 636, "96": 190, "97": 332, "98": 270, "99": 218, "100": 265, "101": 294, "102": 328, "103": 319, "104": 308, "105": 224, "106": 224, "107": 396, "108": 274, "109": 525, "110": 384, "111": 304, "112": 381, "113": 276, "114": 235, "115": 229, "116": 268, "117": 380, "118": 357, "119": 512, "120": 318, "121": 346, "122": 310, "123": 333, "124": 342, "125": 333, "126": 416, "160": 189, "161": 342, "168": 240, "171": 491, "172": 417, "173": 417, "175": 733, "176": 285, "177": 383, "178": 317, "179": 278, "180": 249, "183": 249, "184": 151, "185": 306, "186": 285, "187": 512, "191": 425, "192": 491, "193": 491, "194": 491, "195": 491, "196": 491, "197": 491, "198": 861, "199": 459, "200": 417, "201": 417, "202": 417, "203": 417, "204": 342, "205": 342, "206": 342, "207": 342, "208": 527, "209": 700, "210": 501, "211": 501, "212": 501, "213": 501, "214": 501, "215": 241, "216": 501, "217": 651, "218": 651, "219": 651, "220": 651, "221": 445, "222": 521, "223": 443, "224": 342, "225": 342, "226": 342, "227": 342, "228": 342, "229": 342, "230": 475, "231": 260, "232": 282, "233": 282, "234": 282, "235": 282, "236": 224, "237": 224, "238": 224, "239": 224, "240": 192, "241": 460, "242": 291, "243": 273, "244": 264, "245": 282, "246": 291, "247": 299, "248": 486, "249": 380, "250": 380, "251": 380, "252": 380, "253": 282, "254": 326, "255": 282, "304": 342, "305": 224, "338": 728, "339": 560, "352": 460, "353": 254, "376": 445, "381": 693, "382": 538, "402": 508, "710": 350, "711": 353, "730": 285, "732": 326, "1040": 491, "1041": 511, "1042": 486, "1043": 563, "1044": 527, "1045": 396, "1046": 859, "1047": 425, "1048": 502, "1049": 502, "1050": 435, "1051": 602, "1052": 802, "1053": 640, "1054": 481, "1055": 596, "1056": 521, "1057": 475, "1058": 674, "1059": 450, "1060": 638, "1061": 512, "1062": 773, "1063": 542, "1064": 828, "1065": 798, "1066": 392, "1067": 605, "1068": 366, "1069": 510, "1070": 658, "1071": 476, "1072": 332, "1073": 310, "1074": 270, "1075": 283, "1076": 192, "1077": 294, "1078": 549, "1079": 263, "1080": 397, "1081": 410, "1082": 348, "1083": 391, "1084": 494, "1085": 322, "1086": 388, "1087": 321, "1088": 404, "1089": 243, "1090": 520, "1091": 399, "1092": 586, "1093": 315, "1094": 317, "1095": 268, "1096": 447, "1097": 560, "1098": 255, "1099": 452, "1100": 266, "1101": 228, "1102": 396, "1103": 300, "1107": 381, "1109": 487, "1110": 280, "1111": 280, "1112": 403, "1116": 492, "1118": 521, "1123": 436, "1329": 643, "1330": 517, "1331": 475, "1332": 570, "1333": 456, "1334": 536, "1335": 479, "1336": 746, "1337": 558, "1338": 478, "1339": 391, "1340": 319, "1341": 583, "1342": 288, "1343": 346, "1344": 482, "1345": 403, "1346": 361, "1347": 495, "1348": 621, "1349": 411, "1350": 603, "1351": 349, "1352": 446, "1353": 354, "1354": 606, "1355": 425, "1356": 563, "1357": 642, "1358": 604, "1359": 542, "1360": 370, "1361": 660, "1362": 766, "1363": 758, "1364": 586, "1365": 501, "1366": 376, "1370": 195, "1371": 251, "1372": 326, "1373": 219, "1374": 257, "1377": 577, "1378": 400, "1379": 314, "1380": 439, "1381": 298, "1382": 394, "1383": 254, "1384": 387, "1385": 396, "1386": 372, "1387": 338, "1388": 209, "1389": 525, "1390": 444, "1391": 301, "1392": 299, "1393": 288, "1394": 382, "1395": 386, "1396": 319, "1397": 225, "1398": 282, "1399": 242, "1400": 391, "1401": 247, "1402": 603, "1403": 265, "1404": 440, "1405": 361, "1406": 431, "1407": 399, "1408": 412, "1409": 304, "1410": 296, "1411": 483, "1412": 288, "1413": 277, "1414": 449, "1415": 501, "1417": 178, "1418": 383, "7922": 635, "7923": 521, "8208": 355, "8211": 242, "8212": 780, "8213": 883, "8216": 206, "8217": 231, "8218": 195, "8219": 251, "8220": 253, "8221": 253, "8222": 308, "8224": 299, "8226": 387, "8228": 119, "8230": 603, "8242": 251, "8243": 253, "8249": 300, "8250": 300, "8470": 1157, "9679": 387, "9702": 285, "61443": 251, "61444": 251, "61445": 251, "64275": 622, "64276": 705, "64277": 470, "64278": 799, "64279": 705}, "CIDtoGID_Compressed": true, "CIDtoGID": "eJzt23XQF2UQB/Dv2oqKHQgIgoXdigooBmJLiC12t9jd3d3d3d3d3d3dnb95RUdn1AFRX+Pzmdm72+f29vae/y8ZQkNnmAyb4TJ8RsiIGSktMnJGyahpmdEyesbImBkrY2ecjJvxMn5aZYK0Tpu0zYRpl/aZKB3SMRNnkkyayTJ5OmWKTJmpMnWmybSZLtNnhsyYmTJzZsmsmS2dM3vmyJzpkq7plrkyd7pnnsyb+TJ/emSB9MyCWSgLZ5EsmsWyeJZIr/ROn/TNkumXpbJ0lsmyWS7LZ4WsmP5D+vENK2XlX+SrNB1XzWpZvXFeI2tmrayddbJu0/p6WT8bZMNsNLB642ySTTMgm2XzbJEts1W2zjbZNttl++yQHbNTds4u2TW7ZffskT2zV/bOPtk3+2X/HJADc1AOziE5NIfl8ByRI3NUjs4xOTbH5fickBNzUk7OKTk1p+X0nJEzc1bOzjk5N+fl/FyQC3PRn7ADv+/iXDLEPS7NZb957/JcMVi9rvzp6qpcPYjPXDNYb4A/5tpcN0h11zfihr90EvjBjbkpN+eW3JrbcnvuyJ25K3fnntyb+3J/HsiDeSgP55E8msfyeJ7Ik3kqT+eZPJvn8nxeyIt5KS/nlbya1/J63sibeStv5528m/fyfj7Ih/koH+eTfJrP8nm+yJf5Kl/nm3yb7ypVPwxRQzVi6Bqmhq3hBq4M34gRfhyyRmyOrWFQ1EjVokauUWrUalmj1eg1Ro1ZY9XYNU6NW+PV+NWqJqjW1aba1oTVrtrXRNWhOtbENUlNWpPV5NWppqgpa6qauqYZ2HHamq6mrxlqxqZsppq5ZqlZa7bqXLPXHDVndamu1a3mqrmre81T89Z8NX/1qAWqZy1YC9XCtUgtWovV4rVE9are1af61pLVr5aqpWuZRr9la7nm3C8AAAAAAAAAAAAAAAD+b2r5WqG5Z+DfqVZsOvavlarpP/dapVat1Wr1WqPWrLUa+dqNWKcR6zZivd/osX5t8LNsw9ro1+v4Z6uNm3sCAID/utpksKo3/avmAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPi3qwG1WW3e3FMAAAAAAPB3qy1qy9qqtq5tmnsSAACGzPdVM56/", "_version_": 6}