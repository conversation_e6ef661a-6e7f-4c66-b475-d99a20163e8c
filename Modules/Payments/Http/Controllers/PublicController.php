<?php

namespace TypiCMS\Modules\Payments\Http\Controllers;

use App\Services\Aobayt;
use App\Services\Mulberry;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Illuminate\View\View;
use Studioone\Arca\ArcaPurchase;
use TypiCMS\Modules\Clients\Models\Client;
use TypiCMS\Modules\Clients\Notifications\NewClientRequest;
use TypiCMS\Modules\Clients\Notifications\YourClientRequest;
use TypiCMS\Modules\Core\Http\Controllers\BasePublicController;
use TypiCMS\Modules\Payments\Models\Payment;

class PublicController extends BasePublicController
{

    public $mulberry;
    public function __construct(Mulberry $mulberry){
        $this->mulberry = $mulberry;
    }

    public function pay($uuid,Request $request)
    {
        $client_model = Client::whereUuid($uuid)->first();
        if($client_model && $client_model->payment_status == 0) {
            $clientId = $client_model->id;
            $orderId =  rand(1111,9999).substr(time(),strlen(time())-7,7);
            $payment = new Payment();
            $payment->order_id = $orderId;
            $payment->client_id = $clientId;
            $payment->amount = config('typicms.form_amount');
            $payment->save();
            try{
                $arcaTestMode = config('typicms.arca_testing_mode') ? config('typicms.arca_testing_mode') : 0;
                $arca_login = config('typicms.arca_login_setting') ?? config('typicms.arca_login');
                $arca_password = config('typicms.arca_password_setting') ?? config('typicms.arca_password');

                $returnUrl = route(app()->getLocale()."::payment-result");
                $arcaForm = ArcaPurchase::get( $arca_login , $arca_password)
                    ->setLocale(app()->getLocale())
                    ->setAmount( $payment->amount*100)
                    ->setCurrency('051')
                    ->setDescription('Global Credit Form Payment')
                    ->setOrderNumber(  $payment->order_id )
                    ->setReturnUrl($returnUrl)
                    ->setTestMode($arcaTestMode)
                    ->getForm()
                    ->submit();
                return view('payments::public.form',compact('arcaForm'));
            }catch(\Exception $e){
                //dd($e->getMessage(),$e->getTraceAsString());
                Log::error(' ArcaForm ERROR', ['message'=>$e->getMessage(),'getTraceAsString'=>$e->getTraceAsString()]);
                abort(503);
            }
        }
        if($client_model->payment_status == 1){
            return view('payments::public.success',compact('arcaForm'));
        }
        abort(404);
    }

    public function result(Request $request)
    {
        try{
            $arcaTestMode = config('typicms.arca_testing_mode') ? config('typicms.arca_testing_mode') : 0;
            $arca_login = config('typicms.arca_login_setting') ?? config('typicms.arca_login');
            $arca_password = config('typicms.arca_password_setting') ?? config('typicms.arca_password');

            Log::info("Result Get Request", ['request' => $request]);
            $arcaStatus = ArcaPurchase::get($arca_login , $arca_password)
                ->setLocale(app()->getLocale())
                ->setOrderId( $request['orderId'])
                ->setTestMode($arcaTestMode)
                ->getRequest()
                ->getOrderStatus();

            Log::info("Get arcaStatus", [$arcaStatus]);
            $order = Payment::where('order_id','=',$arcaStatus['orderNumber'])->first();
            if($arcaStatus['orderStatus'] != 2 ){
                if($order){
                    $return_data = [
                        'client_name'       => $arcaStatus['cardAuthInfo']['cardholderName'],
                        'card_number'       => $arcaStatus['cardAuthInfo']['pan'],
                        'exp_date'          => $arcaStatus['cardAuthInfo']['expiration'],
                        'order_status'      => $arcaStatus['orderStatus'],
                        'payment_id'        => $request['orderId'], //$arcaStatus['attributes'][0]['value'],
                        'response_code'     => $arcaStatus['errorCode'],
                        'data'              => json_encode($arcaStatus),
                    ];
                    $order->update($return_data);
                }
                throw new \Exception("Payment Error");
            }
            $return_data = [
                'client_name'       => $arcaStatus['cardAuthInfo']['cardholderName'],
                'card_number'       => $arcaStatus['cardAuthInfo']['pan'],
                'exp_date'          => $arcaStatus['cardAuthInfo']['expiration'],
                'order_status'      => $arcaStatus['orderStatus'],
                'payment_id'        => $request['orderId'], //$arcaStatus['attributes'][0]['value'],
                'response_code'     => $arcaStatus['errorCode'],
                'data'              => json_encode($arcaStatus),
            ];
            $order->update($return_data);
            $client = Client::where('id',$order->client_id)->first();
            Log::info("Get Payment ROW in Arca action", [$client]);
            Log::info("Get Client in Arca action", ['client_id'=>$order->client_id,$client]);
            if($arcaStatus['orderStatus'] != 2){
                throw new \Exception("Arca Error");
            }
            if($arcaStatus['orderStatus'] == 2){
                if($order->payment_id == null){
                    if($order->response_code == 0){
                        throw new \Exception("Payment Error in orderStatus == 2 and payment_id is null");
                    }
                }else{
                    if($client->payment_status == '1' && $client->send_mulberry_status == 1){
                        Log::info("Result Method Client refresh or recall" , ['client' => $client]);
                        return redirect()->route(app()->getLocale().'::form-success',['client'=>$client->uuid]);
                    }else{
                        $client->payment_status = '1';
                        $client->save();
                        $mulberry_data = $client;
                        $res = $this->mulberry->sendInfo($mulberry_data);
                        $aobyte =new Aobayt();
                        Log::info("Mulberry Return in Payment " , ['Response' => $res]);
                        if($res['status'] == 'success' ){
                            $client->send_mulberry_status     = 1;
                            $client->submission_id            = $res['submission_id'];
                            $client->tracking_id              = $res['tracking_id'];
                            $client->save();
                            $client_data = json_decode($client->data,true);
                            $client_form = json_decode($client->form_info,true);
                            $body  = view('clients::mail.form1-request',['client_data' => $client_data,'client_form'=>$client_form])->render();
                            Log::info("Get Final Email text",['body'=>$body]);
                            $aobyte->sendFinalEmail($client->uuid,$client_data,$client_form,$res['tracking_id'],$body,__('New application request'));
                            return redirect()->route(app()->getLocale().'::form-success',['client'=>$client->uuid]);
                        }else{
                            $client->send_mulberry_status = 2;
                            $client->save();
                            $client_data = json_decode($client->data,true);
                            $client_form = json_decode($client->form_info,true);
                            if(config('typicms.webmaster_email')  && $client_data && $client_form){
                                $body       = view('clients::mail.mulberry_error_template',['client_data' => $client_data,'client_form'=>$client_form])->render();
                                $aobyte->sendFinalWorningEmail($client->uuid,config('typicms.webmaster_email'),$body,__('New application request'));
                            }
                            return redirect()->route(app()->getLocale().'::form-failed',['client'=>$client->uuid]);
                        }
                    }
                }
            }
            return redirect()->back()->withErrors(['orderStatus'=>$arcaStatus['orderStatus'],'errorCode'=>$arcaStatus['errorCode']]);
        }
        catch(\Exception $e){
            Log::error('ArcaStatus ERROR', ['message'=>$e->getMessage(),'getTraceAsString'=>$e->getTraceAsString()]);
            return redirect()->back()->withErrors(['orderStatus'=>$arcaStatus['orderStatus'],'errorCode'=>$arcaStatus['errorCode']]);
        }
    }

    public function success(Request $request): View
    {
        if (session('status')) {
            $order = $request->session()->get('status');
            return view('payments::public.success')->with(compact('order'));
        }
        else{
            abort(503);
        }
    }

    public function failed(Request $request): View
    {
        if (session('status')) {
            $order =  $request->session()->get('status');
            return view('payments::public.failed')->with(compact('order'));
        }
        else{
            abort(503);
        }
    }

}
