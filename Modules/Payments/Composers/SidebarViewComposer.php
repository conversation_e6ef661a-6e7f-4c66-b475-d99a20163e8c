<?php

namespace TypiCMS\Modules\Payments\Composers;

use Illuminate\Support\Facades\Gate;
use Illuminate\View\View;
use Maatwebsite\Sidebar\SidebarGroup;
use Maatwebsite\Sidebar\SidebarItem;

class SidebarViewComposer
{
    public function compose(View $view)
    {
        /*if (Gate::denies('read payments')) {
            return;
        }
        $view->sidebar->group(__('Content'), function (SidebarGroup $group) {
            $group->id = 'content';
            $group->weight = 30;
            $group->addItem(__('Payments'), function (SidebarItem $item) {
                $item->id = 'payments';
                $item->icon = config('typicms.modules.payments.sidebar.icon');
                $item->weight = config('typicms.modules.payments.sidebar.weight');
                $item->route('admin::index-payments');
                $item->append('admin::create-payment');
            });
        });*/
    }
}
