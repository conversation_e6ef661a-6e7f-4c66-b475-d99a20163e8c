<?php

namespace TypiCMS\Modules\Clients\Http\Requests;

use App\Rules\AmLetterRule;
use TypiCMS\Modules\Core\Http\Requests\AbstractFormRequest;

class AgriculturalFormRequest extends AbstractFormRequest
{
    public function rules()
    {
        $return = [
            'customer_type'             => 'required|in:legal_person,physical_person',
            'gc_customer'               => 'required|in:1,0',
            'company_name'              => ['required_if:customer_type,legal_person',new AmLetterRule()],
            'activity_type'             => 'required_if:customer_type,legal_person',
            'hvhh'                      => 'required_if:customer_type,legal_person',
            'same_registration_address' => 'nullable',
            'same_business_address'     => 'nullable',
            'registration_address'      => ['required',new AmLetterRule()],
            'business_address'          => ['required_without:same_registration_address',new AmLetterRule()],
            'legal_address'             => ['required_without:same_business_address',new AmLetterRule()],

            'full_name'                     => ['required',new AmLetterRule()],
            'social_card_number'            => 'required',
            'identification_document_type'  =>'required|in:id_card,passport',
            'id_card'                       => 'required_if:identification_document_type,id_card',
            'passport'                      => 'required_if:identification_document_type,passport',
            'phone'                         => 'required',
            'email'                         => 'required|email:rfc,dns',

            'loan_type'                 => 'required',
            'loan_currency'             => 'required',
            'loan_amount'               => 'required',
            'loan_purpose'              =>'required',
            'loan_period'               =>'required',
            'loan_special_purpose'      =>'required_if:loan_purpose,agricultural_loan_purpose_other',
        ];
        return $return;
    }
}
