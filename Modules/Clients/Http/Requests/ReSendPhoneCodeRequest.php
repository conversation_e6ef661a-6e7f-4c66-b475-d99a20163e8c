<?php

namespace TypiCMS\Modules\Clients\Http\Requests;

use TypiCMS\Modules\Core\Http\Requests\AbstractFormRequest;

class ReSendPhoneCodeRequest extends AbstractFormRequest
{
    public function rules()
    {
        return [
            'client_token' => 'required',
            'my_name' => 'honeypot',
            'my_time' => 'required|honeytime:5',
        ];
    }

    public function messages()
    {
        return [
            'my_time.honeytime' =>__('Please wait until submitting the form again'),
        ];
    }
}
