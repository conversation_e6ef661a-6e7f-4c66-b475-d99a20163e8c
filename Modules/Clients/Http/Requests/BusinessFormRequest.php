<?php

namespace TypiCMS\Modules\Clients\Http\Requests;

use App\Rules\AmLetterRule;
use TypiCMS\Modules\Core\Http\Requests\AbstractFormRequest;

class BusinessFormRequest extends AbstractFormRequest
{
    public function rules()
    {
        return [
            'gc_customer'           => 'required|in:1,0',
            'company_name'          => ['required',new AmLetterRule()],
            'activity_type'         =>'required',
            'hvhh'                  =>'required',
            'same_legal_address'    => 'nullable',
            'activity_address'      =>['required',new AmLetterRule()],
            'legal_address'         =>'required_without:same_legal_address',

            'full_name'             =>['required',new AmLetterRule()],
            'social_card_number'    =>'required',
            'id_card'               =>'required_without:passport',
            'passport'              =>'required_without:id_card',
            'phone'                 =>'required',
            'email'                 =>'required|email:rfc,dns',

            'loan_type'             =>'required',
            'loan_currency'         =>'required',
            'loan_amount'           =>'required',
            'loan_period'           =>'required',
            'loan_purpose'          =>'required',
            'loan_special_purpose'  =>'required_if:loan_purpose,business_loan_purpose_other',
        ];
    }
}
