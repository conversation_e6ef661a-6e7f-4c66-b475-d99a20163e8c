<?php

namespace TypiCMS\Modules\Clients\Http\Requests;

use TypiCMS\Modules\Core\Http\Requests\AbstractFormRequest;

class VerifyPhoneRequest extends AbstractFormRequest
{
    public function rules()
    {
        return [
            'code' => 'required|numeric|digits:4',
            'token' => 'required',
            'my_name' => 'honeypot',
            'my_time' => 'required|honeytime:5',
        ];
    }

    public function messages()
    {
        return [
            'code.required'     => __('Required'),
            'code.digits'       => __('The code must be 4 digits'),
            'my_time.honeytime' =>__('Please wait until submitting the form again'),
        ];
    }
}
