<?php

namespace TypiCMS\Modules\Clients\Http\Requests;

use TypiCMS\Modules\Core\Http\Requests\AbstractFormRequest;

class FormRequestForm1 extends AbstractFormRequest
{
    public function rules()
    {
        return [
            'complaints' => 'nullable',
            'attached_file.*' => 'required|mimetypes:application/pdf,text/pdf,image/jpeg,image/png|max:5120',
            'receive_method' => 'required|in:postal,email',
            'shipping_address' => 'required_if:receive_method,postal',
        ];
    }
}
