<?php

namespace TypiCMS\Modules\Clients\Http\Controllers;



use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\View\View;

use TypiCMS\Modules\Core\Http\Controllers\BasePublicController;
use TypiCMS\Modules\Clients\Models\Client;

class PdfController extends BasePublicController
{
    public function index(Request $request)
    {
        /*$pdf = PDF::loadView('pdf.sample', [
            'title' => 'CodeAndDeploy.com Laravel Pdf Tutorial',
            'description' => 'This is an example Laravel pdf tutorial.',
            'footer' => 'by <a href="https://codeanddeploy.com">codeanddeploy.com</a>'
        ]);*/


        if($request->pdf == 1) {
            $pdf = Pdf::loadView('clients::public.pdftemplate', [
                'title' => 'CodeAndDeploy.com Laravel Pdf Tutorial ,CodeAndDeploy.com ,CodeAndDeploy.com'
            ])->setPaper('a4', 'portrait')->setOption(['dpi' => 195, 'defaultFont' => 'SegoeUI']);

            return $pdf->download('sample.pdf');
        }
        $title = 'Pdf Example';

        return view('clients::public.pdftemplate')->with(compact('title'));
    }
}
