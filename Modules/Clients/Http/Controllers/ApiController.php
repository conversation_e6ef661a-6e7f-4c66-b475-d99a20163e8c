<?php

namespace TypiCMS\Modules\Clients\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use <PERSON><PERSON>\QueryBuilder\AllowedFilter;
use <PERSON><PERSON>\QueryBuilder\AllowedSort;
use <PERSON><PERSON>\QueryBuilder\QueryBuilder;
use TypiCMS\Modules\Clients\Filters\FilterCustom;
use TypiCMS\Modules\Clients\Filters\SortCustom;
use TypiCMS\Modules\Core\Filters\FilterOr;
use TypiCMS\Modules\Core\Http\Controllers\BaseApiController;
use TypiCMS\Modules\Clients\Models\Client;

class ApiController extends BaseApiController
{
    public function index(Request $request): LengthAwarePaginator
    {
        $data = QueryBuilder::for(Client::class)
            ->selectFields($request->input('fields.clients'))
            //->allowedSorts(['created_at'])
            ->allowedSorts([
                AllowedSort::custom('phone', new SortCustom()),
                AllowedSort::custom('ssn', new SortCustom()),
                AllowedSort::custom('first_name', new SortCustom()),
                AllowedSort::custom('last_name', new SortCustom()),
                AllowedSort::custom('formtype', new SortCustom()),
                'source',
                'created_at'
            ])
            ->allowedFilters([
                AllowedFilter::custom('phone,ssn,first_name,source,created_at', new FilterCustom()),
            ])
            ->allowedIncludes(['image'])
            ->paginate($request->input('per_page'));

        return $data;
    }

    protected function updatePartial(Client $client, Request $request)
    {
        foreach ($request->only('status') as $key => $content) {
            if ($client->isTranslatableAttribute($key)) {
                foreach ($content as $lang => $value) {
                    $client->setTranslation($key, $lang, $value);
                }
            } else {
                $client->{$key} = $content;
            }
        }

        $client->save();
    }

    public function destroy(Client $client)
    {
        $client->delete();
    }
}
