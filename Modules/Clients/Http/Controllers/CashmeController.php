<?php

namespace TypiCMS\Modules\Clients\Http\Controllers;

use App\Repositories\VerifyRepository;
use App\Services\Mulberry;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use TypiCMS\Modules\Clients\Http\Requests\ReSendPhoneCodeRequest;
use TypiCMS\Modules\Clients\Http\Requests\SsnRequest;
use TypiCMS\Modules\Clients\Http\Requests\VerifyPhoneRequest;
use TypiCMS\Modules\Clients\Services\ClientService;
use TypiCMS\Modules\Clients\Services\FormService;
use TypiCMS\Modules\Core\Http\Controllers\BasePublicController;
use TypiCMS\Modules\Clients\Models\Client;

class CashmeController extends BasePublicController
{

    protected $formService;
    protected $verifyrepository;
    protected $mulberry;
    protected $clientService;



    public function __construct(FormService $formService, ClientService $clientService, VerifyRepository $verifyrepository, Mulberry $mulberry)
    {
        $this->formService = $formService;
        $this->clientService = $clientService;
        $this->verifyrepository = $verifyrepository;
        $this->mulberry = $mulberry;
    }

    public function checkClient(SsnRequest $request)
    {
        $data = $request->validated();
        $uuid = Str::uuid();
        $data['uuid'] = $uuid->toString();
        $res = $this->formService->validateClient($data);
//dd($res,$data);
        if ($res && $res['status'] == 'success' && ($res['aobyte_status'] == 'SUCCESS' || $res['aobyte_status'] == 'EXPIRED_PASSPORT' || $res['aobyte_status'] == 'UNMATCHED_PASSPORT')) {
            $createVerifyEmailData['email']     = $data['email'];
            $createVerifyEmailData['uuid']      = $data['uuid'];
            $this->verifyrepository->setVerifyEmail($createVerifyEmailData);
            $uuid = $data['uuid'];
            $validation = $res['aobyte_status'];
            session(["forminfo.uuid" => $data['uuid'], 'forminfo.status' => $validation]);
            return redirect()->route(app()->getLocale() . '::client-phone-validate');
        } else {
            if ($res['status'] == 'success') {
                if ($res['aobyte_status'] == "UNMATCHED_PASSPORT") {
                    $error_code = 4073; // unmatched passport
                } elseif ($res['aobyte_status'] == "UNMATCHED_PHONE_NUMBER") {
                    $error_code = 4075; //unmatched phone number
                } elseif ($res['aobyte_status'] == "UNMATCHED_EMAIL") {
                    $error_code = 4076; //unmatched email
                } else {
                    $error_code = 0013; //unknown error
                }

                return redirect()->back()->withErrors(['verify_error' => __("error with code " . $error_code)]);
            } else {
                return redirect()->back()->withErrors(['verify_error' => __("error with code " . $res['code'])]);
            }
        }
    }

    public function confirmEmail(Request $request)
    {
        $res = $this->verifyrepository->checkVerifyEmail($request);
        Log::info('confirmEmail result ', ['result' => $res]);
        if ($res && !$res['error']) {
            $result = $this->verifyrepository->setVerifyPhone($request);
            if ($result) {
                $res = $this->formService->sendSms($request->token, $result);
                Log::info('Sms Send Response : ', ['Response' => $res, 'uuid' => $request->token]);
            }
            return view('clients::public.success_email_verify');
        } else {
            if ($res['error']['message'] == 'email already verified') {
                return view('clients::public.error_already_verified');
            } else {
                return view('clients::public.error_email_verify');
            }
        }
    }

    public function checkPhone(VerifyPhoneRequest $request)
    {
        $res = $this->verifyrepository->checkVerifyPhone($request);

        if (!$res['error'] && $res->count() > 0) {
            Log::info('verifyPhone : ', ['result' => $res]);

            if (Session::has('forminfo.status') && (Session::get('forminfo.status') == "EXPIRED_PASSPORT" || Session::get('forminfo.status') == "UNMATCHED_PASSPORT")) {
                return redirect()->route(app()->getLocale() . '::update_passport');
            }

            if (Session::has('forminfo.formtype') && Session::get('forminfo.formtype') == 1) {
                return redirect()->route(app()->getLocale() . '::form1');
            } elseif (Session::has('forminfo.formtype') && Session::get('forminfo.formtype') == 2) {
                return redirect()->route(app()->getLocale() . '::form2step1');
            } elseif (Session::has('forminfo.formtype') && Session::get('forminfo.formtype') == 3) {
                return redirect()->route(app()->getLocale() . '::form3');
            }
        } else {
            return redirect()->back()->withErrors(['phoneverify' => __("phone dont verifying")]);
        }
        return view('clients::public.enter_phone_code');
    }


    public function reSendCode(ReSendPhoneCodeRequest $request)
    {

        $res = $this->verifyrepository->reSendCode($request);
        if ($res && !is_array($res)) {
            Log::info('reSendCode : ', ['result' => $res]);
            $res = $this->formService->sendSms($request->client_token, $res);
            if ($res && array_key_exists('success', $res) && $res['success'] == 'true') {
                Log::info('Sms Send Response:', ['Response' => $res]);
                return redirect()->back()->with(['sms_success' => 'sms sent']);
            } else {
                return redirect()->back()->withErrors(['error' => __("sms dont sent")]);
            }
        } else {
            return redirect()->back()->withErrors($res);
        }
        //return redirect()->back()->withErrors(['error' => __("sms dont sent")]);
    }

    public function showPhoneVerify()
    {
        $client_data = array();
        if (request()->session()->get('forminfo.uuid')) {
            $uuid = request()->session()->get('forminfo.uuid');
            $client = Client::whereUuid($uuid)->first();
            $client_data = json_decode($client->data, true);
        }
        return view('clients::public.enter_phone_code')->with(compact('client_data'));
    }
}
