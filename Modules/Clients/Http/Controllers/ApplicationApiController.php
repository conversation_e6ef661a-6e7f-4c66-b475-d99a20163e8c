<?php

namespace TypiCMS\Modules\Clients\Http\Controllers;

use DateTime;
use Eluceo\iCal\Domain\ValueObject\Timestamp;
use Exception;
use TypiCMS\Modules\Core\Http\Controllers\BaseApiController;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use TypiCMS\Modules\Clients\Models\Client;
use TypiCMS\Modules\Core\Models\User;
use TypiCMS\Modules\Core\Facades\Menus;
use TypiCMS\Modules\Core\Facades\Menulinks;
use TypiCMS\Modules\Clients\Http\Requests\CashMeRequest;

class ApplicationApiController extends BaseApiController
{
    public function formList(){
        $lang = app()->getLocale();
        $menu = Menus::getMenu('_app_menu');
        $response = [] ;
        foreach( $menu->menulinks as $menulink) {
            $class  = explode('.', $menulink->class ?? '');
            $response_item = 
            [
                "label"=>$menulink->title,
                "fortype"=>count($class)   ?  end( $class ):'',
                "user_data" => true,
                "type" => count($class)  ?  $class[0]:'',
                'children'=>[]
            ];
            if($menulink->items->count() > 0){
                foreach($menulink->items as $item)
                {
                    $class  =explode('.', $item->class ?? '');
                    $response_item['children'][] =[
                        "label"=>$item->title,
                        "fortype"=>count($class)  ?  end( $class ):'',
                        "user_data" => true,
                        "type" =>count($class)  ?  $class[0]:'',
                        "url"=>current( $class ) == 'link' ? url($item->href):''
                    ];
                }
            }
            $response[] =  $response_item;
        }
        return $response;
    }
    
    public function formListOld()
    {
        $lang = app()->getLocale();

        $response = [
            [
                "label" => __('LOAN APPLICATION'),
                //"fortype" => 4,
                "user_data" => false,
                "children" => [
                    [
                        "label" => __("business"),
                        "type" => "gc-app",
                        "user_data" => false,
                        "fortype" => "business"
                    ],
                    [
                        "label" => __("agricultural"),
                        "type" => "gc-app",
                        "user_data" => false,
                        "fortype" => "agricultural"
                    ],
                    [
                        "label" => "ՍՊԱՌՈՂԱԿԱՆ ՎԱՐԿ",
                        "type" => "cashme",
                        "user_data" => false,
                        "fortype" => "CONSUMER-LOAN"
                    ],
                    [
                        "label" => "ՀԻՓՈԹԵՔԱՅԻՆ ՎԱՐԿ",
                        "type" => "cashme",
                        "user_data" => false,
                        "fortype" => "MORTGAGE LOAN"
                    ]
                ]
            ],
            [
                "label" => __('OBTAINING INFORMATION / CONSENT'),
                "type" => "gc-app",
                "user_data" => true,
                "fortype" => 2,
                "children" => []
            ],
            [
                "label" => __('OTHER'),
                "type" => "gc-app",
                "user_data" => true,
                "fortype" => 3,
                "children" => []
            ],
            [
                "label" =>  __('APPLICATION PROTEST'),
                "type" => "gc-app",
                "user_data" => true,
                "fortype" => 1,
                "children" => []
            ]
        ];
        return $response;
    }

    public function register(CashMeRequest $cashMeRequest)
    {
        $lang = app()->getLocale();
        $dataToSave = $cashMeRequest->validated();
        
        if ($dataToSave['formtype'] != 'business' && $dataToSave['formtype'] != 'agricultural') {
            $dataToSave['data']['status'] = "SUCCESS";
            $uuid = Str::uuid();
            $clinet = Client::create(
                [
                    'source'    => 'cashme',
                    'uuid'      => $uuid->toString(),
                    'data'      => json_encode($dataToSave['data']),
                    'form_info' => json_encode(['formtype' => $dataToSave['formtype']])
                ]
            );
            $url = route($lang.'::webhook', ['uuid' =>  $clinet->uuid]);
        }else{
            $url = route($lang.'::webhook', ['uuid' =>  $dataToSave['formtype']]);
        }

        return compact('url');
    }


    public function mulberryStatus($tracking)
    {
        try {
            $lang = app()->getLocale();
            $client =  Client::where('tracking_id',$tracking)->first();
            $return =[];
            $res =[];
            if($client){
                $sendData = [
                    "jsonrpc"=> "2.0",
                    "method"=> "track_task",
                    "params"=> [
                        "tracking_id"=> $tracking,
                    ],
                    "id"=> time()
                ];

                $statusMap = config('typicms.modules.clients.statusmap');

                $json = json_encode($sendData);
                $postData = $json;
                $mulberry_url = config('typicms.mulberry_track_url') ?? config('typicms.mulberry_track');
                $request_url = $mulberry_url;

                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => $request_url,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_SSL_VERIFYPEER => false,
                    CURLOPT_SSL_VERIFYHOST => false,
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 40,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_POSTFIELDS => $postData,
                    CURLOPT_HTTPHEADER => array(
                        'Content-Type: application/json'
                    ),
                ));

                $response = curl_exec($curl);
                $curl_errno = '';
                $curl_error = '';
                if(! $response ){
                    $curl_errno = curl_errno($curl);
                    $curl_error = curl_error($curl);
                }
                $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
                curl_close($curl);
                $json_resp = json_decode($response, true);
                if(isset($json_resp) && isset($json_resp["result"]) && isset($json_resp["result"]['files'])){
                    unset($json_resp["result"]['files']);
                }
                Log::info("Mulberry application request for tracking id  {$tracking} ", ['Request' => $postData,'send_url'=>$mulberry_url]);
                Log::info("Mulberry application Response for tracking id  {$tracking} ", ['Response' => $json_resp, 'http_code' => $httpCode, 'curl_errno'=>$curl_errno , 'curl_error'=>$curl_error]);
                if($json_resp && isset($json_resp["result"]) && isset($json_resp["result"]["created"]) && isset($json_resp["result"]["status"])){
                    $forminfo = json_decode($client->form_info);
                    $formtype = $forminfo->formtype;
                    $dt = $json_resp["result"]["created"];
                    $datetime = new DateTime($dt);
                    $date = $datetime->format('d/m/Y');
                    $time = $datetime->format('H:i');

                    $rawStatus =  $json_resp["result"]["status"]["title"]["am"] ?? $json_resp["result"]["status"]["name"];
                    
                    $res['status'] = $statusMap[$rawStatus] ?? $statusMap['*'];
                    $res['date'] = $date;
                    $res['time'] = $time;
                    $res['title'] = $json_resp["result"]["data"]["sys_title"]["am"] ?? __("formtype {$formtype}");
                    
                    $res['_raw_status'] = $rawStatus;
                }else{
                     $return = ["status"=>"error","message"=>"wrong proccesing data"];
                     return response()->json($return,400);
                }
            }else{
                $return = ["status"=>"error","message"=>"client not found"];
                return response()->json($return,404);
            }

            $return =  ["status"=>"success","data"=>$res];
            return response()->json($return,200);
        } catch (Exception $e) {
            $return = ["status"=>"error","message"=>"System Error"];
            Log::Error("Mulberry application api request Error  {$tracking} ", ['message' => $e->getMessage(),"trace"=>$e->getTraceAsString()]);
            return response()->json($return,500);
        }
        
    }
}
