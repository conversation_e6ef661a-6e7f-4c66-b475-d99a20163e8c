<?php

namespace TypiCMS\Modules\Clients\Http\Controllers;

use TypiCMS\Modules\Clients\Models\Client;
use Illuminate\Support\Facades\Session;

class WebhookController
{
    public function process($uuid)
    {
        $lang = app()->getLocale();
        session()->forget(['forminfo','source']);
        if ($uuid != "business" && $uuid != "agricultural") {
            $client = Client::where('uuid', $uuid)->firstOrFail();
            $forminfo = json_decode($client->form_info);
            $formtype = $forminfo->formtype;
            session(["forminfo.uuid" =>  $client->uuid, 'forminfo.formtype' => $formtype, 'source' => 'cashme', 'forminfo.status' => 'SUCCESS']);
            if (Session::has('forminfo.formtype') && Session::get('forminfo.formtype') == 1) {
                return redirect()->route($lang . '::form1');
            } elseif (Session::has('forminfo.formtype') && Session::get('forminfo.formtype') == 2) {
                return redirect()->route($lang . '::form2step1');
            } elseif (Session::has('forminfo.formtype') && Session::get('forminfo.formtype') == 3) {
                return redirect()->route($lang . '::form3');
            }
        } else {
            session(['forminfo.formtype' => $uuid, 'source' => 'cashme']);
            return redirect()->route($lang .'::'.$uuid);
        }
    }
}
