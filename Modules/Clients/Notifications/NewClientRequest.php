<?php

namespace TypiCMS\Modules\Clients\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use TypiCMS\Modules\Core\Facades\TypiCMS;

class NewClientRequest extends Notification
{
    use Queueable;

    private $client_data;
    private $client_form;

    /**
     * Create a new notification instance.
     *
     * @param mixed $client
     */
    public function __construct($client_data,$client_form)
    {
        $this->client_data = $client_data;
        $this->client_form = $client_form;

    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage())
            ->subject('['.TypiCMS::title().'] '.__('New application request'))
            ->markdown('clients::mail.new-form1-request', ['client_data' => $this->client_data,'client_form'=>$this->client_form]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
        ];
    }
}
