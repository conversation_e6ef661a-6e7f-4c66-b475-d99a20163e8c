<?php

namespace TypiCMS\Modules\Types\Composers;

use Illuminate\Support\Facades\Gate;
use Illuminate\View\View;
use Maatwebsite\Sidebar\SidebarGroup;
use Maatwebsite\Sidebar\SidebarItem;

class SidebarViewComposer
{
    public function compose(View $view)
    {
        if (Gate::denies('read types')) {
            return;
        }
        $view->sidebar->group(__('Content'), function (SidebarGroup $group) {
            $group->id = 'content';
            $group->weight = 30;
            $group->addItem(__('Types'), function (SidebarItem $item) {
                $item->id = 'types';
                $item->icon = config('typicms.modules.types.sidebar.icon');
                $item->weight = config('typicms.modules.types.sidebar.weight');
                $item->route('admin::index-types');
                $item->append('admin::create-type');
            });
        });
    }
}
