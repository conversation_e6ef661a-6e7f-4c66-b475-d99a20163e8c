<?php

namespace TypiCMS\Modules\Types\Http\Controllers;

use Illuminate\View\View;
use TypiCMS\Modules\Core\Http\Controllers\BasePublicController;
use TypiCMS\Modules\Types\Models\Type;

class PublicController extends BasePublicController
{
    public function index(): View
    {
        $models = Type::published()->order()->with('image')->get();

        return view('types::public.index')
            ->with(compact('models'));
    }

    /*public function show($slug): View
    {
        $model = Type::published()->whereSlugIs($slug)->firstOrFail();

        return view('types::public.show')
            ->with(compact('model'));
    }*/
}
