<?php

namespace TypiCMS\Modules\Types\Http\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Maatwebsite\Excel\Facades\Excel;
use TypiCMS\Modules\Core\Http\Controllers\BaseAdminController;
use TypiCMS\Modules\Types\Exports\Export;
use TypiCMS\Modules\Types\Http\Requests\FormRequest;
use TypiCMS\Modules\Types\Models\Type;

class AdminController extends BaseAdminController
{
    public function index(): View
    {
        return view('types::admin.index');
    }

    public function export(Request $request)
    {
        $filename = date('Y-m-d').' '.config('app.name').' types.xlsx';

        return Excel::download(new Export(), $filename);
    }

    public function create(): View
    {
        $model = new Type();

        return view('types::admin.create')
            ->with(compact('model'));
    }

    public function edit(type $type): View
    {
        return view('types::admin.edit')
            ->with(['model' => $type]);
    }

    public function store(FormRequest $request): RedirectResponse
    {
        $type = Type::create($request->validated());

        return $this->redirect($request, $type)
            ->withMessage(__('Item successfully created.'));
    }

    public function update(type $type, FormRequest $request): RedirectResponse
    {
        $type->update($request->validated());

        return $this->redirect($request, $type)
            ->withMessage(__('Item successfully updated.'));
    }
}
