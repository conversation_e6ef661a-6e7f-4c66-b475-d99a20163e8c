<?php

namespace TypiCMS\Modules\Types\Providers;

use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;
use TypiCMS\Modules\Core\Facades\TypiCMS;
use TypiCMS\Modules\Types\Http\Controllers\AdminController;
use TypiCMS\Modules\Types\Http\Controllers\ApiController;
use TypiCMS\Modules\Types\Http\Controllers\PublicController;

class RouteServiceProvider extends ServiceProvider
{
    public function map(): void
    {
        /*
         * Front office routes
         */
        if ($page = TypiCMS::getPageLinkedToModule('types')) {
            $middleware = $page->private ? ['public', 'auth'] : ['public'];
            foreach (locales() as $lang) {
                if ($page->isPublished($lang) && $uri = $page->uri($lang)) {
                    Route::middleware($middleware)->prefix($uri)->name($lang.'::')->group(function (Router $router) {
                        $router->get('/', [PublicController::class, 'index'])->name('index-types');
                        //$router->get('{slug}', [PublicController::class, 'show'])->name('type');
                    });
                }
            }
        }

        /*
         * Admin routes
         */
        Route::middleware('admin')->prefix('admin')->name('admin::')->group(function (Router $router) {
            $router->get('types', [AdminController::class, 'index'])->name('index-types')->middleware('can:read types');
            $router->get('types/export', [AdminController::class, 'export'])->name('export-types')->middleware('can:read types');
            $router->get('types/create', [AdminController::class, 'create'])->name('create-type')->middleware('can:create types');
            $router->get('types/{type}/edit', [AdminController::class, 'edit'])->name('edit-type')->middleware('can:read types');
            $router->post('types', [AdminController::class, 'store'])->name('store-type')->middleware('can:create types');
            $router->put('types/{type}', [AdminController::class, 'update'])->name('update-type')->middleware('can:update types');
        });

        /*
         * API routes
         */
        Route::middleware(['api', 'auth:api'])->prefix('api')->group(function (Router $router) {
            $router->get('types', [ApiController::class, 'index'])->middleware('can:read types');
            $router->patch('types/{type}', [ApiController::class, 'updatePartial'])->middleware('can:update types');
            $router->delete('types/{type}', [ApiController::class, 'destroy'])->middleware('can:delete types');
        });
    }
}
