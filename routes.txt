
  ANY       .well-known/change-password ................................................................................................................................................. Illuminate\Routing › RedirectController
  GET|HEAD  _debugbar/assets/javascript ............................................................................................................................. debugbar.assets.js › Barryvdh\Debugbar › AssetController@js
  GET|HEAD  _debugbar/assets/stylesheets .......................................................................................................................... debugbar.assets.css › Barryvdh\Debugbar › AssetController@css
  DELETE    _debugbar/cache/{key}/{tags?} .................................................................................................................... debugbar.cache.delete › Barryvdh\Debugbar › CacheController@delete
  GET|HEAD  _debugbar/clockwork/{id} ................................................................................................................... debugbar.clockwork › Barryvdh\Debugbar › OpenHandlerController@clockwork
  GET|HEAD  _debugbar/open .............................................................................................................................. debugbar.openhandler › Barryvdh\Debugbar › OpenHandlerController@handle
  POST      _ignition/execute-solution ............................................................................................................ ignition.executeSolution › Spatie\LaravelIgnition › ExecuteSolutionController
  GET|HEAD  _ignition/health-check ........................................................................................................................ ignition.healthCheck › Spatie\LaravelIgnition › HealthCheckController
  POST      _ignition/update-config ..................................................................................................................... ignition.updateConfig › Spatie\LaravelIgnition › UpdateConfigController
  GET|HEAD  admin ............................................................................................................................................... admin::index › TypiCMS\Modules › DashboardAdminController@index
  GET|HEAD  admin/_locale/{locale} ................................................................................................................... admin::change-locale › TypiCMS\Modules › LocaleController@setContentLocale
  GET|HEAD  admin/blocks .......................................................................................................................................... admin::index-blocks › TypiCMS\Modules › AdminController@index
  POST      admin/blocks ........................................................................................................................................... admin::store-block › TypiCMS\Modules › AdminController@store
  GET|HEAD  admin/blocks/create .................................................................................................................................. admin::create-block › TypiCMS\Modules › AdminController@create
  PUT       admin/blocks/{block} ................................................................................................................................. admin::update-block › TypiCMS\Modules › AdminController@update
  GET|HEAD  admin/blocks/{block}/edit ................................................................................................................................ admin::edit-block › TypiCMS\Modules › AdminController@edit
  GET|HEAD  admin/cache/clear ......................................................................................................................... admin::clear-cache › TypiCMS\Modules › SettingsAdminController@clearCache
  GET|HEAD  admin/clients ................................................................................................................. admin::index-clients › TypiCMS\Modules\Clients\Http\Controllers\AdminController@index
  POST      admin/clients .................................................................................................................. admin::store-client › TypiCMS\Modules\Clients\Http\Controllers\AdminController@store
  GET|HEAD  admin/clients/create ......................................................................................................... admin::create-client › TypiCMS\Modules\Clients\Http\Controllers\AdminController@create
  GET|HEAD  admin/clients/export ........................................................................................................ admin::export-clients › TypiCMS\Modules\Clients\Http\Controllers\AdminController@export
  PUT       admin/clients/{client} ....................................................................................................... admin::update-client › TypiCMS\Modules\Clients\Http\Controllers\AdminController@update
  GET|HEAD  admin/clients/{client}/edit ...................................................................................................... admin::edit-client › TypiCMS\Modules\Clients\Http\Controllers\AdminController@edit
  GET|HEAD  admin/dashboard ............................................................................................................................. admin::dashboard › TypiCMS\Modules › DashboardAdminController@dashboard
  GET|HEAD  admin/files ....................................................................................................................................... admin::index-files › TypiCMS\Modules › FilesAdminController@index
  PUT       admin/files/{file} ............................................................................................................................... admin::update-file › TypiCMS\Modules › FilesAdminController@update
  GET|HEAD  admin/files/{file}/edit .............................................................................................................................. admin::edit-file › TypiCMS\Modules › FilesAdminController@edit
  GET|HEAD  admin/menus ....................................................................................................................................... admin::index-menus › TypiCMS\Modules › MenusAdminController@index
  POST      admin/menus ........................................................................................................................................ admin::store-menu › TypiCMS\Modules › MenusAdminController@store
  GET|HEAD  admin/menus/create ............................................................................................................................... admin::create-menu › TypiCMS\Modules › MenusAdminController@create
  PUT       admin/menus/{menu} ............................................................................................................................... admin::update-menu › TypiCMS\Modules › MenusAdminController@update
  GET|HEAD  admin/menus/{menu}/edit .............................................................................................................................. admin::edit-menu › TypiCMS\Modules › MenusAdminController@edit
  POST      admin/menus/{menu}/menulinks ............................................................................................................... admin::store-menulink › TypiCMS\Modules › MenulinksAdminController@store
  GET|HEAD  admin/menus/{menu}/menulinks/create ...................................................................................................... admin::create-menulink › TypiCMS\Modules › MenulinksAdminController@create
  PUT       admin/menus/{menu}/menulinks/{menulink} .................................................................................................. admin::update-menulink › TypiCMS\Modules › MenulinksAdminController@update
  GET|HEAD  admin/menus/{menu}/menulinks/{menulink}/edit ................................................................................................. admin::edit-menulink › TypiCMS\Modules › MenulinksAdminController@edit
  GET|HEAD  admin/pages ....................................................................................................................................... admin::index-pages › TypiCMS\Modules › PagesAdminController@index
  POST      admin/pages ........................................................................................................................................ admin::store-page › TypiCMS\Modules › PagesAdminController@store
  GET|HEAD  admin/pages/create ............................................................................................................................... admin::create-page › TypiCMS\Modules › PagesAdminController@create
  PUT       admin/pages/{page} ............................................................................................................................... admin::update-page › TypiCMS\Modules › PagesAdminController@update
  GET|HEAD  admin/pages/{page}/edit .............................................................................................................................. admin::edit-page › TypiCMS\Modules › PagesAdminController@edit
  POST      admin/pages/{page}/sections ......................................................................................................... admin::store-page_section › TypiCMS\Modules › PageSectionsAdminController@store
  GET|HEAD  admin/pages/{page}/sections/create ................................................................................................ admin::create-page_section › TypiCMS\Modules › PageSectionsAdminController@create
  POST      admin/pages/{page}/sections/sort ..................................................................................................... admin::sort-page_sections › TypiCMS\Modules › PageSectionsAdminController@sort
  PUT       admin/pages/{page}/sections/{section} ............................................................................................. admin::update-page_section › TypiCMS\Modules › PageSectionsAdminController@update
  GET|HEAD  admin/pages/{page}/sections/{section}/edit ............................................................................................ admin::edit-page_section › TypiCMS\Modules › PageSectionsAdminController@edit
  GET|HEAD  admin/payments .............................................................................................................. admin::index-payments › TypiCMS\Modules\Payments\Http\Controllers\AdminController@index
  POST      admin/payments ............................................................................................................... admin::store-payment › TypiCMS\Modules\Payments\Http\Controllers\AdminController@store
  GET|HEAD  admin/payments/create ...................................................................................................... admin::create-payment › TypiCMS\Modules\Payments\Http\Controllers\AdminController@create
  GET|HEAD  admin/payments/export ..................................................................................................... admin::export-payments › TypiCMS\Modules\Payments\Http\Controllers\AdminController@export
  PUT       admin/payments/{payment} ................................................................................................... admin::update-payment › TypiCMS\Modules\Payments\Http\Controllers\AdminController@update
  GET|HEAD  admin/payments/{payment}/edit .................................................................................................. admin::edit-payment › TypiCMS\Modules\Payments\Http\Controllers\AdminController@edit
  GET|HEAD  admin/roles ....................................................................................................................................... admin::index-roles › TypiCMS\Modules › RolesAdminController@index
  POST      admin/roles ........................................................................................................................................ admin::store-role › TypiCMS\Modules › RolesAdminController@store
  GET|HEAD  admin/roles/create ............................................................................................................................... admin::create-role › TypiCMS\Modules › RolesAdminController@create
  PUT       admin/roles/{role} ............................................................................................................................... admin::update-role › TypiCMS\Modules › RolesAdminController@update
  GET|HEAD  admin/roles/{role}/edit .............................................................................................................................. admin::edit-role › TypiCMS\Modules › RolesAdminController@edit
  GET|HEAD  admin/sections ..................................................................................................................... admin::index-page_sections › TypiCMS\Modules › PageSectionsAdminController@index
  DELETE    admin/sections/{section} ................................................................................................ admin::destroy-page_section › TypiCMS\Modules › PageSectionsAdminController@destroyMultiple
  GET|HEAD  admin/settings .............................................................................................................................. admin::index-settings › TypiCMS\Modules › SettingsAdminController@index
  POST      admin/settings .............................................................................................................................. admin::update-settings › TypiCMS\Modules › SettingsAdminController@save
  PATCH     admin/settings .............................................................................................................. admin::delete-image-in-settings › TypiCMS\Modules › SettingsAdminController@deleteImage
  GET|HEAD  admin/tags .......................................................................................................................................... admin::index-tags › TypiCMS\Modules › TagsAdminController@index
  POST      admin/tags ........................................................................................................................................... admin::store-tag › TypiCMS\Modules › TagsAdminController@store
  GET|HEAD  admin/tags/create .................................................................................................................................. admin::create-tag › TypiCMS\Modules › TagsAdminController@create
  PUT       admin/tags/{tag} ................................................................................................................................... admin::update-tag › TypiCMS\Modules › TagsAdminController@update
  GET|HEAD  admin/tags/{tag}/edit .................................................................................................................................. admin::edit-tag › TypiCMS\Modules › TagsAdminController@edit
  GET|HEAD  admin/taxonomies ........................................................................................................................ admin::index-taxonomies › TypiCMS\Modules › TaxonomiesAdminController@index
  POST      admin/taxonomies .......................................................................................................................... admin::store-taxonomy › TypiCMS\Modules › TaxonomiesAdminController@store
  GET|HEAD  admin/taxonomies/create ................................................................................................................. admin::create-taxonomy › TypiCMS\Modules › TaxonomiesAdminController@create
  GET|HEAD  admin/taxonomies/export ............................................................................................................... admin::export-taxonomies › TypiCMS\Modules › TaxonomiesAdminController@export
  PUT       admin/taxonomies/{taxonomy} ............................................................................................................. admin::update-taxonomy › TypiCMS\Modules › TaxonomiesAdminController@update
  GET|HEAD  admin/taxonomies/{taxonomy}/edit ............................................................................................................ admin::edit-taxonomy › TypiCMS\Modules › TaxonomiesAdminController@edit
  GET|HEAD  admin/taxonomies/{taxonomy}/terms ................................................................................................................. admin::index-terms › TypiCMS\Modules › TermsAdminController@index
  POST      admin/taxonomies/{taxonomy}/terms .................................................................................................................. admin::store-term › TypiCMS\Modules › TermsAdminController@store
  GET|HEAD  admin/taxonomies/{taxonomy}/terms/create ......................................................................................................... admin::create-term › TypiCMS\Modules › TermsAdminController@create
  PUT       admin/taxonomies/{taxonomy}/terms/{term} ......................................................................................................... admin::update-term › TypiCMS\Modules › TermsAdminController@update
  GET|HEAD  admin/taxonomies/{taxonomy}/terms/{term}/edit ........................................................................................................ admin::edit-term › TypiCMS\Modules › TermsAdminController@edit
  GET|HEAD  admin/translations .................................................................................................................. admin::index-translations › TypiCMS\Modules › TranslationsAdminController@index
  POST      admin/translations ................................................................................................................... admin::store-translation › TypiCMS\Modules › TranslationsAdminController@store
  GET|HEAD  admin/translations/create .......................................................................................................... admin::create-translation › TypiCMS\Modules › TranslationsAdminController@create
  PUT       admin/translations/{translation} ................................................................................................... admin::update-translation › TypiCMS\Modules › TranslationsAdminController@update
  GET|HEAD  admin/translations/{translation}/edit .................................................................................................. admin::edit-translation › TypiCMS\Modules › TranslationsAdminController@edit
  GET|HEAD  admin/users ....................................................................................................................................... admin::index-users › TypiCMS\Modules › UsersAdminController@index
  POST      admin/users ........................................................................................................................................ admin::store-user › TypiCMS\Modules › UsersAdminController@store
  GET|HEAD  admin/users/create ............................................................................................................................... admin::create-user › TypiCMS\Modules › UsersAdminController@create
  GET|HEAD  admin/users/export .............................................................................................................................. admin::export-users › TypiCMS\Modules › UsersAdminController@export
  GET|HEAD  admin/users/{id}/impersonate ................................................................................................................ admin::impersonate-user › TypiCMS\Modules › ImpersonateController@start
  PUT       admin/users/{user} ............................................................................................................................... admin::update-user › TypiCMS\Modules › UsersAdminController@update
  GET|HEAD  admin/users/{user}/edit .............................................................................................................................. admin::edit-user › TypiCMS\Modules › UsersAdminController@edit
  GET|HEAD  admin/{uri} ......................................................................................................................... admin::show-404-page-in-admin › TypiCMS\Modules › PagesAdminController@notFound
  GET|HEAD  api/blocks .................................................................................................................................................................... TypiCMS\Modules › ApiController@index
  PATCH     api/blocks/{block} .................................................................................................................................................... TypiCMS\Modules › ApiController@updatePartial
  DELETE    api/blocks/{block} .......................................................................................................................................................... TypiCMS\Modules › ApiController@destroy
  GET|HEAD  api/clients ............................................................................................................................................ TypiCMS\Modules\Clients\Http\Controllers\ApiController@index
  PATCH     api/clients/{client} ........................................................................................................................... TypiCMS\Modules\Clients\Http\Controllers\ApiController@updatePartial
  DELETE    api/clients/{client} ................................................................................................................................. TypiCMS\Modules\Clients\Http\Controllers\ApiController@destroy
  GET|HEAD  api/files ................................................................................................................................................................ TypiCMS\Modules › FilesApiController@index
  POST      api/files ................................................................................................................................................................ TypiCMS\Modules › FilesApiController@store
  DELETE    api/files/{file} ....................................................................................................................................................... TypiCMS\Modules › FilesApiController@destroy
  PATCH     api/files/{ids} ........................................................................................................................................................... TypiCMS\Modules › FilesApiController@move
  GET|HEAD  api/history ............................................................................................................................................................ TypiCMS\Modules › HistoryApiController@index
  DELETE    api/history .......................................................................................................................................................... TypiCMS\Modules › HistoryApiController@destroy
  GET|HEAD  api/menus ................................................................................................................................................................ TypiCMS\Modules › MenusApiController@index
  PATCH     api/menus/{menu} ................................................................................................................................................. TypiCMS\Modules › MenusApiController@updatePartial
  DELETE    api/menus/{menu} ....................................................................................................................................................... TypiCMS\Modules › MenusApiController@destroy
  GET|HEAD  api/menus/{menu}/menulinks ........................................................................................................................................... TypiCMS\Modules › MenulinksApiController@index
  POST      api/menus/{menu}/menulinks/sort ....................................................................................................................................... TypiCMS\Modules › MenulinksApiController@sort
  PATCH     api/menus/{menu}/menulinks/{menulink} ........................................................................................................................ TypiCMS\Modules › MenulinksApiController@updatePartial
  DELETE    api/menus/{menu}/menulinks/{menulink} .............................................................................................................................. TypiCMS\Modules › MenulinksApiController@destroy
  GET|HEAD  api/pages ................................................................................................................................................................ TypiCMS\Modules › PagesApiController@index
  GET|HEAD  api/pages/links-for-editor ...................................................................................................................................... TypiCMS\Modules › PagesApiController@linksForEditor
  POST      api/pages/sort ............................................................................................................................................................ TypiCMS\Modules › PagesApiController@sort
  PATCH     api/pages/{page} ................................................................................................................................................. TypiCMS\Modules › PagesApiController@updatePartial
  DELETE    api/pages/{page} ....................................................................................................................................................... TypiCMS\Modules › PagesApiController@destroy
  GET|HEAD  api/pages/{page}/sections ......................................................................................................................................... TypiCMS\Modules › PageSectionsApiController@index
  PATCH     api/pages/{page}/sections/{section} ....................................................................................................................... TypiCMS\Modules › PageSectionsApiController@updatePartial
  DELETE    api/pages/{page}/sections/{section} ............................................................................................................................. TypiCMS\Modules › PageSectionsApiController@destroy
  GET|HEAD  api/payments .......................................................................................................................................... TypiCMS\Modules\Payments\Http\Controllers\ApiController@index
  PATCH     api/payments/{payment} ........................................................................................................................ TypiCMS\Modules\Payments\Http\Controllers\ApiController@updatePartial
  DELETE    api/payments/{payment} .............................................................................................................................. TypiCMS\Modules\Payments\Http\Controllers\ApiController@destroy
  GET|HEAD  api/roles ................................................................................................................................................................ TypiCMS\Modules › RolesApiController@index
  PATCH     api/roles/{role} ................................................................................................................................................. TypiCMS\Modules › RolesApiController@updatePartial
  DELETE    api/roles/{role} ....................................................................................................................................................... TypiCMS\Modules › RolesApiController@destroy
  GET|HEAD  api/tags .................................................................................................................................................................. TypiCMS\Modules › TagsApiController@index
  GET|HEAD  api/tags-list .......................................................................................................................................................... TypiCMS\Modules › TagsApiController@tagsList
  PATCH     api/tags/{tag} .................................................................................................................................................... TypiCMS\Modules › TagsApiController@updatePartial
  DELETE    api/tags/{tag} .......................................................................................................................................................... TypiCMS\Modules › TagsApiController@destroy
  GET|HEAD  api/taxonomies ...................................................................................................................................................... TypiCMS\Modules › TaxonomiesApiController@index
  PATCH     api/taxonomies/{taxonomy} ................................................................................................................................... TypiCMS\Modules › TaxonomiesApiController@updatePartial
  DELETE    api/taxonomies/{taxonomy} ......................................................................................................................................... TypiCMS\Modules › TaxonomiesApiController@destroy
  GET|HEAD  api/taxonomies/{taxonomy}/terms .......................................................................................................................................... TypiCMS\Modules › TermsApiController@index
  PATCH     api/taxonomies/{taxonomy}/terms/{term} ........................................................................................................................... TypiCMS\Modules › TermsApiController@updatePartial
  DELETE    api/taxonomies/{taxonomy}/terms/{term} ................................................................................................................................. TypiCMS\Modules › TermsApiController@destroy
  GET|HEAD  api/translations .................................................................................................................................................. TypiCMS\Modules › TranslationsApiController@index
  PATCH     api/translations/{translation} ............................................................................................................................ TypiCMS\Modules › TranslationsApiController@updatePartial
  DELETE    api/translations/{translation} .................................................................................................................................. TypiCMS\Modules › TranslationsApiController@destroy
  GET|HEAD  api/users ................................................................................................................................................................ TypiCMS\Modules › UsersApiController@index
  POST      api/users/current/updatepreferences .......................................................................................................................... TypiCMS\Modules › UsersApiController@updatePreferences
  DELETE    api/users/{user} ....................................................................................................................................................... TypiCMS\Modules › UsersApiController@destroy
  GET|HEAD  en/login ................................................................................................................................................ en::login › TypiCMS\Modules › LoginController@showLoginForm
  POST      en/login ................................................................................................................................................. en::login-action › TypiCMS\Modules › LoginController@login
  POST      en/logout ..................................................................................................................................................... en::logout › TypiCMS\Modules › LoginController@logout
  POST      en/password/email ................................................................................................................ en::password.email › TypiCMS\Modules › ForgotPasswordController@sendResetLinkEmail
  GET|HEAD  en/password/reset ............................................................................................................. en::password.request › TypiCMS\Modules › ForgotPasswordController@showLinkRequestForm
  POST      en/password/reset ....................................................................................................................... en::password.reset-action › TypiCMS\Modules › ResetPasswordController@reset
  GET|HEAD  en/password/reset/{token} .............................................................................................................. en::password.reset › TypiCMS\Modules › ResetPasswordController@showResetForm
  GET|HEAD  en/stop-impersonation ............................................................................................................ en::stop-impersonation › TypiCMS\Modules › ImpersonateController@stopImpersonation
  GET|HEAD  hy/login ................................................................................................................................................ hy::login › TypiCMS\Modules › LoginController@showLoginForm
  POST      hy/login ................................................................................................................................................. hy::login-action › TypiCMS\Modules › LoginController@login
  POST      hy/logout ..................................................................................................................................................... hy::logout › TypiCMS\Modules › LoginController@logout
  GET|HEAD  hy/online-form .................................................................................................................. hy::index-clients › TypiCMS\Modules\Clients\Http\Controllers\PublicController@index
  GET|HEAD  hy/online-form/application ........................................................................................................... hy::form1 › TypiCMS\Modules\Clients\Http\Controllers\Form1Controller@showForm1
  POST      hy/online-form/application ..................................................................................................... hy::sendform1 › TypiCMS\Modules\Clients\Http\Controllers\Form1Controller@submitForm1
  GET|HEAD  hy/online-form/application/{uuid} ................................................................................ hy::sendform1preview › TypiCMS\Modules\Clients\Http\Controllers\Form1Controller@submitForm1preview
  POST      hy/online-form/application/{uuid} ........................................................................................ hy::form1mulberry › TypiCMS\Modules\Clients\Http\Controllers\Form1Controller@form1Mulberry
  GET|HEAD  hy/online-form/confirm-email ............................................................................................. hy::confirm-email › TypiCMS\Modules\Clients\Http\Controllers\CashmeController@confirmEmail
  GET|HEAD  hy/online-form/failed .............................................................................................................. hy::form-failed › TypiCMS\Modules\Clients\Http\Controllers\FormController@failed
  GET|HEAD  hy/online-form/obtaining-information ....................................................................................... hy::form2step1 › TypiCMS\Modules\Clients\Http\Controllers\Form2Controller@showForm2Step1
  POST      hy/online-form/obtaining-information ................................................................................. hy::sendform2step1 › TypiCMS\Modules\Clients\Http\Controllers\Form2Controller@submitForm2Step1
  GET|HEAD  hy/online-form/obtaining-information/step2 ................................................................................. hy::form2step2 › TypiCMS\Modules\Clients\Http\Controllers\Form2Controller@showForm2Step2
  POST      hy/online-form/obtaining-information/step2 ........................................................................... hy::sendform2step2 › TypiCMS\Modules\Clients\Http\Controllers\Form2Controller@submitForm2Step2
  GET|HEAD  hy/online-form/obtaining-information/step2/{uuid} ................................................................ hy::sendform2preview › TypiCMS\Modules\Clients\Http\Controllers\Form2Controller@submitForm2preview
  GET|HEAD  hy/online-form/other ................................................................................................................. hy::form3 › TypiCMS\Modules\Clients\Http\Controllers\Form3Controller@showForm3
  POST      hy/online-form/other ........................................................................................................... hy::sendform3 › TypiCMS\Modules\Clients\Http\Controllers\Form3Controller@submitForm3
  GET|HEAD  hy/online-form/other/{uuid} ...................................................................................... hy::sendform3preview › TypiCMS\Modules\Clients\Http\Controllers\Form3Controller@submitForm3preview
  POST      hy/online-form/other/{uuid} .............................................................................................. hy::form3mulberry › TypiCMS\Modules\Clients\Http\Controllers\Form3Controller@form3Mulberry
  POST      hy/online-form/phone-validate ............................................................................................. hy::phone-validate › TypiCMS\Modules\Clients\Http\Controllers\CashmeController@checkPhone
  GET|HEAD  hy/online-form/success ........................................................................................................... hy::form-success › TypiCMS\Modules\Clients\Http\Controllers\FormController@success
  POST      hy/online-form/validate-client .......................................................................................... hy::validate-client › TypiCMS\Modules\Clients\Http\Controllers\CashmeController@checkClient
  GET|HEAD  hy/online-form/validate-phone ................................................................................. hy::client-phone-validate › TypiCMS\Modules\Clients\Http\Controllers\CashmeController@showPhoneVerify
  POST      hy/password/email ................................................................................................................ hy::password.email › TypiCMS\Modules › ForgotPasswordController@sendResetLinkEmail
  GET|HEAD  hy/password/reset ............................................................................................................. hy::password.request › TypiCMS\Modules › ForgotPasswordController@showLinkRequestForm
  POST      hy/password/reset ....................................................................................................................... hy::password.reset-action › TypiCMS\Modules › ResetPasswordController@reset
  GET|HEAD  hy/password/reset/{token} .............................................................................................................. hy::password.reset › TypiCMS\Modules › ResetPasswordController@showResetForm
  GET|HEAD  hy/search-results ...................................................................................................................................... hy::search › TypiCMS\Modules › SearchPublicController@search
  GET|HEAD  hy/stop-impersonation ............................................................................................................ hy::stop-impersonation › TypiCMS\Modules › ImpersonateController@stopImpersonation
  GET|HEAD  hy/{uri} ................................................................................................................................................................ TypiCMS\Modules › PagesPublicController@uri
  GET|HEAD  online-form ..................................................................................................................... en::index-clients › TypiCMS\Modules\Clients\Http\Controllers\PublicController@index
  GET|HEAD  online-form/application .............................................................................................................. en::form1 › TypiCMS\Modules\Clients\Http\Controllers\Form1Controller@showForm1
  POST      online-form/application ........................................................................................................ en::sendform1 › TypiCMS\Modules\Clients\Http\Controllers\Form1Controller@submitForm1
  GET|HEAD  online-form/application/{uuid} ................................................................................... en::sendform1preview › TypiCMS\Modules\Clients\Http\Controllers\Form1Controller@submitForm1preview
  POST      online-form/application/{uuid} ........................................................................................... en::form1mulberry › TypiCMS\Modules\Clients\Http\Controllers\Form1Controller@form1Mulberry
  GET|HEAD  online-form/confirm-email ................................................................................................ en::confirm-email › TypiCMS\Modules\Clients\Http\Controllers\CashmeController@confirmEmail
  GET|HEAD  online-form/failed ................................................................................................................. en::form-failed › TypiCMS\Modules\Clients\Http\Controllers\FormController@failed
  GET|HEAD  online-form/obtaining-information .......................................................................................... en::form2step1 › TypiCMS\Modules\Clients\Http\Controllers\Form2Controller@showForm2Step1
  POST      online-form/obtaining-information .................................................................................... en::sendform2step1 › TypiCMS\Modules\Clients\Http\Controllers\Form2Controller@submitForm2Step1
  GET|HEAD  online-form/obtaining-information/step2 .................................................................................... en::form2step2 › TypiCMS\Modules\Clients\Http\Controllers\Form2Controller@showForm2Step2
  POST      online-form/obtaining-information/step2 .............................................................................. en::sendform2step2 › TypiCMS\Modules\Clients\Http\Controllers\Form2Controller@submitForm2Step2
  GET|HEAD  online-form/obtaining-information/step2/{uuid} ................................................................... en::sendform2preview › TypiCMS\Modules\Clients\Http\Controllers\Form2Controller@submitForm2preview
  GET|HEAD  online-form/other .................................................................................................................... en::form3 › TypiCMS\Modules\Clients\Http\Controllers\Form3Controller@showForm3
  POST      online-form/other .............................................................................................................. en::sendform3 › TypiCMS\Modules\Clients\Http\Controllers\Form3Controller@submitForm3
  GET|HEAD  online-form/other/{uuid} ......................................................................................... en::sendform3preview › TypiCMS\Modules\Clients\Http\Controllers\Form3Controller@submitForm3preview
  POST      online-form/other/{uuid} ................................................................................................. en::form3mulberry › TypiCMS\Modules\Clients\Http\Controllers\Form3Controller@form3Mulberry
  POST      online-form/phone-validate ................................................................................................ en::phone-validate › TypiCMS\Modules\Clients\Http\Controllers\CashmeController@checkPhone
  GET|HEAD  online-form/success .............................................................................................................. en::form-success › TypiCMS\Modules\Clients\Http\Controllers\FormController@success
  POST      online-form/validate-client ............................................................................................. en::validate-client › TypiCMS\Modules\Clients\Http\Controllers\CashmeController@checkClient
  GET|HEAD  online-form/validate-phone .................................................................................... en::client-phone-validate › TypiCMS\Modules\Clients\Http\Controllers\CashmeController@showPhoneVerify
  GET|HEAD  payment/failed ............................................................................................................... en::payment-failed › TypiCMS\Modules\Payments\Http\Controllers\PublicController@failed
  POST      payment/pay/{uuid} .................................................................................................................... en::form-pay › TypiCMS\Modules\Payments\Http\Controllers\PublicController@pay
  GET|HEAD  payment/result ............................................................................................................... en::payment-result › TypiCMS\Modules\Payments\Http\Controllers\PublicController@result
  GET|HEAD  payment/success ............................................................................................................ en::payment-success › TypiCMS\Modules\Payments\Http\Controllers\PublicController@success
  GET|HEAD  ru/login ................................................................................................................................................ ru::login › TypiCMS\Modules › LoginController@showLoginForm
  POST      ru/login ................................................................................................................................................. ru::login-action › TypiCMS\Modules › LoginController@login
  POST      ru/logout ..................................................................................................................................................... ru::logout › TypiCMS\Modules › LoginController@logout
  GET|HEAD  ru/online-form .................................................................................................................. ru::index-clients › TypiCMS\Modules\Clients\Http\Controllers\PublicController@index
  GET|HEAD  ru/online-form/application ........................................................................................................... ru::form1 › TypiCMS\Modules\Clients\Http\Controllers\Form1Controller@showForm1
  POST      ru/online-form/application ..................................................................................................... ru::sendform1 › TypiCMS\Modules\Clients\Http\Controllers\Form1Controller@submitForm1
  GET|HEAD  ru/online-form/application/{uuid} ................................................................................ ru::sendform1preview › TypiCMS\Modules\Clients\Http\Controllers\Form1Controller@submitForm1preview
  POST      ru/online-form/application/{uuid} ........................................................................................ ru::form1mulberry › TypiCMS\Modules\Clients\Http\Controllers\Form1Controller@form1Mulberry
  GET|HEAD  ru/online-form/confirm-email ............................................................................................. ru::confirm-email › TypiCMS\Modules\Clients\Http\Controllers\CashmeController@confirmEmail
  GET|HEAD  ru/online-form/failed .............................................................................................................. ru::form-failed › TypiCMS\Modules\Clients\Http\Controllers\FormController@failed
  GET|HEAD  ru/online-form/obtaining-information ....................................................................................... ru::form2step1 › TypiCMS\Modules\Clients\Http\Controllers\Form2Controller@showForm2Step1
  POST      ru/online-form/obtaining-information ................................................................................. ru::sendform2step1 › TypiCMS\Modules\Clients\Http\Controllers\Form2Controller@submitForm2Step1
  GET|HEAD  ru/online-form/obtaining-information/step2 ................................................................................. ru::form2step2 › TypiCMS\Modules\Clients\Http\Controllers\Form2Controller@showForm2Step2
  POST      ru/online-form/obtaining-information/step2 ........................................................................... ru::sendform2step2 › TypiCMS\Modules\Clients\Http\Controllers\Form2Controller@submitForm2Step2
  GET|HEAD  ru/online-form/obtaining-information/step2/{uuid} ................................................................ ru::sendform2preview › TypiCMS\Modules\Clients\Http\Controllers\Form2Controller@submitForm2preview
  GET|HEAD  ru/online-form/other ................................................................................................................. ru::form3 › TypiCMS\Modules\Clients\Http\Controllers\Form3Controller@showForm3
  POST      ru/online-form/other ........................................................................................................... ru::sendform3 › TypiCMS\Modules\Clients\Http\Controllers\Form3Controller@submitForm3
  GET|HEAD  ru/online-form/other/{uuid} ...................................................................................... ru::sendform3preview › TypiCMS\Modules\Clients\Http\Controllers\Form3Controller@submitForm3preview
  POST      ru/online-form/other/{uuid} .............................................................................................. ru::form3mulberry › TypiCMS\Modules\Clients\Http\Controllers\Form3Controller@form3Mulberry
  POST      ru/online-form/phone-validate ............................................................................................. ru::phone-validate › TypiCMS\Modules\Clients\Http\Controllers\CashmeController@checkPhone
  GET|HEAD  ru/online-form/success ........................................................................................................... ru::form-success › TypiCMS\Modules\Clients\Http\Controllers\FormController@success
  POST      ru/online-form/validate-client .......................................................................................... ru::validate-client › TypiCMS\Modules\Clients\Http\Controllers\CashmeController@checkClient
  GET|HEAD  ru/online-form/validate-phone ................................................................................. ru::client-phone-validate › TypiCMS\Modules\Clients\Http\Controllers\CashmeController@showPhoneVerify
  POST      ru/password/email ................................................................................................................ ru::password.email › TypiCMS\Modules › ForgotPasswordController@sendResetLinkEmail
  GET|HEAD  ru/password/reset ............................................................................................................. ru::password.request › TypiCMS\Modules › ForgotPasswordController@showLinkRequestForm
  POST      ru/password/reset ....................................................................................................................... ru::password.reset-action › TypiCMS\Modules › ResetPasswordController@reset
  GET|HEAD  ru/password/reset/{token} .............................................................................................................. ru::password.reset › TypiCMS\Modules › ResetPasswordController@showResetForm
  GET|HEAD  ru/search-results ...................................................................................................................................... ru::search › TypiCMS\Modules › SearchPublicController@search
  GET|HEAD  ru/stop-impersonation ............................................................................................................ ru::stop-impersonation › TypiCMS\Modules › ImpersonateController@stopImpersonation
  GET|HEAD  ru/{uri} ................................................................................................................................................................ TypiCMS\Modules › PagesPublicController@uri
  GET|HEAD  sanctum/csrf-cookie ..................................................................................................................................................... Laravel\Sanctum › CsrfCookieController@show
  GET|HEAD  search-results ......................................................................................................................................... en::search › TypiCMS\Modules › SearchPublicController@search
  GET|HEAD  sitemap.xml ............................................................................................................................................ sitemap › TypiCMS\Modules › SitemapPublicController@generate
  GET|HEAD  {path} ................................................................................................................................................................................ Bkwld\Croppa › Handler@handle
  GET|HEAD  {uri} ................................................................................................................................................................... TypiCMS\Modules › PagesPublicController@uri

                                                                                                                                                                                                             Showing [237] routes

