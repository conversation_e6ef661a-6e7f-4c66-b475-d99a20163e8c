# VerifyRepository Documentation

## Overview

The `VerifyRepository` class is responsible for handling verification processes in the application, primarily for phone numbers and email addresses. It provides methods for creating verification codes, checking verification status, and managing the verification lifecycle.

## Location

`app/Repositories/VerifyRepository.php`

## Dependencies

- `App\Notifications\GetCustomerNotification`
- `App\Services\SmsGetWay`
- `App\Verify` - Model for storing verification data
- `Carbon\Carbon` - For date/time handling
- `TypiCMS\Modules\Clients\Models\Client`
- `TypiCMS\Modules\Customers\Models\Customer`

## Methods

### 1. `setVerify<PERSON>hone($request)`

Creates a new phone verification record.

**Parameters:**
- `$request` - Request object containing:
  - `phone` - Phone number to verify
  - `token` - Unique identifier (UUID)

**Process:**
1. Generates a random 4-digit code
2. Creates a token by hashing the phone number and code
3. Sets an expiration time (default: 5 minutes)
4. Saves the verification record to the database

**Returns:**
- The generated verification code

### 2. `checkVerifyPhone($request)`

Validates a phone verification code.

**Parameters:**
- `$request` - Request object containing:
  - `token` - UUID of the verification
  - `code` - Verification code entered by the user

**Process:**
1. Checks if there's an unexpired, unverified record matching the token and code
2. If found, marks it as verified

**Returns:**
- The verification record if successful
- Error information if the code is invalid or expired

### 3. `updateVerifyPhone($token, $messageId)`

Updates a phone verification record with a message ID (likely from an SMS service).

**Parameters:**
- `$token` - Verification token
- `$messageId` - ID of the sent message

**Returns:**
- `true` if successful
- Throws an exception on error

### 4. `reSendCode($request)`

Resends a verification code for a client.

**Parameters:**
- `$request` - Request object containing:
  - `client_token` - UUID of the client

**Process:**
1. Checks if there's a verified email record for the client
2. Enforces a rate limit for SMS sending (default: 1 minute)
3. Checks if the client has a failed Mulberry submission (status 2)
4. Creates a new phone verification code

**Returns:**
- The new verification code if successful
- Error information if conditions aren't met

### 5. `setVerifyEmail($data)`

Creates a new email verification record.

**Parameters:**
- `$data` - Array containing:
  - `email` - Email address to verify
  - `uuid` - Unique identifier

**Process:**
1. Generates a random 4-digit code
2. Creates a token by hashing the email and code
3. Sets an expiration time (default: 120 minutes)
4. Saves the verification record to the database

**Returns:**
- The verification record

### 6. `checkVerifyEmail($request)`

Validates an email verification.

**Parameters:**
- `$request` - Request object containing:
  - `token` - UUID of the verification
  - `email` - Email address

**Process:**
1. Checks if there's an unexpired record matching the token and email
2. If found and not already verified, marks it as verified

**Returns:**
- The verification record if successful
- Error information if already verified, invalid, or expired

### 7. `updateVerifyEmail($token)`

Updates an email verification record.

**Parameters:**
- `$token` - Verification token

**Returns:**
- `true` if successful
- Throws an exception on error

### 8. `createPhoneCode($phone, $token)`

Creates a new phone verification code.

**Parameters:**
- `$phone` - Phone number to verify
- `$token` - UUID for the verification

**Process:**
1. Generates a random 4-digit code
2. Creates a token by hashing the phone number and code
3. Sets an expiration time (default: 5 minutes)
4. Saves the verification record to the database

**Returns:**
- The generated verification code

## Database Interaction

The class interacts with the `verifications` table through the `Verify` model, which has the following key fields:

- `type` - Type of verification ('phone' or 'email')
- `code` - Verification code
- `contact` - Contact information (phone number or email)
- `uuid` - Unique identifier linking to the client
- `token` - Hashed token for verification
- `expired_at` - Expiration timestamp
- `verified` - Verification status (0 = unverified, 1 = verified)
- `message` - Optional message ID from SMS service

## Usage in Application Flow

1. **Email Verification Flow:**
   - When a client registers, `setVerifyEmail()` is called to generate a verification code
   - The code is sent to the client's email
   - Client enters the code, and `checkVerifyEmail()` validates it
   - If valid, the email is marked as verified

2. **Phone Verification Flow:**
   - For certain operations, `setVerifyPhone()` or `createPhoneCode()` is called
   - The code is sent to the client's phone via SMS
   - Client enters the code, and `checkVerifyPhone()` validates it
   - If valid, the phone is marked as verified

3. **Resend Functionality:**
   - If a client doesn't receive the code, `reSendCode()` can be used to generate and send a new code
   - Rate limiting is applied to prevent abuse

## Error Handling

The class uses try-catch blocks for error handling and logs errors when they occur. It returns structured error responses that can be used by the controllers to provide feedback to the user.
