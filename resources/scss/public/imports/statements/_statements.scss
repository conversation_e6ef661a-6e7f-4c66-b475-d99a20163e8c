.statements_page {
    margin-top: 20px;

    .tab_section {
        margin: 0 $rowMargin*4;
    }

    .statements_left {
        flex: 0 0 33.333333%;
        max-width: 33.333333%;
        padding: 0 $colPadding*4;
    }

    .statements_right {
        flex: 0 0 66.666666%;
        max-width: 66.666666%;
        padding: 0 $colPadding*4;
        border-left: 2px solid $siteColor;

    }

    .page_title {
        margin-bottom: 30px;
    }

    .tab_section {
        margin-top: 20px;
    }

    .list_weeks {
        > ul {
            @extend %standardList;

            li {
                margin-bottom: 20px;

                a {
                    font-size: 200%;
                    line-height: 27px;
                    color: $black33;
                    font-family: $bold;
                    @extend %siteColorHover;
                }

                &.selected {
                    a {
                        position: relative;
                        color: $siteColor;

                        &:before {
                            content: "";
                            position: absolute;
                            bottom: 0;
                            left: 0;
                            right: 0;
                            width: 100%;
                            height: 1px;
                            background: $siteColor;
                        }
                    }
                }
            }
        }
    }

    .inner_tab {
        margin-bottom: 60px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .tab_block {
        @include overHidden;
        @include animStyle(opacity 0.5s);

        &:not(.selected) {
            height: 0;
            opacity: 0;
        }

        .block_description {
            margin-bottom: 30px;
        }
    }

    @include mediaTo($size991) {
        .page_title {
            margin-bottom: 20px;
        }
        .tab_section {
            margin: 20px $rowMargin 0;
        }

        .statements_left {
            padding: 0 $colPadding;
        }

        .statements_right {
            padding: 0 $colPadding;
        }
        .list_weeks > ul li a {
            font-size: 180%;
            line-height: 24px;
        }
    }
    @include mediaTo($size768) {
        .tab_block .block_description {
            margin-bottom: 20px;
        }
        .inner_tab {
            margin-bottom: 20px;
        }
        .list_weeks > ul li {
            margin-bottom: 10px;

            a {
                font-size: 160%;
                line-height: 21px;
            }
        }
    }
}