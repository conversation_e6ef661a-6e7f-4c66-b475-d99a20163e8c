.blog_inner_page {
    margin-top: 0;

    .inner_top {
        position: relative;
        background-size: cover;
        background-position: center;
        z-index: 2;
        margin-bottom: 30px;
        @include animStyle(transform 1s);

        &:before {
            @extend %coverLayer;
            content: "";
            background: linear-gradient(360deg, #000000 0%, rgba(0, 0, 0, 0) 34.5%);
        }

        .page_container {
            position: relative;
            z-index: 2;
            height: 600px;
            padding: 30px $colPadding;
            display: flex;
            flex-direction: column;
            justify-content: end;
        }

        .news_date {
            font-family: $bold;
            color: $white;
            margin-bottom: 20px;
            font-size: 180%;
            line-height: 24px;
        }

        .page_title {
            color: $white;
            @include textOverflow(7, relative);
        }

        @include mediaTo($size1200) {
            .page_container {
                height: 450px;
            }
        }
        @include mediaTo($size991) {
            margin-bottom: 10px;
            .page_container {
                height: 350px;
            }
            .news_date {
                font-size: 160%;
                line-height: 18px;
                margin-bottom: 7px;
            }
        }
        @include mediaTo($size768) {
            .page_container {
                height: 250px;
                padding: 15px $colPadding;
            }
        }
    }

    .inner_container {
        .page_description,
        .sub_title,
        .sub_title_bold {
            margin: 15px 0;
        }

        img {
            display: block;
            width: 100%;
            height: auto;
            margin: 40px 0;
            border-radius: 10px;
        }
    }

    .comment_block {
        background: $white;
        padding: 30px;
        border-radius: 10px;
    }

    .blog_other {
        margin-top: 80px;
    }

    @include mediaTo($size1200) {
        .blog_other {
            margin-top: 50px;
        }
    }
    @include mediaTo($size991) {
        .blog_other {
            margin-top: 20px;
        }
        .inner_container {
            img {
                margin: 20px 0;
            }
        }
    }

}