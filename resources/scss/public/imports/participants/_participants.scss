.participants_page {
    margin-top: 20px;

    .page_row {
        justify-content: space-between;
        margin-top: 30px;
        margin-bottom: 60px;
    }

    .block_label {
        font-size: 120%;
        line-height: 16px;
        font-family: $bold;
        color: $gray66;
        margin-top: 8px;
    }

    .label_value {
        font-size: 120%;
        line-height: 16px;
        margin-top: 3px;
        color: $siteColor;
    }

    .info_user {
        flex: 0 0 25%;
        max-width: 25%;
        padding: 0 $colPadding;
    }

    .user_description {
        flex: 0 0 66.666666%;
        max-width: 66.666666%;
        padding: 0 $colPadding;
    }

    @include mediaTo($size768) {
        .page_row {
            justify-content: space-between;
            margin-top: 20px;
            margin-bottom: 20px;
        }
        .info_user {
            flex: 0 0 50%;
            max-width: 50%;
            padding: 0 $colPadding;
        }

        .user_description {
            flex: 0 0 50%;
            max-width: 50%;
            padding: 0 $colPadding;
        }
    }
    @include mediaTo($size576) {
        .page_row {
            border-bottom: 1px solid $grayC4;
            padding-bottom: 20px;
        }
        .user_description,
        .info_user {
            flex: 0 0 100%;
            max-width: 100%;
            padding: 0 $colPadding;
        }
    }
}
