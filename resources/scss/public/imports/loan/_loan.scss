.loan_page {
    .tab_block {
        @include overHidden;
        @include animStyle(opacity 0.5s);

        &:not(.selected) {
            height: 0;
            opacity: 0;
        }

        .block_description {
            margin-bottom: 30px;
        }
    }

    .terms_page {
        .inner_tab{
            overflow-x: auto;
        }
        .page_description {
            > a {
                color: $siteColor;
                @extend %opacityHover;
            }
        }

        .loantable {
            margin: 0;
            @extend %standardList;
            max-width: 100%;
            width: 100%;
            min-width: 100%;

            tr {
                width: 100%;
                @extend %standardList;
                padding: 20px 0;
                border-top: 1px solid $grayC4;
                border-bottom: 0;
                display: flex;
                //                flex-wrap: wrap;

                &:last-child {
                    border-bottom: 1px solid $grayC4;
                    margin-bottom: 30px;
                }

                > td {
                    flex: 1;
                    max-width: 50%;
                    font-family: $bold;
                    padding: 0 5px;
                    font-size: 130%;
                    line-height: 17px;
                    color: $gray66;

                    &:first-child {
                        color: $black33;
                    }
                }
            }
        }
    }

    .loan_top {
        background: $siteColor;
        min-height: 480px;
    }

    .page_row {
        display: flex;
        flex-wrap: wrap;
        margin: 0 $rowMargin 0;
    }

    .banner_inner {
        display: flex;
        flex-direction: column;
        height: 100%;
        padding: 0 $colPadding 90px;
        max-width: 360px;
        margin-top: 55px;
    }

    .story_info {
        flex: 0 0 55%;
        max-width: 55%;
        padding: 15px $colPadding;

        .page_title {
            margin-bottom: 4px;
            color: $white;
            display: block;
            @include textOverflow(2, relative)
        }

        .sub_title {
            margin-bottom: 15px;
            color: $white;
            display: block;
            font-family: $regular;
            @include textOverflow(2, relative)
        }

        .sub_description {
            margin-bottom: 30px;
            display: block;
            color: $white;
            font-family: $regular;
            @include textOverflow(4, relative)
        }
    }

    .image_block {
        flex: 0 0 45%;
        max-width: 45%;
        display: flex;

        > img {
            // min-width: 48vw;
            //display: block;
            //width: 48vw;
            display: block;
            //width: 100%;
            height: auto;
            object-fit: cover;
        }
    }

    .btn_get {
        min-width: 180px;
        width: max-content;
        background: $umberC9;
        font-family: $bold;
        border: none;
        min-height: 50px;
        justify-content: center;
        border-radius: 5px;
        display: flex;
        align-items: center;
        padding: 7px;
        font-size: 120%;
        line-height: 16px;
        color: $white;
        text-transform: uppercase;
        @extend %opacityHover;
    }

    .loan_block {
        background: $white;
        border-radius: 10px;
        min-height: 200px;
        padding: 40px 80px;
        width: 100%;
        position: relative;
        z-index: 2;
        margin-top: -90px;
    }

    .loan_params {
        @extend %standardList;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        margin: 0 $rowMargin;
        width: 100%;

        li {
            padding: 0 $colPadding;
            flex: 0 0 33.333333%;
            max-width: 33.333333%;
        }

        .sub_title {
            color: $black33;

            &:after {
                content: "";
                display: block;
                height: 2px;
                background: $siteColor;
                width: 100%;
                max-width: 80px;
                margin: 7px 0;
            }
        }
    }

    .sidebar_loan {
        flex: 0 0 33.333333%;
        max-width: 33.333333%;
        padding: 0 $colPadding*4;
    }

    .content_loan {
        flex: 0 0 66.666666%;
        max-width: 66.666666%;
        padding: 0 $colPadding*4;
        border-left: 2px solid $siteColor;

        .block_description {
            color: $black33;
            margin-bottom: 30px;
        }
    }

    .info_loan {
        margin-top: 60px;

        > .page_row {
            margin: 0 $rowMargin*4;
        }
    }

    .description_loan {
        padding: 0 0 0 20px;
        margin: 20px 0 60px;
        font-size: 130%;
        line-height: 19px;
        color: $black33;

        li {
            margin-bottom: 20px;
        }
    }

    .loan_img {
        flex: 0 0 71%;
        max-width: 71%;

        img {
            display: block;
            width: 100%;
            height: auto;
            border-radius: 10px;
            object-fit: cover;
        }
    }

    .block_title {
        font-size: 160%;
        line-height: 21px;
        font-family: $bold;
        margin-bottom: 8px;
        @include textOverflow(2, relative);

        &:before {
            margin-right: 15px;
            font-size: 170%;
            display: block;
            float: left;
            color: $umberC9;
        }
    }

    .benefits_block {
        margin-top: 138px;
    }

    .inner_list {
        @extend %standardList;
        display: flex;
        flex-wrap: wrap;
        margin: 27px $rowMargin 0;

        li {
            flex: 0 0 33.333333%;
            max-width: 33.333333%;
            padding: 0 $colPadding;
            margin-bottom: 20px;
        }
    }

    .inner_block {
        border-radius: 10px;
        background: $white;
        padding: 30px 25px;
        min-height: 171px;

        .block_description {
            padding-left: 43px;
            font-family: $regular;
            color: $black33;
            @include textOverflow(3, relative);
        }
    }

    .simple_block {
        flex: 0 0 50%;
        max-width: 50%;
        padding: 0 $colPadding;
        margin-bottom: 20px;

        .block_description {
            color: $black33;
            margin-bottom: 20px;
        }
    }

    .sidebar_list {
        @extend %standardList;

        li {
            margin-bottom: 20px;

            a {
                color: $black33;
                font-family: $bold;
                @extend %siteColorHover;
                position: relative;
                @include mediaFrom($size1440) {
                    font-size: 200%;
                    line-height: 26px;
                }
                @include mediaRange($size1200, $size1440) {
                    font-size: 180%;
                    line-height: 23px;
                }
                @include mediaRange($size991, $size1200) {
                    font-size: 160%;
                    line-height: 21px;
                }
                @include mediaTo($size991) {
                    font-size: 140%;
                    line-height: 19px;
                }
            }

            &.selected {
                a {
                    color: $siteColor;

                    &:before {
                        position: absolute;
                        content: "";
                        bottom: 0;
                        left: 0;
                        right: 0;
                        height: 1px;
                        background: $siteColor;
                        width: 100%;
                    }
                }
            }
        }
    }

    .num_block {
        line-height: 1.2em;
        font-family: $medium;
        color: $siteColor;
        -webkit-text-fill-color: transparent;
        -webkit-text-stroke: 1px;

        @include mediaFrom($size1200) {
            font-size: 500%;
        }
        @include mediaRange($size991, $size1200) {
            font-size: 400%;
        }
        @include mediaRange($size768, $size991) {
            font-size: 300%;
        }
        @include mediaRange($size480, $size768) {
            font-size: 200%;
        }
        @include mediaTo($size480) {
            font-size: 200%;
        }
    }

    .work_block {
        margin-top: 60px;

        .list_work {
            @extend %standardList;
            display: flex;
            flex-wrap: wrap;
            margin: 83px $rowMargin 0;
            counter-reset: css-counter;

            li {
                padding: 0 $containerPadding;
                flex: 0 0 33.333333%;
                max-width: 33.333333%;
                margin-bottom: 20px;
                counter-increment: css-counter 1;
                position: relative;

                &:before {
                    position: absolute;
                    bottom: 100%;
                    left: 0;
                    margin-bottom: -10px;
                    content: counter(css-counter);
                    -webkit-text-fill-color: transparent;
                    -webkit-text-stroke: 1px;
                    color: $grayCC;
                    @include mediaFrom($size1200) {
                        font-size: 140px;
                    }
                    @include mediaRange($size991, $size1200) {
                        font-size: 120px;
                    }
                    @include mediaRange($size768, $size991) {
                        font-size: 100px;
                    }
                    @include mediaRange($size480, $size768) {
                        font-size: 80px;
                    }
                    @include mediaRange($size360, $size480) {
                        font-size: 65px;
                    }
                    @include mediaTo($size360) {
                        font-size: 65px;
                    }
                }
            }

            .page_description {
                @include textOverflow(4, relative);
                color: $black33;
                padding-left: 25px;
            }

            .sub_title {
                color: $umber86;
                position: relative;
                display: flex;
                flex-direction: column-reverse;
                max-width: 340px;
                padding-left: 25px;

                &:before {
                    max-width: 54px;
                    display: block;
                    width: 100%;
                    content: "";
                    height: 2px;
                    margin: 20px 0;
                    background: $siteColor;
                }
            }

        }

        @include mediaTo($size991) {

            .list_work {
                margin: 40px $rowMargin 0;

                .sub_title {
                    max-width: 100%;
                }

                li {
                    flex: 0 0 50%;
                    max-width: 50%;
                    margin-bottom: 50px;

                    &:before {
                        left: 3px;
                    }
                }
            }
        }
        @include mediaTo($size480) {
            .list_work {
                li {
                    flex: 0 0 100%;
                    max-width: 100%;
                }

                .sub_title:before {
                    margin: 7px 0;
                }
            }
        }
    }

    @include mediaTo($size1200) {
        .benefits_block {
            margin-top: 80px;
        }
        .banner_inner {
            max-width: 100%;
            margin-top: 0;
        }
        .loan_block {
            padding: 30px 30px;
        }
    }
    @include mediaTo($size991) {
        .terms_page {
            .loantable tr {
                padding: 8px 0;

                td {
                    font-size: 120%;

                    &:last-child {
                        text-align: initial;
                    }
                }
            }
        }
        .loan_top {
            min-height: auto;
        }
        .inner_block {
            padding: 15px;
        }
        .benefits_block {
            margin-top: 20px;
        }
        .info_loan {
            > .page_row {
                margin: 0 $rowMargin;
            }
        }
        .work_block,
        .info_loan {
            margin-top: 20px;
        }
        .description_loan {
            margin: 20px 0 20px;
        }
        .simple_row {
            flex-direction: column-reverse;
        }
        .simple_block {
            flex: 0 0 100%;
            max-width: 100%;
        }
        .content_loan,
        .sidebar_loan {
            padding: 0 $colPadding;
        }
        .banner_inner {
            max-width: 100%;
        }

        .story_info {
            flex: 0 0 100%;
            max-width: 100%;

            .sub_title,
            .page_title,
            .sub_description {
                -webkit-line-clamp: unset;
            }
        }
        .image_block {
            display: none;
        }
        .simple_block .block_description {
            margin-bottom: 10px;
        }
        .content_loan .block_description {
            margin-bottom: 20px;
        }
    }
    @include mediaTo($size768) {
        .terms_page {
            .content_loan .block_description {
                margin-top: 20px;
            }
        }
        .loan_block {
            padding: 15px;
            // margin-top: 15px;
        }
        .loan_params li {
            flex: 0 0 50%;
            max-width: 50%;
        }
        .inner_list {
            margin-top: 15px;

            li {
                flex: 0 0 100%;
                max-width: 100%;
            }
        }
        .loan_img,
        .content_loan,
        .sidebar_loan {
            flex: 0 0 100%;
            max-width: 100%;
            border-left: none;
        }
        .sidebar_loan {
            overflow-x: auto;
        }
        .sidebar_list {
            display: flex;
            white-space: nowrap;

            li {
                padding: 7px;
                margin-bottom: 7px;
            }
        }
    }
    @include mediaTo($size600) {
        .terms_page {
            .loantable tr {
                flex-wrap: wrap;
                &:first-child{
                    counter-reset: my-sec-counter;
                }
                td {
                    flex: 0 0 100%;
                    max-width: 100%;
                    padding-bottom: 10px;

                    &:first-child:before{
                        counter-increment: my-sec-counter;
                        content: counter(my-sec-counter) ". ";
                    }
                }
            }
        }
    }

    @include mediaTo($size576) {
        .loan_params li {
            flex: 0 0 100%;
            max-width: 100%;
            margin-bottom: 15px;
        }
    }
}