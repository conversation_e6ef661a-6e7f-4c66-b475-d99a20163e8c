.mission_page {
    margin-top: 20px;

    .section_info {
        margin-top: 30px;
        justify-content: space-between;

        .sub_title {
            flex: 0 0 25%;
            max-width: 25%;
            padding: 0 $colPadding;
        }

        .page_description {
            flex: 0 0 65%;
            max-width: 65%;
            padding: 0 $colPadding;

            ol {
                margin: 0;
                padding: 0 $colPadding +3;
            }
        }
    }

    .params_block {
        display: flex;
        flex-wrap: wrap;
        margin: 0 $rowMargin 60px;

        .images_2 {
            padding: 0 $colPadding;
            flex: 0 0 66.6666666667%;
            max-width: 66.6666666667%;
            margin-bottom: 10px;

            img {
                display: block;
                width: 100%;
                height: auto;
                object-fit: cover;
                border-radius: 10px;
            }
        }

        .images_1 {
            padding: 0 $colPadding;
            flex: 0 0 33.3333333333%;
            max-width: 33.3333333333%;
            margin-bottom: 10px;

            img {
                display: block;
                width: 100%;
                height: auto;
                border-radius: 10px;
                object-fit: cover;
            }
        }
    }

    .office_params {
        @extend %standardList;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        margin: 60px 0 60px;
        width: 100%;

        li {
            padding: 0 $colPadding;
            flex: 0 0 25%;
            max-width: 25%;
            margin-bottom: 20px;
        }

        .sub_title {
            color: $black33;

            &:after {
                content: "";
                display: block;
                height: 2px;
                background: $siteColor;
                width: 100%;
                max-width: 80px;
                margin: 7px 0;
            }
        }
    }


    .num_block {
        line-height: 1.2em;
        font-family: $medium;
        color: $siteColor91;
        -webkit-text-fill-color: transparent;
        -webkit-text-stroke: 1px;

        @include mediaFrom($size1200) {
            font-size: 700%;
        }
        @include mediaRange($size991, $size1200) {
            font-size: 600%;
        }
        @include mediaRange($size768, $size991) {
            font-size: 500%;
        }
        @include mediaRange($size480, $size768) {
            font-size: 400%;
        }
        @include mediaRange($size360, $size480) {
            font-size: 400%;
        }
        @include mediaTo($size360) {
            font-size: 300%;
        }
    }

    @include mediaTo($size991) {
        .section_info {
            margin-top: 20px;
        }
        .params_block {
            margin: 0 $rowMargin 20px;
        }
        .office_params {
            margin: 20px 0 20px;
        }
    }
    @include mediaTo($size768) {
        .section_info {
            .page_description,
            .sub_title {
                flex: 0 0 50%;
                max-width: 50%;
            }
        }
        .office_params {
            li {
                flex: 0 0 50%;
                max-width: 50%;
            }
        }
    }
    @include mediaTo($size480) {
        .section_info {
            .page_description {
                margin-top: 15px;
            }

            .page_description,
            .sub_title {
                flex: 0 0 100%;
                max-width: 100%;
            }
        }
    }
}