.inside_page {
    margin-top: 20px;

    .product_images {
        display: flex;
        align-items: flex-start;
        flex: 0 0 100%;
        max-width: 100%;
        margin: 0 $rowMargin 0;

        .slick-arrow {
            position: absolute;
            z-index: 5;
            border: none;
            padding: 0;
            background: transparent;
            color: $white;
            font-size: 0;
            @extend %siteColorHover;

            &:before {
                display: block;
                @extend %iconElement;
                font-size: 2rem;
            }
        }

        .slick-prev {
            left: 25px;
            top: 50%;

            &:before {
                content: "\e902";
            }
        }

        .slick-next {
            right: 25px;
            top: 50%;

            &:before {
                content: "\e903";

            }
        }

        .slick-disabled {
            opacity: 0 !important;
            pointer-events: none;
            visibility: hidden;
            cursor: default;
        }

        @include mediaRange($size991, $size768) {
            flex-direction: column-reverse;
            justify-content: flex-end;
        }
        @include mediaTo($size991) {
            margin-bottom: 13px;
        }
        @include mediaTo($size768) {
            flex-direction: column-reverse;
            justify-content: flex-end;
        }
    }

    .small_images {
        width: 280px;
        min-width: 280px;
        padding: 0 $colPadding;
        position: relative;
        overflow: hidden;
        height: 513px;

        .slick-list {
            height: 513px !important;
        }

        .image_block {
            padding: 6px 0;

            a {
                display: block;
                border: 2px solid transparent;
                @extend %square;
                opacity: 1;

                &:before {
                    padding-bottom: 61%;
                }

                &.selected {
                    opacity: 0.6;
                }
            }
        }

        .slick-arrow {
            position: absolute;
            z-index: 5;
            border: none;
            padding: 0;
            background: transparent;
            color: $white;
            width: 100%;
            font-size: 0;
            left: 0;

            &:before {
                display: block;
                @extend %iconElement;
                font-size: 1.3rem;
            }
        }

        .slick-prev {
            left: 0;
            top: 10px;

            &:before {
                content: "\e901";
                @include transStyle(rotate(-180deg));
            }
        }

        .slick-next {
            right: 0;
            bottom: 10px;
            top: auto;
            left: 0;

            &:before {
                content: "\e901";

            }
        }

        @include mediaRange($size991, $size768) {
            width: 100%;
            height: auto;
            flex: none;
            margin-left: 0;
            padding: 0;
            margin-top: 20px;
            @include slider();
            .image_block {
                @include slider;
                padding: 10px;
                width: 33.3%;
                vertical-align: top;

                a {
                    height: 100px;
                }
            }
            .slick-prev {
                left: 0;
                width: 20px;
                top: 10px;
                height: 100px;

                &:before {
                    @include transStyle(rotate(0));
                }
            }
            .slick-next {
                width: 20px;
                right: 0;
                height: 100px;
                bottom: 10px;

                &:before {
                    @include transStyle(rotate(0));
                }
            }
        }
        @include mediaTo($size768) {
            .slick-list {
                height: auto !important;
            }
            width: 100%;
            height: auto;
            flex: none;
            margin-left: 0;
            padding: 0;
            margin-top: 20px;
            @include slider();

            .slick-prev {
                right: auto;
                bottom: 50%;
                left: 10px;
                width: max-content;
                margin-top: 20px;

                &:before {
                    //@include transStyle(rotate(180deg));
                    transform: unset;
                    color: $siteColor;
                    content: "\e902";
                }
            }
            .slick-next {
                right: 10px;
                bottom: 50%;
                left: auto;
                width: max-content;

                &:before {
                    // @include transStyle(rotate(-90deg));
                    transform: unset;
                    color: $siteColor;
                    content: "\e903";
                }
            }
        }
    }

    .big_images {
        flex: 1;
        width: 50%;
        position: relative;
        z-index: 5;
        padding: 0 $colPadding;
        @include slider();

        .image_block {
            @include slide(100%, top);
            display: inline-flex;
            height: 400px;
            align-items: center;
            justify-content: center;
            cursor: pointer;

            img {
                display: block;
                max-width: 100%;
                max-height: 100%;
            }
        }

        @include mediaRange($size991, $size768) {
            width: 100%;
            flex: none;
        }
        @include mediaTo($size768) {
            width: 100%;
            flex: none;
            .image_block {
                height: auto;
            }
        }
    }

    .up_date {
        font-size: 130%;
        line-height: 17px;
        color: $gray66;
    }

    .price_block {
        font-size: 200%;
        line-height: 27px;
        color: $black33;
        font-family: $bold;
    }

    .model_info {
        font-family: $bold;
    }

    .list_block {
        display: flex;
        justify-content: space-between;
        border-top: 1px solid $grayC4;
        padding-top: 20px;
        font-size: 160%;
        line-height: 21px;
        color: $black33;
    }

    .list_info_inner {
        @extend %standardList;
        display: flex;
        flex-wrap: wrap;
        margin: 20px -30px 0;

        li {
            flex: 0 0 50%;
            max-width: 50%;
            padding: 0 30px;
            margin-bottom: 15px;
        }
    }

    .info_block {
        > .page_row {
            margin-bottom: 10px;
        }
    }

    .call_block {
        font-size: 200%;
        line-height: 27px;
        color: $black33;
        font-family: $bold;

        a {
            color: $siteColor;
        }
    }

    .page_row {
        align-items: center;

        .up_date,
        .call_block {
            flex: 0 0 50%;
            max-width: 50%;
            padding: 0 $colPadding 0 30px;
        }

        .price_block,
        .page_title {
            flex: 0 0 50%;
            max-width: 50%;
            padding: 0 $colPadding;
        }
    }

    @include mediaTo($size991) {
        .list_info_inner {
            margin: 20px $rowMargin 0;

            li {
                padding: 0 $colPadding;
            }
        }
        .page_row {
            align-items: center;

            .up_date,
            .call_block {
                padding: 0 $colPadding;
            }
        }
        .list_block {
            font-size: 140%;
            line-height: 19px;
        }
        .call_block {
            font-size: 180%;
            line-height: 23px;
        }
    }
    @include mediaTo($size768) {
        .call_block {
            font-size: 160%;
            line-height: 21px;
        }
        .page_row {
            .call_block,
            .up_date,
            .page_title {
                flex: 0 0 100%;
                max-width: 100%;
                padding: 0 $colPadding;
            }
        }
        .list_info_inner li {
            flex: 0 0 100%;
            max-width: 100%;
        }
    }
}

