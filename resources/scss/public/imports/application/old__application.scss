@property --num {
    syntax: "<integer>";
    initial-value: 0;
    inherits: false;
}

@keyframes counter {
    from {
      --num: 59;
    }
    to {
      --num: 0;
    }
  }

.resent_form.waiting .promo_block {
    animation: counter 60s forwards alternate linear;
    counter-reset: num var(--num);
    &::after {
        position: absolute;
        right: 0;
        top: 1px;
        content: counter(num);
    }
}

.application_page {
    padding-top: 60px;

    .application_container {
        max-width: 820px;
        margin: 0 auto;
        padding: 0 $colPadding;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
    }

    .page_title {
        text-align: center;
    }

    .application_description {
        color: $black33;
        margin-top: 20px;
        text-align: center;
    }

    .block_description {
        color: $black33;
        + .field_block {
            margin-top: 30px;
        }
    }

    .field_unit {
        position: absolute;
        right: 10px;
        pointer-events: none;
        padding: inherit;
        top: 33px;
        font-size: 130%;
        line-height: 19px;
    }

    .list_application {
        @extend %standardList;
        margin-top: 40px;
        width: 100%;
        text-align: center;

        li {
            margin-bottom: 20px;

            a {
                background: $creamyF9;
                border-radius: 10px;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                min-height: 119px;
                padding: $colPadding;
                font-size: 160%;
                line-height: 22px;
                color: $black33;
                font-family: $bold;
                border: 1px solid inherit;
                @include animStyle(all 0.3s);

                &:hover {
                    border: 1px solid $siteColor91;
                    background: $white;
                    color: $siteColor91;
                    @include animStyle(all 0.3s);
                }

            }
        }
    }

    .inner_step {
        max-width: 620px;
        width: 100%;
        margin: 0 auto;
        position: relative;
    }

    .steps_block {
        background: $white;
        padding: 40px;
        width: 100%;
        margin-top: 40px;
    }

    .step_list {
        @extend %standardList;
        margin-bottom: 27px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        li {
            flex: 1;
            display: flex;
            align-items: center;
            position: relative;

            a {
                width: 50px;
                height: 50px;
                position: relative;
                background: $white;
                border: 2px solid $siteColor91;
                display: flex;
                align-items: center;
                justify-content: center;
                color: $siteColor91;
                margin: 0;
                padding: 0;
                border-radius: 50%;
                font-family: $bold;
                @include mediaFrom($size1440) {
                    font-size: 320%;
                    line-height: 43px;
                }
                @include mediaRange($size1200, $size1440) {
                    font-size: 300%;
                    line-height: 38px;
                }
                @include mediaRange($size991, $size1200) {
                    font-size: 250%;
                    line-height: 35px;
                }
                @include mediaRange($size768, $size991) {
                    font-size: 200%;
                    line-height: 29px;
                }
                @include mediaTo($size768) {
                    font-size: 180%;
                    line-height: 27px;
                }
            }

            &:before {
                content: "";
                position: absolute;
                top: 26px;
                height: 1px;
                background: $siteColor91;
                width: 100%;
            }

            &:last-child {
                flex: inherit;

                &:before {
                    display: none;
                }
            }

            &.active {
                a {
                    background: $siteColor;
                    color: $white;
                }

                ~ li {
                    a {
                        background: $grayDD;
                        border: 1px solid $grayDD;
                        color: $white;
                    }

                    &:before {
                        background: $grayDD;
                    }
                }

                &:before {
                    background: $grayDD;
                }
            }
        }
    }

    .form_fields {
        flex-direction: column;
        margin-bottom: 0;
        > .block_description:last-child,
        > .promo_block:last-child {
            margin-bottom: 30px;
        }
        .field_block {
            max-width: 320px;
            padding: 0 10px;
        }
        .block_description {
            padding: 0 10px;
            @include colSize(12);
        }
    }

    .fields_group {
        width: 100%;
        display: flex;
        flex-wrap: wrap;

    }

    .field_block {
        flex: inherit;
        width: 100%;
        max-width: 300px;
        margin-bottom: 30px;
        padding-left: 0;
        padding-right: 0;

        input,
        textarea {
            background: none;
            &[data-transform="uppercase"] {
                text-transform: uppercase;
            }
        }
        &.full_field {
            max-width: 100%;
            flex: 0 0 100%;
        }
        .attach_block {
            margin-top: 10px;

        }
        .attach_label {
            display: block;
            position: relative;
            width: 100%;
            max-width: 247px;
            &[hidden],
            input {
                @extend %maskedInput;
            }
            &.disable {
                pointer-events: none;
                .attach_btn {
                    color: $grayDD;
                }
            }
        }
        .attach_btn {
            @extend %textUnSelect;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            border: 1px solid;
            color: $umberC9;
            font-size: 130%;
            line-height: 22px;
            border-radius: 5px;
            font-family: $bold;
            height: 49px;
        }
        .attach_error {
            display: block;
            color: $error;
            margin-top: 2px;
        }

        .file_block {
            display: flex;
            align-items: center;
            margin-top: 10px;
            justify-content: space-between;
            max-width: 247px;
        }
        .file_name {
            font-size: 130%;
            line-height: 16px;
            display: block;
            flex: 1;
            width: 50%;
            white-space: nowrap;
            text-overflow: ellipsis;
            position: relative;
            overflow: hidden;
        }
        .attach_remove  {
            display: block;
            font-size: 100%;
            padding: 5px;
            @extend %textUnSelect;
            cursor: pointer;
            @include siteColorHover;
            margin-left: 15px;
            margin-right: -5px;
            &:before {
                display: block;
                font-weight: 900 !important;
            }
        }

        .date_input {
            background-image: url('images/svg/calendar.svg');
            background-size: auto 100%;
            background-position: top right;
            background-repeat: no-repeat;
            padding-right: 50px;
            @include mediaTo($size400) {
                background-image: none;
                padding-right: 10px;
            }
            @include mediaTo($size360) {
                &::-webkit-input-placeholder {
                    font-size: 85%;
                }
                &:-moz-placeholder {
                    font-size: 85%;
                }
                &::-moz-placeholder {
                    font-size: 85%;
                }
                &:-ms-input-placeholder {
                    font-size: 85%;
                }
            }
        }

        &.inactive {
            .placeholder,
            > input,
            .error_hint {
                pointer-events: none;
                opacity: 0.4;
            }
        }
    }

    .submission_type,
    .postal_address,
    .toggle_field {
        display: none;
    }

    .buttons_block {
        display: flex;
        margin: 0 -10px -20px;
        padding: 20px 0;
        background: $white;
        position: sticky;
        bottom: 0;

        button,
        a {
            background: $white;
            border: 1px solid $siteColor91;
            border-radius: 5px;
            padding: 13px;
            min-width: 247px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-family: $bold;
            font-size: 130%;
            line-height: 22px;
            color: $siteColor91;
            @include siteBgHover;

            &.btn_next {
                background: $siteColor91;
                color: $white;
                position: relative;
                @include opacityHover;
                &.loading {
                    pointer-events: none;
                    color: transparent !important;
                    @extend %textUnSelect;
                    &:after {
                        color: $white;
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        margin: -4px -8px;
                        border-radius: 50%;
                        display: block;
                        width: 8px;
                        height: 8px;
                        content: "";
                        animation: loadAnimation 1.6s infinite linear;
                    }
                }
            }

            &.disabled {
                background: $grayE5;
                pointer-events: none;
                border-color: $grayE5;
            }
        }
    }

    .promo_block {
        font-size: 160%;
        line-height: 22px;
        color: $black33;
        button,
        a {
            color: $siteColor;
            border: none;
            background: transparent;
            padding: 0;
            display: inline-block;
            vertical-align: top;
            text-decoration: underline;
            @include opacityHover;
        }

        + .field_block {
            margin-top: 30px;
        }
    }

    .form_fields > .promo_block {
        padding: 0 10px;

    }

    .info_form {
        .field_block {
            max-width: 100%;
            padding: 0 15px;
        }
        label {
            @include overHidden();
            vertical-align: top;
            margin: 0;

            input {
                @extend %maskedInput;
                max-width: 100%;

                &:checked {
                    + .radio_button {
                        color: $gray66 !important;
                        cursor: default;

                        &:before {
                            background: $white;
                            border-color: $siteColor;
                        }

                        &:after {
                            opacity: 1;
                        }

                        &.no_selected {
                            color: $black !important;

                            &:before {
                                background: none;
                            }

                            &:after {
                                opacity: 0;
                            }
                        }
                    }
                }
            }

            .radio_button {
                display: flex;
                position: relative;
                font-size: 140%;
                line-height: 16px;
                cursor: pointer;

                > span {
                    margin-left: 2px;
                    color: $grayC4;
                }

                &:before {
                    display: block;
                    content: "";
                    margin-right: 10px;
                    width: 20px;
                    min-width: 20px;
                    height: 20px;
                    @include animStyle(border-color 0.3s);
                    border: 1px solid $gray66;
                    border-radius: 50%;

                }

                &:after {
                    @include psevdoAbs();
                    width: 12px;
                    height: 12px;
                    left: 4px;
                    margin-top: 4px;
                    border-radius: 50%;
                    background: $siteColor;
                    opacity: 0;
                }
            }
        }
    }

    .checkbox_block {
        margin-top: 30px;
        position: relative;
        &:first-child {
            margin-top: 0;
        }
        .error_hint {
            position: absolute;
            left: 0;
            bottom: 100%;
            margin-bottom: -5px;
        }
        &.agree_check .checkbox_btn {
            display: block;
            padding-left: 30px;
            &:before {
                position: absolute;
                top: 0;
                left: 0;
            }
            a {
                display: block;
            }
        }
        > label {
            display: inline-block;
            vertical-align: top;
            font-weight: normal;
            @include animStyle(margin 0.3s);
            &.has-error {
                margin-top: 20px;
            }
            input {
                @extend %maskedInput;
                &:checked + .checkbox_btn {
                    &:before {
                        border-color: transparent;
                        background: $siteColor;
                        font-size: 1rem;
                    }
                }
            }

        }
        .checkbox_btn {
            display: flex;
            font-size: 140%;
            line-height: 20px;
            align-items: flex-start;
            @include siteColorHover;
            cursor: pointer;
            @extend %textUnSelect;
            &:before {
                min-width: 20px;
                height: 20px;
                margin-right: 10px;
                border: 1px solid $grayC4;
                @extend %iconElement;
                content: "\e905";
                border-radius: 3px;
                font-size: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                color: $white;
            }
        }

    }

    .radio_group {
        padding: 0 10px 20px;
        position: relative;
        margin-bottom: 30px;
        input[type="radio"] {
            @extend %maskedInput;
        }
        .radio_block label {
            display: inline-block;
            vertical-align: top;
            font-weight: normal;
        }
        .radio_button {
            display: flex;
            font-size: 140%;
            line-height: 20px;
            align-items: flex-start;
            @include siteColorHover;
            cursor: pointer;
            &:before {
                min-width: 20px;
                height: 20px;
                margin-right: 10px;
            }
        }
        > .radio_block {
            &:not(:first-child) {
                margin-top: 30px;
            }
            > label {
                @include animStyle(margin 0.3s);
                > .radio_button {

                    &:before {
                        display: block;
                        content: "";
                        border-radius: 50%;
                        box-shadow: 0 0 0 1px $grayC4;
                    }
                }
                input:checked + .radio_button {
                    pointer-events: none;
                    &:before {
                        background: $siteColor;
                        box-shadow: 0 0 0 1px $siteColor;
                        border: 4px solid $white;
                    }
                }
                &.has-error {
                    margin-top: 20px;
                }
            }
            > .error_hint {
                position: absolute;
                top: -5px;
                left: 10px;
                font-size: 120%;
            }
            > .sub_fields {
                padding-left: 40px;
                @include mediaTo($size576) {
                    padding-left: 30px;
                }
            }
        }
        .sub_fields {
            display: none;
            position: relative;
            .radio_block {
                padding-top: 20px;
                position: relative;
                > .error_hint {
                    position: absolute;
                    top: auto;
                    left: 0;
                    bottom: 100%;
                    margin-bottom: -35px;
                }
                > label {
                    @include animStyle(margin 0.3s);
                    &.has-error {
                        margin-top: 20px;
                    }
                }
                .radio_button:before {
                    border: 1px solid $grayC4;
                    @extend %iconElement;
                    content: "\e905";
                    border-radius: 3px;
                    font-size: 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: $white;
                }
                input:checked + .radio_button {
                    pointer-events: none;
                    &:before {
                        border-color: transparent;
                        background: $siteColor;
                        font-size: 1rem;
                    }
                }
            }
            .field_block {
                padding: 20px 0 0;
                margin-bottom: 0;
                &:not(.full_field) {
                    max-width: 300px;
                }
            }
        }
    }

    @include selectStyle($textColor, transparent, $grayC4, 4px, 130%, 16px, 15px 30px 15px 14px, 8px, 10px, 10px);
    .select2-container {
        width: 100% !important;
    }

    .has-error .select2-container--default .select2-selection--single {
        border-color: $error;
    }

    .daterangepicker {
        @include mediaFrom($size576) {
            white-space: nowrap;
            .drp-calendar {
                display: inline-block;
                vertical-align: top;
                float: none;
            }
        }
        @include mediaTo($size400) {
            left: auto !important;
            right: 0 !important;
            max-width: 260px;
        }
        .drp-buttons .btn {
            border: none;
            background: $siteColor;
            color: $white;
            padding: 8px 15px;
            @include opacityHover;
        }
        .drp-selected {
            display: none !important;
        }
        td.active,
        td.active {
            background: $siteColor;
        }
        td.in-range:not(.active) {
            background: rgba($siteColor, 0.1);
        }
    }

    .resent_form {
        position: absolute;
        bottom: 100px;
        left: 0;
        max-width: 100%;
        padding-right: 40px;
        ~ form .buttons_block {
            padding-top: 60px;
        }
        .response_error {
            position: absolute;
            left: 0;
            top: 100%;
            margin-top: 5px;
        }
        &.waiting{
            pointer-events: none;
        }
    }


    @include mediaTo($size768) {
        .promo_block {
            font-size: 140%;
            line-height: 20px;
        }
        padding-top: 20px;
        .list_application {
            margin-top: 20px;
        }
        .list_application li a {
            min-height: 70px;
            font-size: 140%;
            line-height: 22px;
        }
        .steps_block {
            padding: 25px;
        }
        .form_fields {
            margin: 20px -10px 15px;
        }
        .buttons_block {
            button,
            a {
                min-width: inherit;
                flex: 1;
            }
        }
    }
}

.footer .upload_text {
    display: none;
}

@include selectDrop($grayC4, 120%, 15px, 7px 10px, none, 6px, 5px, $creamyF2, $siteColor, $siteColor, $white, transparent, $siteColor);

.response_error {
    font-size: 160%;
    line-height: 21px;
    color: $error;
    font-family: $bold;
    padding-bottom: 10px;
    &:empty {
        display: none;
    }
    @include mediaTo($size991) {
        font-size: 140%;
        line-height: 19px;
    }
}

@keyframes loadAnimation {
    9% {
        box-shadow: none;
    }
    10% {
        box-shadow: -8px 0 0;
    }
    35% {
        box-shadow: -8px 0 0;
    }
    36% {
        box-shadow: -8px 0 0, 8px 0 0;
    }
    60% {
        box-shadow: -8px 0 0, 8px 0 0;
    }
    61% {
        box-shadow: -8px 0 0, 8px 0 0, 24px 0 0;
    }
    85% {
        box-shadow: -8px 0 0, 8px 0 0, 24px 0 0;
    }
    86% {
        box-shadow: none
    }
}

.result_block {
    display: flex;
    text-align: center;
    flex-direction: column;
    align-items: center;
    .buttons_block {
        margin-top: 40px;
        @include mediaTo($size768) {
            margin-top: 30px;
        }
    }
}

.inform_popup {
    .popup_container {
        max-width: 560px;
    }

    .popup_text {
        font-size: 140%;
        line-height: 24px;
        padding-top: 40px;
    }
    .popup_close:not(.icon_close) {
        position: relative;
        top: auto;
        right: auto;
        margin: 30px auto 0;
        border: none;
        border-radius: 5px;
        padding: 13px;
        min-width: 247px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: $bold;
        font-size: 130%;
        line-height: 22px;
        color: $siteColor91;
        background: $siteColor91;
        color: $white;
        @include opacityHover;

    }

    @include mediaTo($size480) {
        .popup_text {
            padding-top: 20px;
        }
    }
}

.loan_container {
    background: $white;
    border-radius: 10px;
    width: 100%;
    max-width: 820px;
    margin: 35px auto 145px;
    padding: 60px 100px;
    .list_application {
        margin-top: 0;
        li {
            &:last-child {
                margin: 0;
            }
            a {
                border: 1px solid $grayC4;
                background: transparent;
                @include animStyle(all 0.3s);
                &:hover {
                    border-color: $siteColor;
                    background: rgba($siteColor, 0.05);
                    color: $siteColor;
                }
            }
        }
    }
    .loan_head {
        padding: 30px 100px;
        margin: -60px -100px 30px;
        text-align: center;
        border-bottom: 1px solid $grayE5;
    }


    .fields_hint {
        font-size: 140%;
        line-height: 17px;
        padding-bottom: 30px;
        &:before {
            content: "*";
            color: $siteColor;
        }
    }

    .fields_group + hr {
        margin: 0 -100px 30px;
        opacity: 0.4;
    }

    .fields_group {
        width: auto;
        margin: 0 $rowMargin;
        .block_description {
            @include standardCol(12);
            margin-bottom: 30px;
             + .field_block {
                margin-top: 0;
            }
        }
        .fields_hint {
            @include standardCol(12);
        }
        .field_block {
           padding-left: $colPadding;
           padding-right: $colPadding;
        }
    }

    .details_file {
        display: inline-flex;
        vertical-align: top;
        margin-top: 20px;
        font-size: 140%;
        line-height: 24px;
        align-items: center;
        padding-left: 34px;
        color: inherit;
        text-decoration: underline;
        @include siteColorHover;
        background: url('images/pdf.svg') no-repeat;
        background-size: auto 24px;
        background-position: left top;
    }

    .borrower_switch {
        display: flex;
        border: 1px solid $umberC9;
        border-radius: 5px;
        margin-bottom: 40px;
        @include overHidden;
        input {
            @extend %maskedInput;
            &:checked ~ .radio_btn {
                pointer-events: none;
                color: $white;
                background: $umberC9;
            }
        }
        label {
            flex: 1;
        }
        .radio_btn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 10px;
            color: $umberC9;
            height: 50px;
            cursor: pointer;
            @extend %textUnSelect;
            @extend %all300;
            &:hover {
                background: rgba($siteColor, 0.05);
            }
        }
    }

    .radio_group {
        padding-bottom: 0;
        label {
            display: inline-block;
            margin-right: 30px;
            margin-top: 5px;
            &.has-error ~ .error_hint {
                max-height: 60px;
            }
        }
        input:checked ~ .radio_btn {
            pointer-events: none;
            &:before {
                border-color: $siteColor;
            }
            &:after {
                opacity: 1;
            }
        }
        .radio_btn {
            display: flex;
            align-items: center;
            position: relative;
            font-size: 140%;
            line-height: 16px;
            cursor: pointer;
            @include siteColorHover;
            > span {
                margin-left: 2px;
                color: $grayC4;
            }

            &:before {
                display: block;
                content: "";
                margin-right: 10px;
                width: 20px;
                min-width: 20px;
                height: 20px;
                @include animStyle(border-color 0.3s);
                border: 1px solid $gray66;
                border-radius: 50%;

            }

            &:after {
                @include psevdoAbs();
                width: 12px;
                height: 12px;
                left: 4px;
                top: 4px;
                border-radius: 50%;
                background: $siteColor;
                opacity: 0;
            }
        }
    }

    .review_table {
        border-spacing: 0;
        width: 100%;
        thead {
            text-align: center;
            background: $siteColor;
            color: #ffffff;
            th {
                padding: 10px 20px;
            }
        }
        tbody {
            font-size: 140%;
            line-height: 20px;
        }
        tfoot {
            height: 40px;
            display: block;
        }
        .info_type {
            border: 1px solid $grayC4;
            padding: 10px 20px;
            border-top: none;
            width: 50%;
            color: $gray66;
        }
        .info_inner {
            border: 1px solid $grayC4;
            padding: 10px 20px;
            border-top: none;
            border-left: none;
            width: 50%;
        }
        .block_description {
            color:  inherit;
        }
    }

    .singature_table {
        border-spacing: 0;
        width: 100%;
        font-size: 130%;
        line-height: 18px;
        thead {
            display: none;
        }
        .apply_col {
            padding: 0 20px 20px 0;
            width: 90px;
        }
        .signature_col {
            padding: 0 0 20px;
        }
        div.signature {
            border-bottom: 1px dashed $gray99;
        }
        .name_col {
            padding: 0 0 20px 20px;
            width: 50%;
        }
    }

    @include mediaTo($size991) {
        max-width: 720px;
        padding: 40px 16px;
        margin-top: 20px;
        margin-bottom: 0;
        .fields_group + hr {
            margin: 0 -16px 24px;
        }
        .loan_head {
            padding: 20px 16px 0;
            margin: -40px -16px 24px;
        }
        .review_table {
            tbody {
                font-size: 130%;
                line-height: 18px;
            }
            .info_type,
            .info_inner {
                padding: 8px 10px;
            }
            tfoot {
                height: 30px;
            }
        }
        .singature_table {
            font-size: 120%;
            line-height: 16px;
            .apply_col {
                padding-right: 10px;
                width: 70px;
            }
            .name_col {
                padding-left: 10px;
            }
        }

    }

    @include mediaFrom($size640) {
        .field_block {
            @include colSize(6);
        }
    }
    @include mediaTo($size640) {
        .field_block {
            @include colSize(12);
        }
    }
}
