.slider_main_block {
    position: relative;
}

.main_slider {
    @include slider;
    margin-bottom: 40px;

    .slide_block {
        @include slide(100%, top);
        height: 50vw;
        max-height: 800px;
        min-height: 200px;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        padding: 0 40px;
        position: relative;

        .page_container {
            display: flex;
            height: 100%;
            align-items: center;
            justify-content: flex-start;
            position: relative;
            z-index: 3;
        }

        &:before {
            @extend %coverLayer;
            content: "";
            background: $black;
            opacity: 0.2;
        }
    }

    .slick-arrow {
        position: absolute;
        top: 50%;
        @include transStyle(translate3d(0, -50%, 0));
        border: none;
        background: transparent;
        color: $white;
        @include siteColorHover;
        z-index: 2;

        &:before {
            display: block;
            @extend %iconElement;
        }
    }

    .slick-prev {
        left: $containerPadding;

        &:before {
            content: "\e902";
        }
    }

    .slick-next {
        right: $containerPadding;

        &:before {
            content: "\e903";
        }
    }

    .slick-dots {
        @extend %standardList;
        position: absolute;
        bottom: 50%;
        left: 0;
        right: 0;
        margin: 0 auto -130px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: 100%;
        max-width: $containerWidth + 2*$containerPadding;
        padding: 0 $containerPadding;
        @include mediaTo($size991) {
            bottom: 0;
        }

        li {
            margin: 0;
            padding: 0 4px;
            width: auto;
            height: auto;

            &.slick-active button {
                pointer-events: none;
                background: $white;
                border-color: $white;
            }
        }

        button {
            border: 1px solid $white;
            background: transparent;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            padding: 0;
            @extend %all300;

            &:hover {
                opacity: 0.7;;
            }
        }
    }

    .slide_content {
        max-width: 280px;
    }

    .slide_title {
        color: $white;
        margin: 0 0 20px;
        padding: 0;
        font-family: $bold;
        @include textOverflow(3, relative);
        @include mediaFrom($size1440) {
            font-size: 240%;
            line-height: 32px;
        }
        @include mediaRange($size1200, $size1440) {
            font-size: 220%;
            line-height: 30px;
        }
        @include mediaRange($size991, $size1200) {
            font-size: 200%;
            line-height: 29px;
        }
        @include mediaRange($size768, $size991) {
            font-size: 180%;
            line-height: 27px;
        }
        @include mediaTo($size768) {
            font-size: 160%;
            line-height: 25px;
        }
    }

    .slide_description {
        font-size: 150%;
        line-height: 21px;
        color: $white;
        @include textOverflow(3, relative);
    }

    .slide_btn {
        display: inline-block;
        vertical-align: top;
        color: $white;
        background: $gray99;
        text-align: center;
        min-width: 200px;
        max-width: 200px;
        @include siteLightRHover;
        padding: 17px $colPadding;
        margin-top: 10px;
    }

    @include mediaFrom($size1200) {
        .slick-arrow:before {
            font-size: 3.4rem;
        }
    }
    @include mediaRange($size991, $size1200) {
        .slick-arrow:before {
            font-size: 2.8rem;
        }
    }
    @include mediaRange($size768, $size991) {
        .slide_content {
            max-width: 440px;
        }
        .slick-arrow {
            padding: 5px;

            &:before {
                font-size: 2.4rem;
            }
        }
    }
    @include mediaTo($size991) {
        margin-bottom: 20px;
        .slick-arrow {
            padding: 5px;
        }
        .slick-prev {
            margin-left: -5px;
        }
        .slick-next {
            margin-right: -5px;
        }
        .slide_btn {
            padding: 13px 15px;
            min-width: 160px;
        }
    }
    @include mediaTo($size768) {
        .slide_content {
            margin: 0 auto;
            text-align: center;
            max-width: 400px;
            padding-bottom: 20px;
        }

        .slick-arrow {
            &:before {
                font-size: 2rem;
            }
        }
        .slide_block .page_container {
            align-items: flex-end;
        }
        .slide_btn {
            margin-top: 25px;
            padding: 11px 15px;
            min-width: 140px;
        }
    }
}