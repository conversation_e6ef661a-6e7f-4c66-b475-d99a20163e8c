.daterangepicker .drp-buttons .btn {
    border: none;
    background: $siteColor;
    color: $white;
    padding: 8px 15px;
    @include opacityHover;
}

.daterangepicker .drp-selected {
    display: none !important;
}

.daterangepicker td.active, .daterangepicker td.active {
    background: $siteColor;
}
.yearselect,
.monthselect{
    font-size: 130%;
    line-height: 22px;
    color: $black33;
    height: 49px;
    background: $grayE5;
    border: 1px solid $grayC4;
    border-radius: 5px;
    padding: 5px 20px;
    background: none;
}

.career_page {
    margin-top: 20px;

    .field_career {
        flex: 0 0 33.333333%;
        max-width: 33.333333%;

        .field_block {
            flex: 0 0 100%;
            max-width: 100%;
        }
    }

    .page_row {
        justify-content: space-between;
        margin-top: 30px;
    }


    #file-upload {
        display: none;
    }

    .file_upload {
        cursor: pointer;
        position: relative;
        overflow: hidden;
        white-space: nowrap;
        display: inline-block;
        text-overflow: ellipsis;
        max-width: 246px;
        font-size: 130%;
        line-height: 23px;
        color: $umberC9;
        border: 1px solid $umberC9;
        border-radius: 5px;
        background: $grayE5;
        width: 100%;
        text-transform: uppercase;
        font-family: $bold;
        padding: 12px 20px;
        text-align: center;
        @extend %siteBgHover;

        &:before {
            margin-right: 10px;
            position: absolute;
            right: 0;
            top: 50%;
            margin-top: -8px;
        }
    }

    .left_block {
        flex: 0 0 33.333333%;
        max-width: 33.333333%;
        padding: 0 $colPadding;
    }

    .right_block {
        flex: 0 0 66.666666%;
        max-width: 66.666666%;
        padding: 0 $colPadding;
    }

    .fields_section {
        margin-bottom: 30px;
    }

    .form_fields {
        margin: 30px $rowMargin 10px;
    }

    .block_description {
        margin-bottom: 30px;
    }

    .section_subtitle {
        font-size: 160%;
        line-height: 21px;
        color: $black33;
        font-family: $bold;
        margin-bottom: 20px;
    }

    .full_fields {
        .field_block {
            flex: 0 0 34%;
            max-width: 34%;

            input {
                max-width: 247px;
            }
        }
    }

    .info_form {
        margin: 0 $rowMargin 30px;

        .field_block {
            flex: 0 0 16%;
            max-width: 16%;
        }
    }

    .table_career {
        .inner_table {
            overflow-x: auto;
            white-space: nowrap;
            @include customScroll(auto, 3px, auto, 5px, 5px, $creamyF2, $gray66);
        }

        .btn_add {
            margin: 30px 0 60px;
            width: max-content;
        }

        table {
            border-collapse: collapse;
            width: 100%;
        }

        th {
            border: none;
            text-align: left;
            padding: 10px 0;
            font-size: 130%;
            line-height: 22px;
            font-family: $bold;
            color: $black33;
        }

        td {
            border: none;
            text-align: left;
            padding: 10px 0;
            font-size: 130%;
            line-height: 22px;
            color: $black33;
            min-width: 179px;
            max-width: 179px;
            width: 179px;

            input {
                height: 23px;
                border: none;
                padding: 0;
                font-size: 13px;
                line-height: 22px;
            }

            &:first-child {
                min-width: 230px;
                max-width: 230px;
                width: 230px;
            }
        }
    }

    label {
        @include overHidden();
        vertical-align: top;
        margin: 0;

        input {
            @extend %maskedInput;

            &:checked {
                + .radio_button {
                    color: $gray66 !important;
                    cursor: default;

                    &:before {
                        background: $white;
                        border-color: $siteColor;
                    }

                    &:after {
                        opacity: 1;
                    }

                    &.no_selected {
                        color: $black !important;

                        &:before {
                            background: none;
                        }

                        &:after {
                            opacity: 0;
                        }
                    }
                }
            }
        }

        .radio_button {
            display: flex;
            position: relative;
            align-items: center;
            font-size: 120%;
            line-height: 16px;
            cursor: pointer;

            > span {
                margin-left: 2px;
                color: $grayC4;
            }

            &:before {
                display: block;
                content: "";
                margin-right: 10px;
                width: 14px;
                min-width: 14px;
                height: 14px;
                @include animStyle(border-color 0.3s);
                border: 1px solid $gray66;
                border-radius: 50%;

            }

            &:after {
                @include psevdoAbs();
                width: 8px;
                height: 8px;
                top: 50%;
                left: 3px;
                margin-top: -4px;
                border-radius: 50%;
                background: $siteColor;
                opacity: 0;
            }
        }
    }

    .btn_add {
        font-size: 130%;
        line-height: 22px;
        color: $umberC9;
        border: 1px solid $umberC9;
        border-radius: 5px;
        background: $grayE5;
        margin: 0 0 20px;
        width: max-content;
        text-transform: uppercase;
        font-family: $bold;
        min-width: 247px;
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 49px;
        padding: 7px;
        cursor: pointer;
        margin-top: 18px;
        @extend %siteBgHover;
    }

    @include mediaTo($size991) {
        .right_block,
        .left_block {
            flex: 0 0 100%;
            max-width: 100%;
        }
        .table_career td {
            min-width: 100px;
            max-width: 100px;
            width: 100px;

            &:first-child {
                min-width: 130px;
                max-width: 130px;
                width: 130px;
            }
        }
    }
    @include mediaTo($size576) {
        .full_fields .field_block input {
            max-width: 100%;
        }
        .validate_btn,
        .file_upload,
        .btn_add{
            width: 100%;
            margin: 0;
            max-width: 100%;
        }
        .field_career{
            flex: 0 0 100%;
            max-width: 100%;
        }
        .form_fields {
            .field_block {
                flex: 0 0 100%;
                max-width: 100%;
            }
        }
    }
}