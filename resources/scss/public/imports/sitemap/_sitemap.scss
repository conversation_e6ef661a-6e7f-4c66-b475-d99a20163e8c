.sitemap_page {
    margin-top: 20px;

    .sitemap_list {
        @include customList(40px 0 0, 0, none);
        border-top: 1px solid $grayE5;

        > li {
            margin-bottom: 20px;

            &.opened {
                .question_block {
                    &:after {
                        @include animStyle(all 0.3s);
                        @extend %horizontalRotate;
                    }
                }
            }

            > a {
                color: $siteColor;
                padding: 15px;
                background: $white;
                border: none;
                display: flex;
                text-align: left;
                align-items: center;
                width: 100%;
                @include opacityHover;
                @include mediaFrom($size1440) {
                    font-size: 200%;
                    line-height: 26px;
                }
                @include mediaRange($size1200, $size1440) {
                    font-size: 180%;
                    line-height: 23px;
                }
                @include mediaRange($size991, $size1200) {
                    font-size: 160%;
                    line-height: 21px;
                }
                @include mediaTo($size991) {
                    font-size: 140%;
                    line-height: 19px;
                }
            }

            .question_block {
                &:after {
                    @include animStyle(all 0.3s);
                    display: block;
                    @extend %iconElement;
                    content: "\e901";
                    font-size: 1rem;
                    padding-left: 20px;
                    margin-left: auto;
                    color: $black33;
                }
            }
        }
    }

    .inner_info {
        margin-top: 20px;
        display: flex;
        flex-direction: column;
    }

    .list_answer {
        @extend %standardList;
        margin: 0 -10px;

        > li {
            border-bottom: 1px solid $grayC4;
            padding: 20px 0;

            &.opened {
                .bnt_answer {
                    &:after {
                        @include animStyle(all 0.3s);
                        @extend %horizontalRotate;
                    }
                }
            }

            .bnt_answer {
                &:after {
                    @include animStyle(all 0.3s);
                    display: block;
                    @extend %iconElement;
                    content: "\e901";
                    font-size: 1rem;
                    padding-left: 20px;
                    margin-left: auto;
                    color: $black33;
                }
            }

            > a {
                color: $black33;
                border: none;
                display: flex;
                text-align: left;
                align-items: center;
                width: 100%;
                @include siteColorHover;
                font-size: 130%;
                line-height: 17px;
                font-family: $bold;
            }
        }


        .list_inner {
            @extend %standardList;
            width: 100%;

            li {
                border-bottom: 1px solid $grayC4;
                padding: 20px 0;

                &:last-child {
                    border-bottom: none;
                    padding: 20px 0 0;
                }

                &:first-child {
                    border-top: 1px solid $grayC4;
                }

                a {
                    font-size: 130%;
                    line-height: 17px;
                    color: $black33;
                    display: block;
                    @extend %siteColorHover;
                }
            }
        }
    }

    .inner_answer {
        display: none;
    }

    .answer_block {
        display: none;

        a {
            color: $siteColor;
        }
    }

    .answer_inner {
        display: flex;
        flex-wrap: wrap;
        padding-top: 20px;

        .sub_title_bold {
            margin-bottom: 20px;
            color: $black33;
            width: max-content;
            @extend %siteColorHover;
        }

        .info_block {
            flex: 1;
            width: 100%;
            padding: 0 15px;
            display: flex;
            flex-direction: column;
        }
    }
}