@font-face {
    font-family: 'SegoeUI';
    src: url('../fonts/SegoeUIRegular/SegoeUIRegular.ttf') format("truetype");
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'signature';
    src: url('../fonts/handwriting/HovhannesTumanianU.ttf') format('truetype');
    src: url('../fonts/handwriting/HovhannesTumanianU.eot?#iefix') format('embedded-opentype'),
        url('../fonts/handwriting/HovhannesTumanianU.woff2') format('woff2'),
        url('../fonts/handwriting/HovhannesTumanianU.woff') format('woff'),
        url('../fonts/handwriting/HovhannesTumanianU.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display:swap;
}

body form {
    position: sticky;
    bottom: 0;
}

body {
    max-width: 1284px;
    width: 100%;
    margin: 0 auto;
    border: none;
    padding: 40px;
    font-family: 'SegoeUI', Arial,  Helvetica, sans-serif;
    box-sizing: border-box;
}
.header {
    white-space: nowrap;
    display: flex;
    align-items: flex-end;
}
.header img {
    display: inline-block;
    vertical-align: bottom;
    width: 140px;
    height: auto;
    margin-right: 20px;
}
.page_title {
    font-weight: normal;
    white-space: normal;
    margin: 0;
    font-size: 28px;
    line-height: 24px;
    text-align: center;
    flex:1;
    box-sizing: border-box;
}
.content {
    padding-top: 100px;
    min-height: auto !important;
}
.content .top {
    text-align: right;
    font-size: 28px;
    line-height: 40px;
}
.middle {
    padding-top: 100px;
}
.app_title {
    text-align: center;
    font-size: 40px;
    line-height: 50px;
    color: #000000;
    margin: 0;
}
.app_text {
    font-size: 30px;
    line-height: 45px;
    text-indent: 80px;
    padding-top: 100px;
    text-align: justify;
}
.app_text .app_text{
    padding-top: 0;
}
.app_text p {
    margin: 0
}
.footer {
    width: 100%;
    border-spacing: 0;
    margin-top: 100px;
    font-size: 28px;
    line-height: 40px;
}
.footer .apply_col {
    width: 20%;
    padding-left: 80px;
}
.footer .signature,
.footer .name_surname {
    border-bottom: 1px dashed;
    text-align: center;
    margin: 0 auto;
    min-height: 41px;
    width: 320px;
}
.footer .signature_col {
    width: 50%;
    text-align: center;
}
.footer .name_col {
    width: 30%;
    text-align: center;
}
.footer .signature {
    font-family: 'signature';
}
.buttons_block button.btn_next.loading,
.buttons_block a.btn_next.loading {
    pointer-events: none;
    color: transparent !important;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    position: relative;
}
.buttons_block button.btn_next.loading:after ,
.buttons_block a.btn_next.loading:after {
    color: #ffffff;
    position: absolute;
    top: 50%;
    left: 50%;
    margin: -4px -8px;
    border-radius: 50%;
    display: block;
    width: 8px;
    height: 8px;
    content: "";
    animation: loadAnimation 1.6s infinite linear;
}

@keyframes loadAnimation {
    9% {
        box-shadow: none;
    }
    10% {
        box-shadow: -8px 0 0;
    }
    35% {
        box-shadow: -8px 0 0;
    }
    36% {
        box-shadow: -8px 0 0, 8px 0 0;
    }
    60% {
        box-shadow: -8px 0 0, 8px 0 0;
    }
    61% {
        box-shadow: -8px 0 0, 8px 0 0, 24px 0 0;
    }
    85% {
        box-shadow: -8px 0 0, 8px 0 0, 24px 0 0;
    }
    86% {
        box-shadow: none
    }
}
@media (max-width: 1024px) {
    .app_title{
        font-size: 36px;
        line-height: 1.25em
    }
    .header img{
        width: 120x;
    }
    body{
        padding: 40px 20px
    }
    .content,
    .middle{
        padding-top: 70px;
    }
    .content .top,
    .page_title{
        font-size: 24px;
        line-height: 1.5em;
    }
    .app_text{
        font-size: 26px;
        line-height: 1.5em;
        padding-top: 50px;
        text-indent: 40px;
    }
    .footer{
        font-size: 24px;
        line-height: 1.5em;
        margin-top: 70px
    }
    .footer .apply_col{
        padding-left: 0;
    }
    .footer .name_col{
        text-align: right;
    }
    .footer .signature, 
    .footer .name_surname{
        margin: 0 0 0 auto;
        text-align: right;
        width: auto;
    }
}
@media (max-width: 767px) {
    .app_title{
        font-size: 32px
    }
    .content,
    .middle{
        padding-top: 50px;
    }
    .content .top,
    .page_title{
        font-size: 20px;
    }
    .app_text{
        font-size: 22px;
        padding-top: 30px;
        text-indent: 20px;
    }
    .footer{
        font-size: 20px;
        margin-top: 50px;
    }
}
@media (max-width: 575px) {
    
    .buttons_block a,
    .buttons_block button{
        min-width: auto !important;
        width: 150px !important;
        font-size: 110% !important;
        line-height: 1.25em !important;
        padding: 8px 0 !important;
    }
    .header img{
        width: 100px;
    }
    .app_title{
        font-size: 28px
    }
    .content,
    .middle{
        padding-top: 50px;
    }
    .content .top,
    .page_title{
        font-size: 18px;
    }
    .app_text{
        font-size: 20px;
    }
    .footer{
        font-size: 18px;
    }
}
@media (max-width: 479px) {
    .header img{
        width: 80px;
    }
}