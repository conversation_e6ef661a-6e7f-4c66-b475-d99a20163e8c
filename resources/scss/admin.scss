@import 'admin/variables';
@import 'admin/bootstrap';
@import 'admin/buttons';
@import 'navbar';
@import 'auth';
@import 'alertify';
@import 'admin/sortable-tree';
@import 'admin/dropzone';
@import 'admin/history';
@import 'admin/selectize/selectize.bootstrap5';
@import 'admin/sidebar';
@import 'admin/twilight';
@import 'admin/sortable';
@import 'admin/filemanager';
@import 'admin/permissions';
@import 'admin/modal';
@import 'admin/header';
@import 'admin/content';
@import 'admin/item-list-table';
@import 'admin/item-list-pagination';

html {
    height: 100%;
}

body {
    min-height: 100%;
    overflow-x: hidden;
}

.container-menulinks {
    position: static;
}

.main {
    @include media-breakpoint-up(md) {
        min-height: 0;
        margin-left: 260px;
    }
}

h1 {
    margin-top: 0;
    font-weight: normal;
    a:hover {
        text-decoration: none;
    }
}

.btn-status {
    @extend .btn;
    display: block;
    width: 1.5rem;
    height: 1.5rem;
    background: $primary no-repeat center;
    transition: none;
    mask-repeat: no-repeat;
    flex-shrink: 0;
    &-on {
        background-color: $primary;
        mask-image: url('data:image/svg+xml,<svg width="1.5em" height="1.5em" viewBox="0 0 16 16" class="bi bi-toggle-on" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M5 3a5 5 0 0 0 0 10h6a5 5 0 0 0 0-10H5zm6 9a4 4 0 1 0 0-8 4 4 0 0 0 0 8z"/></svg>');
        &:hover {
            background-color: $primary;
        }
    }
    &-off {
        background-color: $gray-400;
        mask-image: url('data:image/svg+xml,<svg width="1.5em" height="1.5em" viewBox="0 0 16 16" class="bi bi-toggle-off" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M11 4a4 4 0 0 1 0 8H8a4.992 4.992 0 0 0 2-4 4.992 4.992 0 0 0-2-4h3zm-6 8a4 4 0 1 1 0-8 4 4 0 0 1 0 8zM0 8a5 5 0 0 0 5 5h6a5 5 0 0 0 0-10H5a5 5 0 0 0-5 5z"/></svg>');
        &:hover {
            background-color: $gray-400;
        }
    }
}

.btn-impersonate {
    @extend .btn;
    display: block;
    width: 1.5rem;
    height: 1.5rem;
    transition: none;
    mask-repeat: no-repeat;
    mask-position: center;
    flex-shrink: 0;
    background-color: $gray-500;
    transition: $transition-base;
    mask-image: url('data:image/svg+xml,<svg height="16" viewBox="0 0 16 16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="m6.76923077 14.7692308.92307692-4.3076923-.92307692-1.23076927-1.23076923-.61538461zm2.46153846 0 1.23076927-6.15384618-1.23076927.61538461-.92307692 1.23076927zm1.53846157-9.71153849c-.0128205-.02564103-.0256411-.0448718-.0384616-.05769231-.0641025-.05128205-.3717948-.07692308-.92307689-.07692308-.44871795 0-.98397436.06089744-1.60576923.18269231-.0448718.01282051-.11217949.01923077-.20192308.01923077s-.15705128-.00641026-.20192308-.01923077c-.62179487-.12179487-1.15705128-.18269231-1.60576923-.18269231-.55128205 0-.85897436.02564103-.92307692.07692308-.01282051.01282051-.02564103.03205128-.03846154.05769231.01282051.11538461.02564103.20192307.03846154.25961538.01282051.01923077.03685897.0400641.07211538.0625s.05929488.05608975.07211539.10096154c.01282051.02564103.03685897.09134615.07211538.19711539.03525641.10576923.05769231.17147435.0673077.19711538.00961538.02564103.03365384.08012821.07211538.16346154s.06570513.13782051.08173077.16346154c.01602564.02564102.04487179.07051282.08653846.13461538.04166667.06410257.08012821.1073718.11538462.12980769.03525641.0224359.0801282.05288462.13461538.09134616s.11057692.06410256.16826923.07692307c.05769231.01282052.12339744.02564103.19711539.03846154.07371794.01282052.15224359.01923077.23557692.01923077.23076923 0 .41987179-.0400641.56730769-.12019231.1474359-.0801282.25160257-.17628205.3125-.28846153.06089744-.11217949.1073718-.22275641.13942308-.33173077s.06891025-.20352564.11057692-.28365385c.04166667-.0801282.09775641-.12019231.16826923-.12019231h.11538462c.07051282 0 .12660256.04006411.16826923.12019231.04166667.08012821.07852564.17467949.11057692.28365385s.07852564.21955128.13942308.33173077c.06089743.11217948.1650641.20833333.3125.28846153.1474359.08012821.33653846.12019231.56730769.12019231.08333333 0 .16185898-.00641025.23557692-.01923077.07371795-.01282051.13942308-.02564102.19711539-.03846154.05769231-.01282051.11378205-.03846153.16826923-.07692307.05448713-.03846154.09935893-.06891026.13461543-.09134616.0352564-.02243589.0737179-.06570512.1153846-.12980769.0416666-.06410256.0705128-.10897436.0865384-.13461538.0160257-.02564103.0432693-.08012821.0817308-.16346154s.0625-.13782051.0721154-.16346154.0320513-.09134615.0673077-.19711538c.0352564-.10576924.0592948-.17147436.0721154-.19711539.0128205-.04487179.0368589-.07852564.0721153-.10096154.0352565-.0224359.0592949-.04326923.0721154-.0625.0128205-.05769231.0256411-.14423077.0384616-.25961538zm4 8.45192309c0 .775641-.2339744 1.3846154-.7019231 1.8269231s-1.0897436.6634615-1.8653846.6634615h-8.40384618c-.77564102 0-1.39743589-.2211538-1.86538461-.6634615s-.70192308-1.0512821-.70192308-1.8269231c0-.3910257.01442308-.7692308.04326923-1.1346154.02884616-.3653846.08974359-.7676282.18269231-1.2067308.09294872-.4391025.21314102-.8349359.36057692-1.18749997.1474359-.3525641.35096154-.68429487.61057693-.99519231.25961538-.31089743.55929487-.54967948.89903846-.71634615l-.86538462-2.11538462h2.05769231c-.14102564-.41025641-.21153846-.82051282-.21153846-1.23076923 0-.07692307.00641025-.17948718.01923077-.3076923-1.24358975-.25641026-1.86538462-.56410257-1.86538462-.92307693 0-.36538461.67307692-.68269231 2.01923077-.95192307.10897436-.3974359.27403846-.82692308.49519231-1.28846154.22115384-.46153846.44711538-.82692308.67788461-1.09615385.20512821-.23717949.44871795-.35576923.73076923-.35576923.1923077 0 .46153847.09935897.80769231.29807692.34615385.19871795.61538462.29807693.80769231.29807693s.46153846-.09935898.80769231-.29807693c.34615384-.19871795.61538461-.29807692.80769231-.29807692.28205128 0 .52564098.11858974.73076918.35576923.2307693.26923077.4567308.63461539.6778847 1.09615385.2211538.46153846.3862179.89102564.4951923 1.28846154 1.3461538.26923076 2.0192307.58653846 2.0192307.95192307 0 .35897436-.6217948.66666667-1.8653846.92307693.0448718.51923076-.0192307 1.03205128-.1923077 1.53846153h2.0576923l-.7884615 2.16346154c.4038462.21153846.7483974.52083334 1.0336538.92788462.2852565.40705128.4951924.86698719.6298077 1.37980769.1346154.5128205.2275641.9983974.2788462 1.4567308.051282.4583333.0769231.9342948.0769231 1.4278846z"/></svg>');
    &:hover {
        background-color: $gray-600;
    }
}

.container-system-locales {
    max-height: 150px;
    overflow: auto;
}

.delete-attachment {
    cursor: pointer;
}

.checkbox.form-group-translation {
    margin-bottom: 20px;
    & + .checkbox.form-group-translation {
        margin-top: 10px;
    }
}

.noscroll {
    overflow: hidden;
}
