function initDateRangePicker($dateInput) {
    var $parrent = $dateInput.parent();
    var $dateFormat = $dateInput.data('format') ? $dateInput.data('format') : 'DDDD.MM.YY';
    var $dateLg = $dateInput.data('lg') ? $dateInput.data('lg') : 'en';
    var $minDate = $dateInput.data('mindate') ? eval($dateInput.data('mindate')) : false;
    var $maxDate = $dateInput.data('maxdate') ? eval($dateInput.data('maxdate')) : false;
	var $singleDate = $dateInput.data('single') ? $dateInput.data('single') == true : false;
    // var dropdowns = $dateInput.data('dropdowns') ? true : false;
    var daysList = {
        "en": ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"],
        "am": ["Կի", "Եկ", "Եք", "Չո", "Հի", "Ու", "Շա"],
        "ru": ["Вс", "Пн", "Вт", "Ср", "Чт", "Пт", "Сб"]
    };

    var monthsList = {
        "en": ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"],
        "am": ["Հունվար", "Փետրվար", "Մարտ", "Ապրիլ", "Մայիս", "Հունիս", "Հուլիս", "Օգոստոս", "Սեպտեմբեր", "Հոկտեմբեր", "Նոյեմբեր", "Դեկտեմբեր"],
        "ru": ["Январь", "Февраль", "Март", "Апрель", "Май", "Июнь", "Июль", "Август", "Сентябрь", "Октябрь", "Ноябрь", "Декабрь"]
    };

    $dateInput.daterangepicker({
        maxDate: $maxDate,
        minDate: $minDate,
        parentEl: $parrent,
        autoUpdateInput: false,
        autoApply: true,
		singleDatePicker: $singleDate,
        locale: {
            daysOfWeek: daysList[$dateLg],
            monthNames: monthsList[$dateLg],
        },

    }, function (chosen_date, end) {
		$singleDate ? $dateInput.val(chosen_date.format($dateFormat)) : $dateInput.val(chosen_date.format($dateFormat) + ' - ' + end.format($dateFormat));
    });

}


$(document).ready(function(){
    

	$('select').on('change',function(){
		$(this).find('option').each(function(){
			if($(this).data('sub')) {
				let $subElement = $('.'+$(this).data('sub'));
				if(!$(this).is(':selected')) {
					$subElement.hide().find('input, textarea, select').attr('disabled','disabled');
				} else {
					$subElement.show().find('input, textarea, select').removeAttr('disabled');
				}
			};

			if($(this).data('file') && $(this).is(':selected')) {
				let $fileName = $(this).data('file');
				let $fileUrl = $(this).data('file_url');
				if($(this).parents('.field_block').find('.details_file').length > 0) {
					$(this).parents('.field_block').find('.details_file').text($fileName).attr('href', $fileUrl)
				} else {
					$(this).parents('.field_block').append('<a href="'+$fileUrl+'" download class="details_file">'+$fileName+'</a>');
				}
			}
		});

		if($(this).data('switch') && $(this).data('switch').length > 0) {
			let $switchBlock = $('.' + $(this).data('switch'));
			let fieldPlaceholder = $(this).find('option:selected').text();
			let fieldName = $(this).val();
			$switchBlock.find('.placeholder').text(fieldPlaceholder);
			$switchBlock.find('input').attr('name', fieldName);
			if($(this).val() == "id_card") {
				$switchBlock.find('input').attr('oninput', "this.value=this.value.replace(/[^0-9]/g,'');")
			} else {
				$switchBlock.find('input').removeAttr('oninput');
			}

		}
	});

	$('input[type="radio"]').on('change',function(){
		if($(this).closest('.radio_block').parent().find('.sub_fields').length > 0) {
			let $radioGroup = $(this).closest('.radio_block').parent();
			$radioGroup.find('> .radio_block > .sub_fields').slideUp().find('input, select').attr('disabled', 'disabled');
			$(this).closest('.radio_block').find('> .sub_fields').stop(true,true).slideDown().find('> .radio_block > label > input, > .field_block input, .checkbox_block > label > input, > .field_block select').removeAttr('disabled');
			$radioGroup.find('> .radio_block > label').removeClass('has-error');
		};

		if($(this).data('type') && $(this).data('type').length > 0) {
			let thisTypeFields = $('.' + $(this).data('type'));
			$('.fields_by_type').slideUp().find('input:not([type*="checkbox"]), select').attr('disabled','disabled');
			thisTypeFields.stop(true,true).slideDown().find('.field_block:not(.inactive) input:not([type="checkbox"]), .field_block:not(.inactive) select').removeAttr('disabled');
		}
		
	});

	if($('.date_input').length > 0) {
		$('.date_input').each(function(){
			initDateRangePicker($(this));
		})
	};

	$('input[type="checkbox"]').on('change',function(){
		if($(this).parent().next() && $(this).parent().next().hasClass('toggle_field')) {
			if($(this).is(':checked')) {
				$(this).parent().next().stop(true, true).slideDown().find('input, select').removeAttr('disabled');
			} else {
				$(this).parent().next().slideUp().find('input, select').attr('disabled','disabled');
			}
		}

		if($(this).data('switch') && $(this).data('switch').length > 0) {
			let $switchBlock = '.' + $(this).data('switch');
			if($(this).data('switchtype') && $(this).data('switchtype') == "reverse") {
				if($(this).is(':checked')) {
					$(this).parents('.fields_group').find($switchBlock).addClass('inactive').find('input:not([type="checkbox"]), select').attr('disabled','disabled');
				} else {
					$(this).parents('.fields_group').find($switchBlock).removeClass('inactive').find('input, select').removeAttr('disabled');
				}
			} else {
				if($(this).is(':checked')) {
					$(this).parents('.fields_group').find($switchBlock).removeClass('inactive').find('input, select').removeAttr('disabled');
				} else {
					$(this).parents('.fields_group').find($switchBlock).addClass('inactive').find('input:not([type="checkbox"]), select').attr('disabled','disabled');
				}
			}
		}
	});

	$('input[data-type="num_format"]').on('keyup change', (e) => {
		$(e.currentTarget).val($(e.currentTarget).val().toString().replaceAll(' ','').replace(/\B(?=(\d{3})+(?!\d))/g, ' '));
	})

	if($('.resent_form').length > 0) {
		setTimeout(() => {
			$('.resent_form').removeClass('waiting');
		},60000)
	}
})

$(window).on('load',function(){
	if($('input[type="file"]').length > 0) {
		$('input[type="file"]').val('');
		$('input[type="file"]').each(function(){
			attachFile($(this));
		})
	};

	
	$('input[type="radio"]').each(function() {
		if($(this).is(':checked')) {
			
			if($(this).closest('.radio_block').parent().find('.sub_fields').length > 0) {
				let $radioGroup = $(this).closest('.radio_block').parent();
				$radioGroup.find('> .radio_block > .sub_fields').slideUp().find('input').attr('disabled', 'disabled');
				$(this).closest('.radio_block').find('> .sub_fields').stop(true,true).slideDown().find('> .radio_block > label > input, > .field_block input, .checkbox_block > label > input').removeAttr('disabled');
			}
			if($(this).data('type') && $(this).data('type').length > 0) {
				let thisTypeFields = $('.' + $(this).data('type'));
				$('.fields_by_type').hide().find('input:not([type="checkbox"]), select').attr('disabled','disabled');
				thisTypeFields.stop(true,true).slideDown().find('.field_block:not(.inactive) input:not([type="checkbox"]), .field_block:not(.inactive) select').removeAttr('disabled');
			}
		};
	})

	$('input[type="checkbox"]').each(function() {
		
		if($(this).is(':checked')) {
			if($(this).parent().next() && $(this).parent().next().hasClass('toggle_field')) {
				$(this).parent().next().stop(true, true).slideDown().find('input, select').removeAttr('disabled');
			}
			if($(this).data('switch') && $(this).data('switch').length > 0) {
				let $switchBlock = '.' + $(this).data('switch');
				if($(this).data('switchtype') && $(this).data('switchtype') == "reverse") {
					if($(this).is(':checked')) {
						$($switchBlock).addClass('inactive').find('input, select').attr('disabled','disabled');
					} else {
						$($switchBlock).removeClass('inactive').find('input, select').removeAttr('disabled');
					}
				} else {
					if($(this).is(':checked')) {
						$($switchBlock).removeClass('inactive').find('input, select').removeAttr('disabled');
					} else {
						$($switchBlock).addClass('inactive').find('input, select').attr('disabled','disabled');
					}
				}
				
				
			}
		}
	});

	

})