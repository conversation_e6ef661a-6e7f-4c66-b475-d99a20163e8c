@extends('core::public.master')

@section('title', $model->title.' – '.__('Forms').' – '.$websiteTitle)
@section('ogTitle', $model->title)
@section('description', $model->summary)
@section('ogImage', $model->present()->image(1200, 630))
@section('bodyClass', 'body-forms body-form-'.$model->id.' body-page body-page-'.$page->id)

@section('content')

<article class="form">
    <header class="form-header">
        <div class="form-header-container">
            <div class="form-header-navigator">
                @include('core::public._items-navigator', ['module' => 'Forms', 'model' => $model])
            </div>
            <h1 class="form-title">{{ $model->title }}</h1>
        </div>
    </header>
    <div class="form-body">
        @include('forms::public._json-ld', ['form' => $model])
        @empty(!$model->summary)
        <p class="form-summary">{!! nl2br($model->summary) !!}</p>
        @endempty
        @empty(!$model->image)
        <picture class="form-picture">
            <img class="form-picture-image" src="{{ $model->present()->image(2000) }}" width="{{ $model->image->width }}" height="{{ $model->image->height }}" alt="">
            @empty(!$model->image->description)
            <legend class="form-picture-legend">{{ $model->image->description }}</legend>
            @endempty
        </picture>
        @endempty
        @empty(!$model->body)
        <div class="rich-content">{!! $model->present()->body !!}</div>
        @endempty
        @include('files::public._documents')
        @include('files::public._images')
    </div>
</article>

@endsection
