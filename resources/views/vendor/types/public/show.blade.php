@extends('core::public.master')

@section('title', $model->title.' – '.__('Types').' – '.$websiteTitle)
@section('ogTitle', $model->title)
@section('description', $model->summary)
@section('ogImage', $model->present()->image(1200, 630))
@section('bodyClass', 'body-types body-type-'.$model->id.' body-page body-page-'.$page->id)

@section('content')

<article class="type">
    <header class="type-header">
        <div class="type-header-container">
            <div class="type-header-navigator">
                @include('core::public._items-navigator', ['module' => 'Types', 'model' => $model])
            </div>
            <h1 class="type-title">{{ $model->title }}</h1>
        </div>
    </header>
    <div class="type-body">
        @include('types::public._json-ld', ['type' => $model])
        @empty(!$model->summary)
        <p class="type-summary">{!! nl2br($model->summary) !!}</p>
        @endempty
        @empty(!$model->image)
        <picture class="type-picture">
            <img class="type-picture-image" src="{{ $model->present()->image(2000) }}" width="{{ $model->image->width }}" height="{{ $model->image->height }}" alt="">
            @empty(!$model->image->description)
            <legend class="type-picture-legend">{{ $model->image->description }}</legend>
            @endempty
        </picture>
        @endempty
        @empty(!$model->body)
        <div class="rich-content">{!! $model->present()->body !!}</div>
        @endempty
        @include('files::public._documents')
        @include('files::public._images')
    </div>
</article>

@endsection
