@extends('pages::public.master')

@section('bodyClass', 'body-clients body-clients-index body-page body-page-'.$page->id)
@section('css')
    <link href="{{ App::environment('production') ? mix('css/home.css') : asset('css/home.css') }}" rel="stylesheet">
@endsection
@section('js')
    <script src="{{ App::environment('production') ? mix('js/form1.js') : asset('js/form1.js') }}"></script>
@endsection

@section('page')

    <div class="content">
        <div class="application_page">
            <div class="application_container">
                <h1 class="page_title">@lang('OBTAINING INFORMATION / CONSENT')</h1>
                <div class="steps_block">
                    <div class="inner_step">
                        <ul class="step_list">
                            <li><a href="">1</a></li>
                            <li><a href="">2</a></li>
                            <li class="active"><a href="">3</a></li>
                            <li><a href="">4</a></li>
                        </ul>

{{--                        <div class="promo_block">@lang('Submit your Application Complaint')</div>--}}
                        {!! BootForm::open()->action(route($lang.'::sendform2step1'))->multipart() !!}
                        <div class="radio_group form_fields">
                            <div class="radio_block">
                                <label>
                                    <input type="radio" name="reference" data-validation="required" value="obligations_information"  @if(Session()->has('forminfo.form2step1.reference') && Session()->get('forminfo.form2step1.reference') == 'obligations_information' ) checked="checked" @elseif(old('reference') == "obligations_information") checked="checked" @endif >
                                    <span class="radio_button">@lang('Information on obligations')</span>
                                </label>
                                <span class="error_hint">@lang('choose one option')</span>
                                <div class="sub_fields">
                                    <div class="radio_block">
                                        <label>
                                            <input type="radio" name="all_contract" data-validation="required" value="1" @if(Session()->has('forminfo.form2step1.reference') && Session()->get('forminfo.form2step1.reference') == 'obligations_information' && Session()->get('forminfo.form2step1.all_contract') == 1) checked="checked" @elseif(old('reference')=="obligations_information" && old('all_contract')=="1") checked="checked" @endif/>
                                            <span class="radio_button">@lang('for all contracts')</span>
                                        </label>
                                        <span class="error_hint">@lang('choose one option')</span>
                                    </div>
                                    <div class="radio_block">
                                        <label>
                                            <input type="radio" name="all_contract" value="0"  @if(Session()->has('forminfo.form2step1.reference') && Session()->get('forminfo.form2step1.reference') == 'obligations_information' && Session()->get('forminfo.form2step1.all_contract') == 0) checked="checked" @elseif(old('reference')=="obligations_information" && old('all_contract')=="0") checked="checked" @endif/>
                                            <span class="radio_button">@lang('contract number')</span>
                                        </label>
                                        <div class="sub_fields">
                                            <div class="field_block full_field">
                                                <span class="placeholder">@lang('enter contract number')</span>
                                                <input type="text" name="contract_number"  disabled data-validation="required" @if(Session()->has('forminfo.form2step1.reference') && Session()->get('forminfo.form2step1.reference') == 'obligations_information' && Session()->get('forminfo.form2step1.all_contract') == 0) value="{{Session()->get('forminfo.form2step1.contract_number')}}" @elseif(old('reference')=="obligations_information" && old('all_contract') == "0")  value="{{old('contract_number')}}" @endif"/>
                                                <span class="error_hint">@lang('required')</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="radio_block">
                                <label>
                                    <input type="radio" name="reference" value="no_obligations_notice" @if(Session()->has('forminfo.form2step1.reference') && Session()->get('forminfo.form2step1.reference') == 'no_obligations_notice' ) checked="checked" @elseif(old('reference') == "no_obligations_notice") checked="checked" @endif/>
                                    <span class="radio_button">@lang('Notice of no obligations')</span>
                                </label>
                                <div class="sub_fields">
                                    <div class="field_block">
                                        <span class="placeholder">@lang('By what date is the reference needed?')</span>
                                        <input class="date_input" readonly type="text" data-single="true" name="receive_date" data-lg="am" data-maxdate="new Date()" data-format="DD.MM.YYYY" data-validation="required" placeholder="Նշել օրը" @if(Session()->has('forminfo.form2step1.receive_date')) value="{{Session()->get('forminfo.form2step1.receive_date')}}" @else value="{{old('receive_date')}}" @endif/>
                                        @if($errors->has('receive_date')) <span class="error_hint" style="max-height: inherit">{{$errors->first('receive_date')}} </span> @endif
                                        <span class="error_hint">@lang('required')</span>
                                    </div>
                                </div>
                            </div>
                            <div class="radio_block">
                                <label>
                                    <input type="radio" name="reference" value="due_dates_classifications" @if(Session()->has('forminfo.form2step1.reference') && Session()->get('forminfo.form2step1.reference') == 'due_dates_classifications' ) checked="checked" @elseif(old('reference') == "due_dates_classifications") checked="checked" @endif />
                                    <span class="radio_button">@lang('Information on due dates and classifications')</span>
                                </label>
                                <div class="sub_fields">
                                    <div class="radio_block">
                                        <label>
                                            <input type="radio" name="all_contract" data-validation="required" value="1" @if(Session()->has('forminfo.form2step1.reference') && Session()->get('forminfo.form2step1.reference') == 'due_dates_classifications' && Session()->get('forminfo.form2step1.all_contract') == 1) checked="checked" @elseif(old('reference')=="due_dates_classifications" && old('all_contract')=="1") checked="checked" @endif/>
                                            <span class="radio_button">@lang('for all contracts')</span>
                                        </label>
                                        <span class="error_hint">@lang('choose one option')</span>
                                    </div>
                                    <div class="radio_block">
                                        <label>
                                            <input type="radio" name="all_contract" value="0" @if(Session()->has('forminfo.form2step1.reference') && Session()->get('forminfo.form2step1.reference') == 'due_dates_classifications' && Session()->get('forminfo.form2step1.all_contract') == 0) checked="checked" @elseif(old('reference')=="due_dates_classifications" && old('all_contract')=="0") checked="checked" @endif/>
                                            <span class="radio_button">@lang('contract number')</span>
                                        </label>
                                        <div class="sub_fields">
                                            <div class="field_block full_field">
                                                <span class="placeholder">@lang('enter contract number')</span>
                                                <input type="text" name="contract_number" disabled data-validation="required" @if(Session()->has('forminfo.form2step1.reference') && Session()->get('forminfo.form2step1.reference') == 'due_dates_classifications' && Session()->get('forminfo.form2step1.all_contract') == 0) value="{{Session()->get('forminfo.form2step1.contract_number')}}" @elseif(old('reference')=="due_dates_classifications" && old('all_contract')=="0") {{old('contract_number')}} @endif/>
                                                <span class="error_hint">@lang('required')</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="radio_block">
                                <label>
                                    <input type="radio" name="reference" value="pc_compliance_information" @if(Session()->has('forminfo.form2step1.reference') && Session()->get('forminfo.form2step1.reference') == 'pc_compliance_information' ) checked="checked" @elseif(old('reference') == "pc_compliance_information") checked="checked" @endif/>
                                    <span class="radio_button">@lang('Information on PC compliance (transfer of money from social package account)')</span>
                                </label>
                                <div class="sub_fields">
                                    <div class="field_block full_field">
                                        <span class="placeholder">@lang('Enter the credit agreement number')</span>
                                        <input type="text" name="cp_contract_number"  disabled data-validation="required" @if(Session()->has('forminfo.form2step1.reference') && Session()->get('forminfo.form2step1.reference') == 'pc_compliance_information') value="{{Session()->get('forminfo.form2step1.cp_contract_number')}}" @elseif(old('reference')=="pc_compliance_information" ) value="{{old('cp_contract_number')}}" @endif/>
                                        <span class="error_hint">@lang('required')</span>
                                    </div>
                                </div>
                            </div>
                            <div class="radio_block">
                                <label>
                                    <input type="radio" name="reference" value="child_birth_payment" @if(Session()->has('forminfo.form2step1.reference') && Session()->get('forminfo.form2step1.reference') == 'child_birth_payment' ) checked="checked" @elseif(old('reference') == "child_birth_payment") checked="checked" @endif/>
                                    <span class="radio_button">@lang('Reference for the transfer of a one-time payment for the birth of a child')</span>
                                </label>
                                <div class="sub_fields">
                                    <div class="field_block full_field">
                                        <span class="placeholder">@lang('Enter the credit agreement number')</span>
                                        <input type="text" name="child_birth_contract_number" disabled data-validation="required" @if(Session()->has('forminfo.form2step1.reference') && Session()->get('forminfo.form2step1.reference') == 'child_birth_payment') value='{{Session()->get('forminfo.form2step1.child_birth_contract_number')}}' @elseif(old('reference')=="child_birth_payment" ) value='{{old('child_birth_contract_number')}}' @endif />
                                        <span class="error_hint">@lang('required')</span>
                                    </div>
                                    <div class="field_block">
                                        <div class="placeholder">@lang('Choose file')</div>
                                        <div class="attach_block" data-count="15">
                                            <label class="attach_label">
                                                <input type="file" name="child_birth_file[]" data-maxsize="5" accept=".jpeg,.jpg,.png,.pdf" data-sizeerror="անթույլատրելի չափ, առավելագույնը 5մբ" data-validation="required" data-typeerror="անթույլատրելի ֆայլ (.jpeg,.jpg,.png,.pdf)" title=""/>
                                                <span class="attach_btn">@lang('ADD FILE')</span>
                                            </label>
                                            <span class="error_hint">@lang('choose file')</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="radio_block">
                                <label>
                                    <input type="radio" name="reference" value="pledged_property_agreement" @if(Session()->has('forminfo.form2step1.reference') && Session()->get('forminfo.form2step1.reference') == 'pledged_property_agreement' ) checked="checked" @elseif(old('reference') == "pledged_property_agreement") checked="checked" @endif/>
                                    <span class="radio_button">@lang('Agreement to perform state registration of rights to pledged property')</span>
                                </label>
                                <div class="sub_fields">
                                    <div class="field_block full_field">
                                        <span class="placeholder">@lang('Enter the collateral agreement number')</span>
                                        <input type="text" name="pledged_property_contract_number" disabled data-validation="required" @if(Session()->has('forminfo.form2step1.reference') && Session()->get('forminfo.form2step1.reference') == 'pledged_property_agreement') value="{{Session()->get('forminfo.form2step1.pledged_property_contract_number')}}" @elseif(old('reference')=="pledged_property_agreement" ) value="{{old('pledged_property_contract_number')}}" @endif/>
                                        <span class="error_hint">@lang('required')</span>
                                    </div>
                                    <div class="field_block">
                                        <span class="placeholder">@lang('The address of the mortgaged property')</span>
                                        <input type="text" name="mortgage_property_address" oninput="this.value=this.value.replace(/[^ա-ֆԱ-Ֆ0-9 \«\»\․\՞\՝\՛\՜\֊\(\)\՛\-\,\/\։\:]/g,'');" disabled data-validation="required" @if(Session()->has('forminfo.form2step1.reference') && Session()->get('forminfo.form2step1.reference') == 'pledged_property_agreement') value="{{Session()->get('forminfo.form2step1.mortgage_property_address')}}" @elseif(old('reference')=="pledged_property_agreement" ) value="{{old('mortgage_property_address')}}" @endif/>
                                        <span class="error_hint">@lang('required')</span>
                                    </div>
                                    <div class="field_block">
                                        <span class="placeholder">@lang('The change being made')</span>
                                        <input type="text" name="change_being_made" oninput="this.value=this.value.replace(/[^ա-ֆԱ-Ֆ0-9 \«\»\․\՞\՝\՛\՜\֊\(\)\՛\-\,\/\։\:]/g,'');" disabled data-validation="required" @if(Session()->has('forminfo.form2step1.reference') && Session()->get('forminfo.form2step1.reference') == 'pledged_property_agreement') value="{{Session()->get('forminfo.form2step1.change_being_made')}}" @elseif(old('reference')=="pledged_property_agreement" ) value="{{old('change_being_made')}}" @endif/>
                                        <span class="error_hint">@lang('required')</span>
                                    </div>
                                </div>

                            </div>
                            <div class="radio_block">
                                <label>
                                    <input type="radio" name="reference" value="pledged_vehicle_agreement" @if(Session()->has('forminfo.form2step1.reference') && Session()->get('forminfo.form2step1.reference') == 'pledged_vehicle_agreement' ) checked="checked" @elseif(old('reference') == "pledged_vehicle_agreement") checked="checked" @endif/>
                                    <span class="radio_button">@lang('Consent to change the state license plate or other technical data of the pledged vehicle')</span>
                                </label>
                                <div class="sub_fields">
                                    <div class="field_block full_field">
                                        <span class="placeholder">@lang('Enter the collateral agreement number')</span>
                                        <input type="text" name="pledged_vehicle_contract_number" disabled data-validation="required" @if(Session()->has('forminfo.form2step1.reference') && Session()->get('forminfo.form2step1.reference') == 'pledged_vehicle_agreement') value="{{Session()->get('forminfo.form2step1.pledged_vehicle_contract_number')}}" @elseif(old('reference')=="pledged_vehicle_agreement" ) value="{{old('pledged_vehicle_contract_number')}}" @endif/>
                                        <span class="error_hint">@lang('required')</span>
                                    </div>
                                    <div class="field_block">
                                        <span class="placeholder">@lang('The make of the car')</span>
                                        <input type="text" name="pledge_property_model" data-transform="uppercase" oninput="this.value=this.value.replace(/[^()a-zA-Z0-9 ._*+/-]/g,'');" disabled data-validation="required" @if(Session()->has('forminfo.form2step1.reference') && Session()->get('forminfo.form2step1.reference') == 'pledged_vehicle_agreement') value="{{Session()->get('forminfo.form2step1.pledge_property_model')}}" @elseif(old('reference')=="pledged_vehicle_agreement" ) value="{{old('pledge_property_model')}}" @endif/>
                                        <span class="error_hint">@lang('required')</span>
                                    </div>
                                    <div class="field_block">
                                        <span class="placeholder">@lang('Vehicle license plate')</span>
                                        <input type="text" name="pledge_property_serial" data-transform="uppercase" oninput="this.value=this.value.replace(/[^a-zA-Z0-9]/g,'');" maxlength="7" disabled data-validation="required" @if(Session()->has('forminfo.form2step1.reference') && Session()->get('forminfo.form2step1.reference') == 'pledged_vehicle_agreement') value="{{Session()->get('forminfo.form2step1.pledge_property_serial')}}" @elseif(old('reference')=="pledged_vehicle_agreement" ) value="{{old('pledge_property_serial')}}" @endif/>
                                        <span class="error_hint">@lang('required')</span>
                                    </div>
                                    <div class="field_block">
                                        <span class="placeholder">@lang('The reason for the change')</span>
                                        <input type="text" name="change_reason" oninput="this.value=this.value.replace(/[^ա-ֆԱ-Ֆ0-9 \«\»\․\՞\՝\՛\՜\֊\(\)\՛\-\,\/\։\:]/g,'');" disabled data-validation="required"  @if(Session()->has('forminfo.form2step1.reference') && Session()->get('forminfo.form2step1.reference') == 'pledged_vehicle_agreement') value="{{Session()->get('forminfo.form2step1.change_reason')}}" @elseif(old('reference')=="pledged_vehicle_agreement" ) value="{{old('change_reason')}}" @endif/>
                                        <span class="error_hint">@lang('required')</span>
                                    </div>
                                </div>
                            </div>
                            <div class="radio_block">
                                <label>
                                    <input type="radio" name="reference" value="additional_pledged_property" @if(Session()->has('forminfo.form2step1.reference') && Session()->get('forminfo.form2step1.reference') == 'additional_pledged_property' ) checked="checked" @elseif(old('reference') == "additional_pledged_property") checked="checked" @endif/>
                                    <span class="radio_button">@lang('Agreement to Terminate Pledge of Additional Pledged Property')</span>
                                </label>
                                <div class="sub_fields">
                                    <div class="field_block full_field">
                                        <span class="placeholder">@lang('Enter the collateral agreement number')</span>
                                        <input type="text" name="additional_pledged_contract_number" disabled data-validation="required" @if(Session()->has('forminfo.form2step1.reference') && Session()->get('forminfo.form2step1.reference') == 'additional_pledged_property') value="{{Session()->get('forminfo.form2step1.additional_pledged_contract_number')}}" @elseif(old('reference')=="additional_pledged_property" ) value="{{old('additional_pledged_contract_number')}}" @endif/>
                                        <span class="error_hint">@lang('required')</span>
                                    </div>
                                    <div class="field_block">
                                        <span class="placeholder">@lang('The address of the mortgaged property')</span>
                                        <input type="text" name="additional_mortgage_address" oninput="this.value=this.value.replace(/[^ա-ֆԱ-Ֆ0-9 \«\»\․\՞\՝\՛\՜\֊\(\)\՛\-\,\/\։\:]/g,'');" disabled data-validation="required" @if(Session()->has('forminfo.form2step1.reference') && Session()->get('forminfo.form2step1.reference') == 'additional_pledged_property') value="{{Session()->get('forminfo.form2step1.additional_mortgage_address')}}" @elseif(old('reference')=="additional_pledged_property" ) value="{{old('additional_mortgage_address')}}" @endif />
                                        <span class="error_hint">@lang('required')</span>
                                    </div>
                                    <div class="field_block">
                                        <span class="placeholder">@lang('pledge termination basis')</span>
                                        <input type="text" name="pledge_termination_basis" oninput="this.value=this.value.replace(/[^ա-ֆԱ-Ֆ0-9 \«\»\․\՞\՝\՛\՜\֊\(\)\՛\-\,\/\։\:]/g,'');" disabled data-validation="required" @if(Session()->has('forminfo.form2step1.reference') && Session()->get('forminfo.form2step1.reference') == 'additional_pledged_property') value="{{Session()->get('forminfo.form2step1.pledge_termination_basis')}}" @elseif(old('reference')=="additional_pledged_property" ) value="{{old('pledge_termination_basis')}}" @endif/>
                                        <span class="error_hint">@lang('required')</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="response_error">
                        </div>
                        <div class="buttons_block">
                            <button style="justify-content: center;" class="validate_btn btn_next" type="submit">@lang('NEXT')</button>
                        </div>
                        {!! BootForm::close() !!}
                    </div>
                </div>
            </div>
        </div>
    </div>







@endsection
