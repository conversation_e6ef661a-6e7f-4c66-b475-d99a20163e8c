@extends('pages::public.master')

@section('bodyClass', 'body-clients body-clients-index body-page body-page-'.$page->id)
@section('css')
    <link href="{{ App::environment('production') ? mix('css/home.css') : asset('css/home.css') }}" rel="stylesheet">
@endsection
@section('js')
    <script src="{{ App::environment('production') ? mix('js/home.js') : asset('js/home.js') }}"></script>
@endsection

@section('page')
<div class="content">
    <div class="application_page step_search">
        <div class="application_container">
            <h1 class="page_title">@lang("tracking page title")</h1>
            <div class="step_search_block">
                <div class="search_block">
                    <div class="block_description">
                        @lang("Please fill the Application number below (XX-XX-XXX)")
                    </div>
                    <div class="field_block">
                        <form  id="mulberry_tracking">
                            <input type="text" aria-label="" id="tracking_id" name="search" autocomplete="off" placeholder="@lang('Application number')">
                            <button type="submit" class="icon_search"></button>
                        </form>
                    </div>
                </div>
                <div class="result_block active"><!-- add active class for open result list-->
                    <div class="block_description">@lang("Searching Results")</div>
                    <div class="list_block" id="gv_list_block" style="display: none">
                        <div class="title_result">@lang("application")</div>
                        <div id="empty_block" class="empty_block" style="display: none">@lang("There is no matches in our base. Please try another Application Number")</div>
                        <div id="gv_tracking_info" style="display: none">
                            <ul >
                                <li>
                                    <div class="title_list">@lang('Date')</div>
                                    <div class="result_list gv_date">14.08.2024</div>
                                </li>
                                <li>
                                    <div class="title_list">@lang('Time')</div>
                                    <div class="result_list gv_time">12:56</div>
                                </li>
                                <li>
                                    <div class="title_list">@lang('App Type')</div>
                                    <div class="result_list gv_app">Lorem Ipsum</div>
                                </li>
                                <li>
                                    <div class="title_list ">@lang('Status')</div>
                                    <div class="result_list gv_status pending">Pending</div>
                                </li>
                            </ul>
                            <div class="info_block">@lang("application deadline text")</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@push('js')

<script>
    $(document).ready(function() {
        $('#mulberry_tracking').on('submit', function(e) {
            e.preventDefault();        
            const trackingId = document.getElementById('tracking_id').value ?? 0;
            const route = '/api/application-status/' +trackingId;
            /* console.log("url",route);
            console.log("trackingId",trackingId); */
             $.ajax({
                url: route,
                method: 'get',
                success: function(result){
                    $('.gv_date').text(result.data.date);
                    $('.gv_time').text(result.data.time);
                    $('.gv_app').text(result.data.title);
                    $('.gv_status').text(result.data.status);
                    /* $('.gv_status').removeClass('pending');
                    $('.gv_status').addClass('success'); */
                    document.getElementById("gv_list_block").style.display = "block";
                    document.getElementById("empty_block").style.display = "none";
                    document.getElementById("gv_tracking_info").style.display = "block";
                    console.log("result",result);
                },
                error: function(result) {
                    document.getElementById("gv_list_block").style.display = "block";
                    if(result.status == 404){
                        document.getElementById("gv_tracking_info").style.display = "none";
                        document.getElementById("empty_block").style.display = "block";
                    }else{
                        document.getElementById("gv_tracking_info").style.display = "none";
                        document.getElementById("empty_block").style.display = "block";
                    }
                    console.log("error result",result);
                    /*console.log("error status",result.status);
                    alert('Please insert tracking id'); */
                }
            }) 
        })
    });
</script>
@endpush
