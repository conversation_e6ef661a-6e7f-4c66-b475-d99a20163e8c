@extends('pages::public.master')

@section('bodyClass', 'body-clients body-clients-index body-page body-page-' . $page->id)
@section('css')
    <link href="{{ App::environment('production') ? mix('css/home.css') : asset('css/home.css') }}" rel="stylesheet">
@endsection
@section('js')
    <script src="{{ App::environment('production') ? mix('js/form1.js') : asset('js/form1.js') }}"></script>
@endsection

@section('page')

    <div class="content">
        <div class="application_page">
            <div class="application_container">

                @if ($form_info['formtype'] == 1)
                    <h1 class="page_title">@lang('APPLICATION PROTEST')</h1>
                @elseif($form_info['formtype'] == 2)
                    <h1 class="page_title">@lang('OBTAINING INFORMATION / CONSENT')</h1>
                @elseif($form_info['formtype'] == 3)
                    <h1 class="page_title">@lang('OTHER') </h1>
                @endif
                <div class="steps_block">
                    <div class="result_block">
                        <div class="block_description">
                            @if ($form_info['formtype'] == 2)
                                @if ($client_model->payment_status == '1')
                                    @lang('Your payment success but application does not sent, contact with administration')
                                @else
                                    @lang('Your application dont sent')
                                @endif
                            @else
                                @lang('Your application dont sent')
                            @endif
                        </div>
                        @if (!Session::has('source') && Session::get('source') != 'cashme')
                            <div class="buttons_block">
                                <a href="{{ url(TypiCMS::homeUrl()) }}" class="btn_next">@lang('Go main page')</a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection
