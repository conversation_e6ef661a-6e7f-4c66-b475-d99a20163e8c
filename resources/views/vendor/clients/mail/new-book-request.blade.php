<!DOCTYPE>
<html>
<head>
    <title> @lang('Your Jets')</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="color-scheme" content="only">
</head>
<body>
<table  bgcolor="#E5E5E5" style="background: #E5E5E5;background-image:linear-gradient(to right,#E5E5E5,#E5E5E5);">
    <tr>
        <td style="padding: 20px;">
            <table bgcolor="#E5E5E5" width="600px"  border="0" cellpadding="0" cellspacing="0"
                   style="max-width:600px;min-width: 600px; margin:0 auto;font-family:Arial, Helvetica, 'sans-serif' !important;color: #777777">
                <tr bgcolor="#FFFFFF">
                    <td style="padding: 20px 0 20px 20px;vertical-align: middle;white-space: nowrap;">
                        <img src="{{asset('images/footer_logo.png')}}" style="max-width: 120px" title="" alt=""/>
                    </td>
                    <td colspan="2" style="white-space: nowrap;vertical-align: middle;">
                        @if($social->menulinks->count() > 0)
                            @foreach($social->menulinks as $item)
                                @switch($item->class)
                                    @case('icon_facebook')
                                    <a href="{{ url($item->href) }}" @if(!$loop->first)style="padding-left: 10px" @endif><img src="{{asset('images/Path.png')}}" title="" alt=""/></a>
                                    @break
                                    @case('icon_youtube')
                                    <a href="{{ url($item->href) }}" @if(!$loop->first)style="padding-left: 10px" @endif><img src="{{asset('images/yt_dark.png')}}" title="" alt=""/></a>
                                    @break
                                    @case('icon_twitter')
                                    <a href="{{ url($item->href) }}" @if(!$loop->first)style="padding-left: 10px" @endif><img src="{{asset('images/tw_dark.png')}}" title="" alt=""/></a>
                                    @break
                                    @case('icon_instagram')
                                    <a href="{{ url($item->href) }}" @if(!$loop->first)style="padding-left: 10px" @endif><img src="{{asset('images/insta_dark.png')}}" title="" alt=""/></a>
                                    @break
                                    @case('icon_linkedin')
                                    <a href="{{ url($item->href) }}" @if(!$loop->first)style="padding-left: 10px" @endif><img src="{{asset('images/in_dark.png')}}" title="" alt=""/></a>
                                    @break
                                @endswitch
                            @endforeach
                        @endif
                    </td>
                    <td style="padding: 20px 20px 20px 0;vertical-align: middle;text-align: end;">
                        <a href="" style="font-size: 12px;line-height: 14px;color: #E4AB2F;text-decoration: none;">{{request()->getHost()}}</a>
                        <br />
                        @if(config('typicms.webmaster_email'))
                        <a href="" style="font-size: 12px;line-height: 14px;color: #E4AB2F;text-decoration: none;">{{config('typicms.webmaster_email')}}</a>
                        @endif
                        <br />
                        @if(config('typicms.contact_phone'))
                        <a href="" style="font-size: 12px;line-height: 14px;color: #666666;text-decoration: none;">{{config('typicms.contact_phone')}}</a>
                        @endif
                    </td>
                </tr>
                <tr>
                    <td colspan="4" style="box-sizing:border-box;vertical-align:top;padding-bottom: 40px;padding-top: 40px">
                        <span style="font-size: 24px;display: block;margin: 0 auto;line-height: 27px;padding-bottom:10px;text-transform:uppercase;color: #333333;text-transform: uppercase;">
                           @lang('Dear') {{$book->name}} {{$book->last_name}}
                        </span>
                        <span style="font-size: 14px;line-height: 16px; display: block;margin: 0 auto;color: inherit;">
                            @lang('send us a request text')
                        </span>
                    </td>
                </tr>
                <tr bgcolor="#FFFFFF" style="max-width: 600px;margin: 0 auto 40px;border-top: 4px solid #E4AB2F;">
                    <td colspan="4" style="box-sizing:border-box;padding:40px;vertical-align:top;border-top: 4px solid #E4AB2F;">
                        <span style="font-size: 16px;color: #333333;display: block;line-height: 18px;padding-bottom:5px;text-transform:uppercase;">
                            @lang('Personal information')
                        </span>
                        <span style="font-size: 14px;line-height: 16px; color: #666666; display: block;margin: 0 auto;">
                            @lang('Here are the credentials you’ve provided on our website')
                        </span>
                    </td>
                </tr>
                <tr bgcolor="#FFFFFF" style="max-width: 600px;margin: 0 auto;">
                    <td colspan="4"
                        style="float: left;padding:0 40px 40px 40px;vertical-align: top;box-sizing:border-box; padding-bottom: 40px;">
                        <div style="font-size: 14px;line-height: 16px;color: #4D4D4D;">@lang('Name')</div>
                        <div style="font-size: 14px;line-height: 16px;color: #666666;">{{$book->name}}</div>
                    </td>
                    <td colspan="4" style="box-sizing:border-box;padding:0 40px 40px 40px;">
                        <div style="font-size: 14px;line-height: 16px;color: #4D4D4D;">@lang('Last name')</div>
                        <div style="font-size: 14px;line-height: 16px;color: #666666;">{{$book->last_name}}</div>
                    </td>
                </tr>

                <tr bgcolor="#FFFFFF" style="max-width: 600px;margin: 0 auto;">
                    <td colspan="4"
                        style="float: left;padding:0 40px 40px 40px;vertical-align: top;box-sizing:border-box; padding-bottom: 40px;">
                        <div style="font-size: 14px;line-height: 16px;color: #4D4D4D;">@lang('Phone')</div>
                        <div style="font-size: 14px;line-height: 16px;color: #666666;white-space: nowrap;">{{$book->phone}}</div>
                    </td>
                    <td colspan="4" style="box-sizing:border-box;padding:0 40px 40px 40px;">
                        <div style="font-size: 14px;line-height: 16px;color: #4D4D4D;">@lang('Email')</div>
                        <div style="font-size: 14px;line-height: 16px;color: #666666;">{{$book->email}}</div>
                    </td>
                </tr>
                <tr>
                    <td colspan="4" bgcolor="#E5E5E5" style="height: 40px;background: #E5E5E5"></td>
                </tr>
                <tr bgcolor="#FFFFFF" style="max-width: 600px;margin: 0 auto;">
                    <td colspan="4" style="box-sizing:border-box;vertical-align:top;padding:40px;border-top: 4px solid #E4AB2F;">
                        <span style="font-size: 16px;color: #333333;display: block;line-height: 18px;padding-bottom:5px;text-transform:uppercase;">
                            @lang('Personal information 2')
                        </span>
                        <span style="font-size: 14px;line-height: 16px; color: #666666; display: block;margin: 0 auto;">
                            @lang('Here are the credentials you’ve provided on our website 2')
                        </span>
                    </td>
                </tr>
                <tr bgcolor="#FFFFFF" style="max-width: 600px;margin: 0 auto;">
                    <td colspan="4" style="float: left;padding:0 40px 40px 40px;vertical-align: top;box-sizing:border-box; padding-bottom: 40px;">
                        <div style="font-size: 12px;color: #999999;line-height: 13px;padding-bottom: 5px">@lang('FROM')</div>
                        <div style="font-size: 14px;line-height: 16px;color: #333333;">{{$book->from}}</div>
                    </td>
                    <td colspan="4" style="box-sizing:border-box;padding:0 40px 40px 40px;">
                        <div style="font-size: 12px;color: #999999;line-height: 13px;padding-bottom: 5px">@lang('TO')</div>
                        <div style="font-size: 14px;line-height: 16px;color: #333333;">{{$book->to}}</div>
                    </td>
                </tr>
                <tr bgcolor="#FFFFFF" style="max-width: 600px;margin: 0 auto;">
                    <td colspan="4"
                        style="float: left;padding:0 40px 40px 40px;vertical-align: top;box-sizing:border-box; padding-bottom: 40px;">
                        <div style="font-size: 12px;color: #999999;line-height: 13px;padding-bottom: 5px">@lang('WHEN'):</div>
                        <div style="font-size: 14px;line-height: 16px;color: #333333;white-space: nowrap;">{{$book->when}}  {{$book->time}}</div>
                        <div style="font-size: 12px;line-height: 14px;color: #666666;">{{Carbon\Carbon::parse($book->when)->format('l')}}</div>
                    </td>
                    <td colspan="1" style="box-sizing:border-box;padding:0 0 40px 40px">
                        <div style="font-size: 12px;color: #999999;line-height: 13px;padding-bottom: 5px">@lang('WHO')</div>
                        <div style="font-size: 14px;line-height: 16px;color: #333333;">{{$book->adults}}</div>
                        <div style="font-size: 12px;line-height: 14px;color: #666666;">@lang('Adult')</div>
                    </td>
                    <td colspan="1" style="box-sizing:border-box;padding:0 40px 40px 0">
                        <div style="font-size: 12px;color: #ffffff;line-height: 13px;padding-bottom: 5px;opacity: 0;">@lang('WHO')</div>
                        <div style="font-size: 14px;line-height: 16px;color: #333333;">{{$book->children}}</div>
                        <div style="font-size: 12px;line-height: 14px;color: #666666;">@lang('Children')</div>
                    </td>
                    <td colspan="1"></td>
                </tr>
                <tr bgcolor="#FFFFFF" style="max-width: 600px;margin: 0 auto;">
                    <td colspan="4" style="padding:0 40px 10px 40px;vertical-align: top;box-sizing:border-box;">
                        <span style="color:#999999;font-size:13px;display: block;line-height: 12px;">@lang('YOUR COMMENT')</span>
                    </td>
                </tr>
                <tr bgcolor="#FFFFFF" style="max-width: 600px;margin: 0 auto;">
                    <td colspan="4"
                        style="padding:0 40px 40px 40px;vertical-align: top;box-sizing:border-box;">
                        <div style="padding:20px;font-size: 14px;border: 1px solid #E6E6E6;line-height: 20px;">
                            {{$book->message}}
                        </div>
                    </td>
                </tr>
                <tr>
                    <td colspan="4" style="text-align: center;vertical-align: bottom;box-sizing:border-box;padding-top: 30px;">
                        <span style="font-family: Arial, Helvetica, sans-serif; font-size: 17px; text-align: center; min-width:600px;width:600px;margin:0 auto;max-width: 600px;color: #777777;padding-bottom: 45px;">
                         © {{ now()->year }} {{$websiteTitle}}. @lang('all_rights_reserved')
                     </span>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
</table>

