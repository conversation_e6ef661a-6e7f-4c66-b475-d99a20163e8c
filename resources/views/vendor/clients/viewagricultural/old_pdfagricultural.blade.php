<!DOCTYPE html>
<html>
<head>
    @include('core::public.favicons')
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, viewport-fit=cover">
</head>
<body>
<div class="header">
    <img src="https://application.globalcredit.am/img/logo_footer.png" alt="" title=""/>
    <h1 class="page_title">{{__('agricultural loan')}}</h1>
</div>
<div class="content">
    <table class="top">
        <thead>
        <tr>
            <th colspan="2">ՊԱՀԱՆՋՎՈՂ ՎԱՐԿԻ ՏՎՅԱԼՆԵՐԸ</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td class="info_type">Պահանջվող վարկի տեսակը</td>
            <td class="info_inner">{{$loan_type}}</td>
        </tr>
        <tr>
            <td class="info_type">Վարկի գումար</td>
            <td class="info_inner">{{$loan_amount}} {{$loan_currency}}</td>
        </tr>
        <tr>
            <td class="info_type">Վարկի ժամկետ</td>
            <td class="info_inner">{{$loan_period}} ամիս</td>
        </tr>
        <tr>
            <td class="info_type">Վարկի նպատակ</td>
            <td class="info_inner">@isset($loan_purpose){{$loan_purpose == 'agricultural_loan_purpose_other' ? $loan_purpose_other : __($loan_purpose)}}@endisset</td>
        </tr>
        </tbody>
        <tfoot></tfoot>
        <thead>
        <tr>
            <th colspan="2">ՀԱՃԱԽՈՐԴԻ ՏՎՅԱԼՆԵՐԸ</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td class="info_type">«ԳԼՈԲԱԼ ԿՐԵԴԻՏ» ՈՒՎԿ ՓԲԸ հաճախորդ</td>
            <td class="info_inner">{{ $gc_customer ? __('gc_customer') : __('not_gc_customer') }}</td>
        </tr>
        @if($customer_type == 'legal_person')
        <tr>
            <td class="info_type">Կազմակերպության կամ ԱՁ֊ի անվանումը</td>
            <td class="info_inner">{{$company_name}}</td>
        </tr>
        <tr>
            <td class="info_type">Գործունեության տեսակը</td>
            <td class="info_inner">{{__($activity_type)}}</td>
        </tr>
        <tr>
            <td class="info_type">ՀՎՀՀ</td>
            <td class="info_inner">{{$hvhh}}</td>
        </tr>
        @endif
        @if($customer_type != 'legal_person')
            <tr>
                <td class="info_type">Անուն ազգանուն հայրանուն</td>
                <td class="info_inner">{{$full_name}}</td>
            </tr>
            <tr>
                <td class="info_type">Անձը հաստատող փաստաթուղթ</td>
                <td class="info_inner">{{$passport ?? $id_card}}</td>
            </tr>
            <tr>
                <td class="info_type">Սոց․ քարտի համարը</td>
                <td class="info_inner">{{$social_card_number}}</td>
            </tr>
            <tr>
                <td class="info_type">Հեռախոսի համար(ներ)ը</td>
                <td class="info_inner">{{$phone}}</td>
            </tr>
            <tr>
                <td class="info_type">Էլ․ փոստ</td>
                <td class="info_inner">{{$email}}</td>
            </tr>
        @endif
        <tr>
            <td class="info_type">Գրանցման հասցե</td>
            <td class="info_inner">{{$registration_address}}</td>
        </tr>
        <tr>
            <td class="info_type">Գործունեության հասցե</td>
            <td class="info_inner">@if(isset($same_registration_address)) {{$registration_address}} @else {{$business_address}} @endif</td>
        </tr>
        <tr>
            <td class="info_type">Իրավաբանական հասցե</td>
            <td class="info_inner">@isset($same_business_address)
                    @isset($same_registration_address)  {{$registration_address}} @else {{$business_address}} @endisset
                    @else {{$legal_address}} @endisset
            </td>
        </tr>
        </tbody>
        <tfoot></tfoot>
        @if($customer_type == 'legal_person')
        <thead>
        <tr>
            <th colspan="2">ՂԵԿԱՎԱՐԻ ՏՎՅԱԼՆԵՐԸ (լրացվում է տնօրենի կամ Ա/Ձ-ի տվյալները)</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td class="info_type">Անուն ազգանուն հայրանուն</td>
            <td class="info_inner">{{$full_name}}</td>
        </tr>
        <tr>
            <td class="info_type">Անձը հաստատող փաստաթուղթ</td>
            <td class="info_inner">{{$passport ?? $idcard}}</td>
        </tr>
        <tr>
            <td class="info_type">Սոց․ քարտի համարը</td>
            <td class="info_inner">{{$social_card_number}}</td>
        </tr>
        <tr>
            <td class="info_type">Հեռախոսի համար(ներ)ը</td>
            <td class="info_inner">{{$phone}}</td>
        </tr>
        <tr>
            <td class="info_type">Էլ․ փոստ</td>
            <td class="info_inner">{{$email}}</td>
        </tr>
        </tbody>
        @endif
    </table>
    <table class="footer">
        <tr>
            <td class="apply_col">Դիմող՝</td>
            <td class="signature_col">{{$company_name ?? $full_name}}</td>
            <td class="name_col"><div class="name_surname">Կազմակերպության կամ ԱՁ֊ի անվանումը</div></td>
        </tr>
        @isset($full_name)
            <tr>
                <td class="apply_col">Տնօրեն՝</td>
                <td class="signature_col">{{$full_name}}</td>
                <td class="name_col"><div class="name_surname">Ղեկավարի Անուն ազգանուն հայրանունը</div></td>
            </tr>
        @endisset
        <tr>
            <td class="apply_col">Ամսաթիվ՝</td>
            <td class="signature_col">{{date('d.m.Y')}} թ.</td>
            <td class="name_col"></td>
        </tr>

    </table>
</div>
{!! BootForm::open()->action(route(app()->getLocale().'::agriculturalmulberry',['uuid'=>Session()->get('forminfo.uuid')])) !!}
<div class="buttons_block" style="display: flex;margin: 30px -10px 0;justify-content: center;">
    <a href="{{route(app()->getLocale()."::sendagriculturalpreview",['uuid'=>Session()->get('forminfo.uuid')])}}" style="background: #fff;border: 1px solid #91332A;border-radius: 5px;padding: 13px;min-width: 247px;
    display: flex;align-items: center;justify-content: center;margin: 0 10px;font-size: 130%;line-height: 22px;color: #91332A;cursor: pointer;text-decoration: none;" class="btn_back">@lang('BACK')</a>
    <button class="validate_btn btn_next" style="background: #91332A;border: 1px solid #91332A;border-radius: 5px;padding: 13px;min-width: 247px;
    display: flex;align-items: center;justify-content: center;margin: 0 10px;font-size: 130%;line-height: 22px;color: #ffffff;cursor: pointer;" type="submit">@lang('SEND')</button>
</div>
{!! BootForm::close() !!}
</body>
<style>
    @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600&display=swap');

    @font-face {
        font-family: 'signature';
        font-style: normal;
        font-weight: 400;
        src: url({{ asset('fonts/handwriting/HovhannesTumanianU.ttf')}}) format('truetype');
        src: url({{ asset('fonts/handwriting/HovhannesTumanianU.eot?#iefix')}}) format('embedded-opentype'),
        url({{ asset('fonts/handwriting/HovhannesTumanianU.woff2')}}) format('woff2'),
        url({{ asset('fonts/handwriting/HovhannesTumanianU.woff')}}) format('woff'),
        url({{ asset('fonts/handwriting/HovhannesTumanianU.ttf')}}) format('truetype');
    }
    body {
        width: 1284px;
        margin: 0 auto;
        border: none;
        padding: 40px;
        font-family: 'Montserrat', Arial,  Helvetica, sans-serif;
        box-sizing: border-box;
    }
    .header {
        text-align: center;
    }
    .header img {
        display: inline-block;
        vertical-align: bottom;
        width: 120px;
        height: auto;
    }
    .page_title {
        font-weight: 600;
        white-space: normal;
        margin: 30px 0 0;
        font-size: 26px;
        line-height: 24px;
        text-align: center;
        box-sizing: border-box;
        color: #8E342A;
    }
    .content {
        padding-top: 60px;
    }
    .content .top {
        font-size: 24px;
        line-height: 32px;
        border-spacing: 0;
        width: 100%;
    }
    .top thead {
        text-align: center;
        background: #8E342A;
        color: #ffffff;
    }
    .top thead th {
        padding: 10px 20px;
    }
    .top tbody {
        margin-bottom: 20px;
    }
    .top tfoot {
        height: 40px;
        display: block;
    }
    .top .info_type {
        border: 1px solid #c4c4c4;
        padding: 10px 20px;
        border-top: none;
        width: 50%;
        color: #666666;
    }
    .top .info_inner {
        border: 1px solid #c4c4c4;
        padding: 10px 20px;
        border-top: none;
        border-left: none;
        width: 50%;
    }
    .footer {
        width: 100%;
        border-spacing: 0;
        margin-top: 60px;
        font-size: 24px;
        line-height: 40px;
    }
    .footer .apply_col {
        width: 15%;
        padding-left: 40px;
        padding-right: 20px;
    }
    .footer td {
        padding-top: 10px;
        padding-bottom: 10px;
    }
    .footer .signature {
        border-bottom: 1px dashed;
        width: 100%;
    }
    .footer .signature_col {
        width: 35%;
        text-align: center;
        padding: 0 20px;
    }
    .footer .name_col {
        width: 50%;
        padding-left: 20px;
    }
    .footer .signature {
        font-family: 'signature';
    }
</style>
</html>
