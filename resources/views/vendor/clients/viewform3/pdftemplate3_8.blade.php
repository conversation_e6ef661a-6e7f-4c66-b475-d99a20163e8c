<!DOCTYPE html>
<html>
<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, viewport-fit=cover"/>
    <title>{{ $title }}</title>
    <script src="{{ asset('js/form1.js')}}"></script>
    <link rel="stylesheet" href="{{ asset('css/forms.css')}}">
    {{-- <style>
        @font-face {
            font-family: 'SegoeUI';
            src: url({{ storage_path('fonts/SegoeUIRegular/SegoeUIRegular.ttf') }}) format("truetype");
            font-weight: 400;
            font-style: normal;
        }

        @font-face {
            font-family: 'signature';
            src: url({{ asset('fonts/handwriting/HovhannesTumanianU.ttf')}}) format('truetype');
            src: url({{ asset('fonts/handwriting/HovhannesTumanianU.eot?#iefix')}}) format('embedded-opentype'),
                url({{ asset('fonts/handwriting/HovhannesTumanianU.woff2')}}) format('woff2'),
                url({{ asset('fonts/handwriting/HovhannesTumanianU.woff')}}) format('woff'),
                url({{ asset('fonts/handwriting/HovhannesTumanianU.ttf')}}) format('truetype');
            font-weight: 400;
            font-style: normal;
            font-display:swap;
        }

        body form {
            position: sticky;
            bottom: 0;
        }

        body {
            max-width: 1284px;
            width: 100%;
            margin: 0 auto;
            border: none;
            padding: 40px;
            font-family: 'SegoeUI', Arial,  Helvetica, sans-serif;
            box-sizing: border-box;
        }
        .header {
            white-space: nowrap;
            display: flex;
            align-items: flex-end;
        }
        .header img {
            display: inline-block;
            vertical-align: bottom;
            width: 140px;
            height: auto;
            margin-right: 20px;
        }
        .page_title {
            font-weight: normal;
            white-space: normal;
            margin: 0;
            font-size: 28px;
            line-height: 24px;
            text-align: center;
            flex:1;
            box-sizing: border-box;
        }
        .content {
            padding-top: 100px;
            min-height: auto !important;
        }
        .content .top {
            text-align: right;
            font-size: 28px;
            line-height: 40px;
        }
        .middle {
            padding-top: 100px;
        }
        .app_title {
            text-align: center;
            font-size: 40px;
            line-height: 50px;
            color: #000000;
            margin: 0;
        }
        .app_text {
            font-size: 30px;
            line-height: 45px;
            text-indent: 80px;
            padding-top: 100px;
            text-align: justify;
        }
        .app_text p {
            margin: 0
        }
        .footer {
            width: 100%;
            border-spacing: 0;
            margin-top: 100px;
            font-size: 28px;
            line-height: 40px;
        }
        .footer .apply_col {
            width: 20%;
            padding-left: 80px;
        }
        .footer .signature,
        .footer .name_surname {
            border-bottom: 1px dashed;
            text-align: center;
            margin: 0 auto;
            min-height: 41px;
            width: 320px;
        }
        .footer .signature_col {
            width: 50%;
            text-align: center;
        }
        .footer .name_col {
            width: 30%;
            text-align: center;
        }
        .footer .signature {
            font-family: 'signature';
        }
        .buttons_block button.btn_next.loading,
        .buttons_block a.btn_next.loading {
            pointer-events: none;
            color: transparent !important;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            position: relative;
        }
        .buttons_block button.btn_next.loading:after ,
        .buttons_block a.btn_next.loading:after {
            color: #ffffff;
            position: absolute;
            top: 50%;
            left: 50%;
            margin: -4px -8px;
            border-radius: 50%;
            display: block;
            width: 8px;
            height: 8px;
            content: "";
            animation: loadAnimation 1.6s infinite linear;
        }

        @keyframes loadAnimation {
            9% {
                box-shadow: none;
            }
            10% {
                box-shadow: -8px 0 0;
            }
            35% {
                box-shadow: -8px 0 0;
            }
            36% {
                box-shadow: -8px 0 0, 8px 0 0;
            }
            60% {
                box-shadow: -8px 0 0, 8px 0 0;
            }
            61% {
                box-shadow: -8px 0 0, 8px 0 0, 24px 0 0;
            }
            85% {
                box-shadow: -8px 0 0, 8px 0 0, 24px 0 0;
            }
            86% {
                box-shadow: none
            }
        }
        @media (max-width: 1024px) {
            .app_title{
                font-size: 36px;
                line-height: 1.25em
            }
            .header img{
                width: 120x;
            }
            body{
                padding: 40px 20px
            }
            .content,
            .middle{
                padding-top: 70px;
            }
            .content .top,
            .page_title{
                font-size: 24px;
                line-height: 1.5em;
            }
            .app_text{
                font-size: 26px;
                line-height: 1.5em;
                padding-top: 50px;
                text-indent: 40px;
            }
            .footer{
                font-size: 24px;
                line-height: 1.5em;
                margin-top: 70px
            }
            .footer .apply_col{
                padding-left: 0;
            }
            .footer .name_col{
                text-align: right;
            }
            .footer .signature, 
            .footer .name_surname{
                margin: 0 0 0 auto;
                text-align: right;
                width: auto;
            }
        }
        @media (max-width: 767px) {
            .app_title{
                font-size: 32px
            }
            .content,
            .middle{
                padding-top: 50px;
            }
            .content .top,
            .page_title{
                font-size: 20px;
            }
            .app_text{
                font-size: 22px;
                padding-top: 30px;
                text-indent: 20px;
            }
            .footer{
                font-size: 20px;
                margin-top: 50px;
            }
        }
        @media (max-width: 575px) {
            
            .buttons_block a,
            .buttons_block button{
                min-width: auto !important;
                width: 150px !important;
                font-size: 110% !important;
                line-height: 1.25em !important;
                padding: 8px 0 !important;
            }
            .header img{
                width: 100px;
            }
            .app_title{
                font-size: 28px
            }
            .content,
            .middle{
                padding-top: 50px;
            }
            .content .top,
            .page_title{
                font-size: 18px;
            }
            .app_text{
                font-size: 20px;
            }
            .footer{
                font-size: 18px;
            }
        }
        @media (max-width: 479px) {
            .header img{
                width: 80px;
            }
        }
    </style> --}}
</head>
<body>
<div class="header">
    <img src="https://application.globalcredit.am/img/logo_footer.png" alt="ԳԼՈԲԱԼ ԿՐԵԴԻՏ" title="ԳԼՈԲԱԼ ԿՐԵԴԻՏ"/>
    <h1 class="page_title">{{$title}}</h1>
</div>
<div class="content">
    <div class="top">
        <div class="company_name">«ԳԼՈԲԱԼ ԿՐԵԴԻՏ» ՈՒՎԿ ՓԲԸ</div>
        <div class="director_name">{{$director_name}}</div>
        <div class="client_name">Հաճախորդ՝<span>&nbsp;&nbsp; {{$fio}} &nbsp;&nbsp;</span></div>
        <div class="client_document">Անձնագիր/Ն/Ք <span>&nbsp;&nbsp;{{$passport}}</span></div>
        <div class="client_phone">Հեռախոսահամար`<span>&nbsp;&nbsp;{{$phone}}</span></div>
        <div class="client_email">Էլ. փոստ`<span>&nbsp;&nbsp;{{$email}}</span></div>
    </div>
    <div class="middle">
        <div class="app_title">Դիմում</div>

        <div class="app_text">
            <p>Հարգելի {{$application_whom}},</p>
            <p>Խնդրում եմ  {{$loan_car_contract_number}}  պայմանագով վարկի գումարը փոխանցել  @if($account_number_owner) իմ @else վաճառողի @endif  {{$loan_car_account_number}}  հաշվեհամարին:</p>
        </div>
    </div>
    <table class="footer">
        <tr>
            <td rowspan="2" class="apply_col">Դիմող՝</td>
            <td class="name_col"><div class="name_surname"> {{$full_name}} </div></td>
        </tr>
        <tr>
            <td class="name_col">(անուն ազգանուն)</td>
        </tr>
    </table>
</div>
{!! BootForm::open()->action(route(app()->getLocale().'::form3mulberry',['uuid'=>Session()->get('forminfo.uuid')])) !!}
<div class="buttons_block" style="display: flex;margin: 30px -10px 0;justify-content: center;">
    <a href="{{route(app()->getLocale()."::form3")}}" style="background: #fff;border: 1px solid #91332A;border-radius: 5px;padding: 13px 0;min-width: 247px;
    display: flex;align-items: center;justify-content: center;margin: 0 10px;font-size: 130%;line-height: 22px;color: #91332A;cursor: pointer;text-decoration: none;" class="btn_back">@lang('BACK')</a>
    <button class="validate_btn btn_next" style="background: #91332A;border: 1px solid #91332A;border-radius: 5px;padding: 13px 0;min-width: 247px;
    display: flex;align-items: center;justify-content: center;margin: 0 10px;font-size: 130%;line-height: 22px;color: #ffffff;cursor: pointer;" type="submit">@lang('SEND')</button>
</div>
{!! BootForm::close() !!}
</body>

</html>
