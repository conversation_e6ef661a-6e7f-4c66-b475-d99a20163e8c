# FormService Documentation

## Overview

The `FormService` class is a key component of the Clients module in the GForm application. It provides functionality for validating clients and sending SMS verification codes. The service integrates with the Aobayt external service for client verification and communication.

## Location

`Modules/Clients/Services/FormService.php`

## Dependencies

- `App\Services\Aobayt` - External service for client verification
- `Illuminate\Support\Facades\Log` - For logging
- `TypiCMS\Modules\Clients\Models\Client` - Client model
- `Bkwld\Croppa\Facade` - Image manipulation (imported but not used in the code)

## Methods

### 1. `validateClient($data)`

Validates a client's information using the Aobayt service.

**Parameters:**
- `$data` - Array containing:
  - `ssn` - Social Security Number
  - `phone` - Phone number
  - `email` - Email address
  - `uuid` - Unique identifier

**Process:**
1. Checks if the application is in test mode (`config('typicms.cash_me_mode') == 'test'`)
2. If in test mode, uses hardcoded test data
3. Otherwise, calls the Aobayt service to validate the client
4. If validation is successful:
   - Creates a new Client record with the validated data
   - Sends an email verification through Aobayt
   - Returns success status
5. If validation fails:
   - Returns error status with appropriate code

**Returns:**
- Array containing:
  - `status` - 'success' or 'error'
  - `aobyte_status` - Status from Aobayt (on success)
  - `code` - Error code (on error)
  - `state` - Error state (on error)

### 2. `sendSms($uuid, $code)`

Sends an SMS verification code using the Aobayt service.

**Parameters:**
- `$uuid` - Unique identifier for the client
- `$code` - Verification code to send

**Process:**
1. Logs the SMS send request
2. Checks if the application is in test mode
3. If in test mode, returns a hardcoded success response
4. Otherwise, calls the Aobayt service to send the SMS

**Returns:**
- Response from the Aobayt service

## Test Mode

The service includes a test mode that can be enabled through configuration:

```php
config('typicms.cash_me_mode') = 'test'
```

When in test mode:
- Client validation uses hardcoded test data
- SMS sending returns a hardcoded success response
- Email sending is bypassed

This allows testing the application without making actual calls to external services.

## Integration with Aobayt Service

The FormService integrates with the Aobayt service for:

1. **Client Validation:**
   - Validates client information (SSN, phone, email)
   - Retrieves client details (name, passport)

2. **Email Verification:**
   - Sends verification emails to clients

3. **SMS Verification:**
   - Sends SMS verification codes to clients

## Error Handling

The service includes error handling for various scenarios:

1. **Validation Errors:**
   - If Aobayt returns an error, it's captured and returned
   - If the response format is unexpected, a generic error is returned

2. **Email Sending Errors:**
   - If email sending fails, an error is returned with the appropriate code

## Usage in Controllers

The FormService is used by several controllers in the Clients module:

1. **CashmeController:**
   - Uses `validateClient()` to validate client information
   - Uses `sendSms()` to send verification codes

2. **Form1Controller, Form2Controller, Form3Controller:**
   - Use the FormService for client validation and verification

## Code Examples

### Example 1: Validating a Client

```php
// In a controller
public function validateClientInfo(Request $request)
{
    $formService = app(FormService::class);
    
    $data = [
        'ssn' => $request->ssn,
        'phone' => $request->phone,
        'email' => $request->email,
        'uuid' => Str::uuid()->toString()
    ];
    
    $result = $formService->validateClient($data);
    
    if ($result['status'] == 'success') {
        return response()->json(['success' => true, 'message' => 'Client validated successfully']);
    } else {
        return response()->json(['success' => false, 'message' => 'Validation failed', 'error' => $result], 422);
    }
}
```

### Example 2: Sending an SMS Verification Code

```php
// In a controller
public function sendVerificationCode(Request $request)
{
    $formService = app(FormService::class);
    $code = rand(1000, 9999);
    
    $result = $formService->sendSms($request->uuid, $code);
    
    if (isset($result['success']) && $result['success'] == 'true') {
        return response()->json(['success' => true, 'message' => 'Verification code sent']);
    } else {
        return response()->json(['success' => false, 'message' => 'Failed to send verification code'], 500);
    }
}
```

## Security Considerations

1. **Client Validation:**
   - Uses an external service (Aobayt) for validation
   - Stores only necessary client information

2. **Test Mode:**
   - Provides a way to test the application without exposing real client data
   - Uses hardcoded test data that doesn't represent real clients

## Configuration

The service uses the following configuration parameters:

1. **Test Mode:**
   - `config('typicms.cash_me_mode')` - When set to 'test', bypasses actual validation

## Logging

The service includes logging for SMS sending:

```php
Log::info('Aobyte Sms Send request:', ['uuid' => $uuid, 'code' => $code]);
```

This helps track SMS sending operations and troubleshoot issues.
