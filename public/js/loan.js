$(document).ready(function(){

    //detect Active NavItem
    function detectActiveNavItem(navbar, navList, currentItem) {
        var currentLeft = currentItem.offset().left;
        var listLeft = navList.offset().left;
        var currentWidth = currentItem.width();
        var scrollSize = currentLeft - listLeft - (window.innerWidth - currentWidth) / 2;
        if (window.innerWidth < $smallTSize)
            navbar.animate({scrollLeft: scrollSize}, 1000);
    }

    //mobile active btn
    detectActiveNavItem($('.sidebar_loan'), $('.sidebar_loan ul'), $('.sidebar_loan .selected'));
    var ww = window.innerWidth;
    $(window).resize(function () {
        var nww = window.innerWidth;
        if (Math.abs(ww - nww) >= 50) {
            detectActiveNavItem($('.sidebar_loan'), $('.sidebar_loan ul'), $('.sidebar_loan .selected'));
        }
        ww = nww;
    });

});