var $mobileSize = 991;
var $smallTSize = 768;
var changing = null;

function isTouchDevice() {
    return 'ontouchstart' in document.documentElement;
};

function detectDevice() {
    if (navigator.userAgent.search("Safari") >= 0 && navigator.userAgent.search("Chrome") < 0) {
        $('body').addClass('ios_device');
    }
    ;
    if (isTouchDevice()) {
        $('html').addClass('touch');
    } else {
        $('html').addClass('web');
    }
}

function closeAllMenues(evt) {
    detectDevice();
    $('.exchange_rates').slideUp(300).parent().removeClass('opened');
    $('.for_sale_list').slideUp(300).parent().removeClass('opened');
    $('.drop_btn').parent().removeClass('opened');
    $('.drop_block').slideUp(300);

    if ($('.search_block').data('type') && $('.search_block').data('type') == 'close') {
        $('.search_block').removeClass('opened');
    }

    if (isTouchDevice() && window.innerWidth >= $mobileSize) {
        $('.header .menu_list li').removeClass('opened');
        $('.header .submenu_list').fadeOut(300);
    }
}

function ignorBodyClick(evt) {
    evt.stopPropagation();
}

function ignorMobileBodyClick(evt) {
    if (window.innerWidth < 992) {
        evt.stopPropagation();
    }
}

function dropList(dropButton, dropList, dropItem, dropElement) {
    if (dropButton.parents(dropItem).hasClass('opened')) {
        dropButton.parents(dropItem).removeClass('opened').find(dropElement).slideUp(300);
        $('.inner_answer').slideUp(300);
    } else {
        dropButton.parents(dropList).find('.opened').removeClass('opened');
        dropButton.parents(dropList).find(dropElement).slideUp(300);
        dropButton.parents(dropItem).addClass('opened').find(dropElement).stop(true, true).slideDown(300);
        setTimeout(function () {
            if ($(dropList).find('.opened').length > 0) {
                if (dropButton.parents(dropItem).offset().top < $(document).scrollTop()) {
                    $('body,html').animate({scrollTop: dropButton.parents(dropItem).offset().top}, 300);
                }
            }
        }, 300)
    }

};

function activateLineSlider(rangeInput, showedInput) {
    var $typing = false;
    var minVal = parseInt(rangeInput.data('min'));
    var maxVal = parseInt(rangeInput.data('max'));
    var step = rangeInput.data('step') ? rangeInput.data('step') : 1000;
    rangeInput.slider({
        from: minVal,
        to: maxVal,
        step: step,
        round: 1,
        onstatechange: function (value) {
            if(!$typing) {
                showedInput.val(value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, " "));
            }

            rangeInput.parent().find('.l').width(Math.round((value - minVal) / (maxVal - minVal) * 100) + '%');
        }
    });

    showedInput.on('keyup', function () {
        $typing = true
        $(this).val($(this).val().toString().replace(/\B(?=(\d{3})+(?!\d))/g, " "));
        var newVal = parseInt($(this).val().split(" ").join(""));
        if (newVal >= minVal && newVal <= maxVal) {
            rangeInput.slider('value', newVal);
        }
        $typing = false
    });

    showedInput.on('change', function () {
        $typing = true
        var newVal = parseInt($(this).val().split(" ").join(""));
        if (newVal < minVal || !newVal) {
            rangeInput.slider('value', minVal);
            $(this).val(minVal);
        } else if (newVal > maxVal) {
            rangeInput.slider('value', maxVal);
            $(this).val(maxVal);
        } else {
            rangeInput.slider('value', newVal);
        }
        $typing = false
    });
}

function mobMenuTrigger(e) {
    e.preventDefault();
    if ($('body').hasClass('menu_opened')) {
        $('body').removeClass('menu_opened');
    } else {
        $('.header .menu_list li').removeClass('opened');
        $('.header .submenu_list').hide();
        $('.header .menu_inner').animate({scrollTop: 0}, 0);
        $('body').addClass('menu_opened');
    }
}

function detectContentHeight() {
    var freeSpace = window.innerHeight - $('.header').height() - $('.footer').height();
    if (freeSpace > 0) {
        $('.content').css('min-height', freeSpace);
    } else {
        $('.content').css('min-height', 0);
    }
    ;
    $('.footer').css('opacity', 1);
}

function toggleSearch(evt) {
    if (!$('.search_block').hasClass('opened')) {
        evt.preventDefault();
        closeAllMenues(evt);
        evt.stopPropagation();
        $('.search_block').addClass('opened').find('input').focus();
    } else if (!$('.search_block input').val()) {
        $('.search_block input').focus();
        evt.preventDefault();
    } else {
        evt.stopPropagation();
    }
}

function focusEmptySearch(evt) {
    if (!$('.search_block input').val()) {
        evt.preventDefault();
        $('.search_block input').focus();
    }
}

function checkFields() {
    $('form input,select').change(function () {
        if ($(this).val().length > 0) {
            $(this).parent().find('.individual_hint').show();
            $(this).parent().find('.standard_hint').hide();
        } else {
            $(this).parent().find('.individual_hint').hide();
            $(this).parent().find('.standard_hint').show();
        }

        if ($('.confirm_field').length > 0) {
            $('.confirm_field').on('keyup change', function () {
                if ($(this).val() == $(this).parents('form').find('.password_field').val()) {
                    $(this).parent().removeClass('has-error');
                    passwordConfirm = true;
                }
            })
        }
    });
}

function checkPassConfirm() {
    var passValue = $('.confirm_field').parents('form').find('.password_field').val();
    var passConfirm = $('.confirm_field').val();
    if (passValue && passValue != passConfirm && $('.pass_fields').css('display') != "none") {
        $('.confirm_field').parent().addClass('has-error');
        passwordConfirm = null;
    } else {
        passwordConfirm = true;
    }
}

function checkForm(e) {
    var $button = $(this);
    if ($button.parents('form').find('.confirm_field').length > 0) {
        checkPassConfirm();
    } else {
        passwordConfirm = true;
    }
    $.validate({
        scrollToTopOnError: false,
        onError: function () {
            if ($button.parents('form').hasClass('login_form') || $button.parents('form').hasClass('register_form')) {

                $('.has-error').each(function () {
                    var errorInputType = $(this).find('input').attr('type');
                    $('input[type="' + errorInputType + '"]').parents('.general_field').addClass('has-error');
                });
            }
            ;
        },
        onSuccess: function () {
            if (!passwordConfirm) {
                return false;
            }
        }

    });
    setTimeout(function () {
        if ($button.hasClass('checkout_submit') && $('.has-error').length > 0) {
            $('body, html').animate({scrollTop: $('.has-error').eq(0).offset().top - $('.header').height()}, 1000);
        }
    }, 100)


};

function openLanguages(evt) {
    evt.preventDefault();
    if (!$('.language_block').hasClass('opened')) {
        closeAllMenues(evt);
        evt.stopPropagation();
        $('.language_block').addClass('opened');
        $('.language_list').stop(true, true).slideDown();
    }
    ;
}

function dropToggle(evt) {
    evt.preventDefault();
    if (!$(this).parent().hasClass('opened')) {
        closeAllMenues(evt);
        evt.stopPropagation();
        $(this).parent().addClass('opened').find('.drop_block').stop(true, true).slideDown(300);
    }
};

function openSubWithClick(evt) {
    evt.preventDefault();
    if (isTouchDevice() && window.innerWidth >= $mobileSize) {
        if (!$(this).parents('li').hasClass('opened')) {
            closeAllMenues(evt);
            evt.stopPropagation();
            $(this).parents('li').addClass('opened').find('.submenu_list').stop(true, true).slideDown(300);
        }
    } else if (window.innerWidth < $mobileSize) {
        if ($(this).parents('li').hasClass('opened')) {
            $(this).parents('li').removeClass('opened').find('.submenu_list').slideUp(300);
        } else {
            $('.header .menu_list > li.opened').removeClass('opened');
            $('.header .menu_list .submenu_list').slideUp(300);
            $(this).parents('li').addClass('opened').find('.submenu_list').stop(true, true).slideDown(300);
        }
    }
}

var delayTime = null;

function openSubWithHover() {
    if (!isTouchDevice() && window.innerWidth >= $mobileSize) {
        if (delayTime) {
            clearTimeout(delayTime);
        }
        var $item = $(this).parents('li');
        $item.addClass('hovered');
        delayTime = setTimeout(function () {
            if ($item.hasClass('hovered')) {
                $item.addClass('opened').find('.submenu_list').stop(true, true).slideDown(300);
            }
        }, 300)
    }
}

function mouseLeaveItem() {
    $(this).parents('li').removeClass('hovered');
}

function closeSubWithHover() {
    if (!isTouchDevice()) {
        $(this).removeClass('opened').find('.submenu_list').fadeOut(300);
    }
}

function comboHover() {
    $(this).parents('.combo_hover').addClass('hovered');
}

function comboUnHover() {
    $(this).parents('.combo_hover').removeClass('hovered');
}

function tabSwitch($section, $block, $btns, $btn) {
    if (!$btn.hasClass('selected')) {
        $btn.parents($btns).find('.selected').removeClass('selected');
        $btn.parents($section).find($block).removeClass('selected');
        $btn.addClass('selected');
        $btn.parents($section).find($block + '.' + $btn.data('tab')).addClass('selected');
    }
}

function detectCallPosibillity() {
    if (/Android|iPhone|iPad|iPod|BlackBerry|BB|PlayBook|IEMobile|Windows Phone|Kindle|Silk|Opera Mini/i.test(navigator.userAgent)) {
        $('.phone_link').addClass('clickable');
    }
    $('.phone_link').click(function (e) {
        if (!$(this).hasClass('clickable')) {
            e.preventDefault();
        }
    })
}

function initSlider(sliderEl, sliderOpts) {
    sliderEl.slick(sliderOpts);
}

function accordionToggle(_button, _block, _list) {
    if (_button.parent(_block).hasClass('opened')) {
        _button.parent(_block).removeClass('opened');
        _button.parent(_block).find(_list).slideUp(300);
    } else {
        $(_block).removeClass('opened');
        $(_list).slideUp(300);
        _button.parent(_block).addClass('opened').find(_list).stop(true, true).slideDown(300);
    }
}

function goToTarget() {
    var endPoint = $(this).data('endpoint');
    $('html,body').animate({scrollTop: $('[data-target="' + endPoint + '"]').offset().top - $('.header_inner').height()}, 500);
    if ($('[data-target="' + endPoint + '"]').parent().hasClass('tab_buttons')) {
        $('[data-target="' + endPoint + '"]').trigger('click');
    }
}

function openPopup(evt) {
    evt.preventDefault();
    $('body').addClass('popup_opened');
    var popupName = '.' + $(this).data('popup');
    var popupContent = $(this).data('content') ? '.' + $(this).data('content') : null;
    $(popupName).addClass('showed');
    if (popupContent) {
        $(popupName).find('.popup_content').removeClass('showed');
        $(popupContent).addClass('showed');
    }
}

function moveSecondMenu() {
    if (window.innerWidth >= $mobileSize && $('.header_main .second_menu').length < 1) {
        $('.header .phone_block').after('<div class="second_menu">' + $('.header .menu_block .second_menu').html() + '</div>');
        $('.header .menu_block .second_menu').remove();
        $('.header .menu_list > li').removeClass('opened');
        $('.header .submenu_list').hide();
        $('.second_menu .submenu_btn').click(openSubWithClick);
        $('body').removeClass('menu_opened');
    } else if (window.innerWidth < $mobileSize && $('.header .menu_block .second_menu').length < 1) {
        $('.header .menu_inner').append('<div class="second_menu">' + $('.header_main .second_menu').html() + '</div>');
        $('.header_main .second_menu').remove();
        $('.header .menu_list > li').removeClass('opened');
        $('.header .submenu_list').hide();
        $('.second_menu .submenu_btn').click(openSubWithClick);
    }
}

function closePopup() {
    $('body').removeClass('popup_opened');
    $('.popup_block').removeClass('showed');
}

function changeCount(countBlock, decreaseBtn, increaseBtn, countInput) {

    $(countInput).each(function () {
        var maxValue = $(this).data('max') ? $(this).data('max') : Math.pow(10, $(this).attr('maxlength')) - 1;
        if ($(this).val() == 1) {
            $(this).parents(countBlock).find(decreaseBtn).addClass('inactive');
        } else if ($(this).val() == maxValue) {
            $(this).parents(countBlock).find(increaseBtn).addClass('inactive');
        }
    });

    $(document).on('change', countInput, function () {
        var thisDecrease = $(this).parents(countBlock).find(decreaseBtn);
        var thisIncrease = $(this).parents(countBlock).find(increaseBtn);
        var maxValue = $(this).data('max') ? $(this).data('max') : Math.pow(10, $(this).attr('maxlength')) - 1;
        if ($(this).val() <= 1) {
            $(this).val(1);
            thisDecrease.addClass('inactive');
            thisIncrease.removeClass('inactive');
        } else if ($(this).val() >= maxValue) {
            $(this).val(maxValue);
            thisIncrease.addClass('inactive');
            thisDecrease.removeClass('inactive');
        } else {
            thisIncrease.removeClass('inactive');
            thisDecrease.removeClass('inactive');
        }
    })

    $(document).on('click', decreaseBtn, function () {
        var thisInput = $(this).parent().find('input');
        var thisIncrease = $(this).parent().find(increaseBtn);
        var _value = thisInput.val();
        thisIncrease.removeClass('inactive');
        if (_value > 1) {
            _value--;
            thisInput.val(_value);
        }
        if (_value == 1) {
            $(this).addClass('inactive');
        }
    });

    $(document).on('click', increaseBtn, function () {
        var thisInput = $(this).parent().find('input');
        var thisDecrease = $(this).parent().find(decreaseBtn);
        var _value = thisInput.val();
        var maxValue = thisInput.data('max') ? thisInput.data('max') : Math.pow(10, thisInput.attr('maxlength')) - 1;
        thisDecrease.removeClass('inactive');
        if (_value < maxValue) {
            _value++;
            thisInput.val(_value);
        }
        if (_value == maxValue) {
            $(this).addClass('inactive');
        }
    });
}

function exchange(evt) {
    evt.preventDefault();
    if (!$(this).parent().hasClass('opened')) {
        closeAllMenues(evt);
        evt.stopPropagation();
        $(this).parent().addClass('opened').find('.exchange_rates').stop(true, true).slideDown(300);
        $('body').removeClass('menu_opened')
    }
}

function property_sale(evt) {
    evt.preventDefault();
    if (!$(this).parent().hasClass('opened')) {
        closeAllMenues(evt);
        evt.stopPropagation();
        $(this).parent().addClass('opened').find('.for_sale_list').stop(true, true).slideDown(300);
        $('body').removeClass('menu_opened')
    }
}

function bnt_answer(evt) {
    evt.preventDefault();
    if (!$(this).parent().hasClass('opened')) {
        closeAllMenues(evt);
        evt.stopPropagation();
        $(this).parent().addClass('opened').find('.inner_answer').stop(true, true).slideDown(300);
    } else {
        $(this).parent().removeClass('opened').find('.inner_answer').stop(true, true).slideUp(300);

    }
}

var myMap = null;

function mapInit() {
    ymaps.ready(function () {
        myMap = new ymaps.Map("map-canvas", {
            center: [40.177200, 44.503490],
            zoom: 11,
            controls: ['zoomControl']
        });

        if (isTouchDevice()) {
            myMap.behaviors.disable('drag');
        } else {
            myMap.behaviors.disable('scrollZoom');
        }
        myMap.behaviors.disable('scrollZoom');

        var placemark;
        var placeMarks = [];

        function createMarker($branch) {
            var _coords = $branch.data('coords').split(',');
            var _center = [_coords[0] * 1, _coords[1] * 1];
            var _street = $branch.data('street');
            var _title = $branch.data('title');

            var contentString = "<div class=" + "iw_content" + ">" +
                '<div class="iw_branch_name">' +  _title + '</div>' + '<div class="iw_branch_stree">' + _street + '</div>' +
                '</div>';

            placemark = new ymaps.Placemark(_center, {
                balloonContent: contentString,
            }, {
                iconLayout: 'default#image',
                // iconImageHref: './css/images/map_pin.png',
                iconImageSize: [35, 35],
            });

            myMap.geoObjects.add(placemark);

            placeMarks.push(placemark);
        }

        function showAllMarkers() {
            $('#map_coords li').each(function () {
                createMarker($(this));
            });
            if($('#map_coords li').length > 1) {
                myMap.setBounds(myMap.geoObjects.getBounds());
            } else {
                var _coords = $('#map_coords li').data('coords').split(',');
                var _center = [_coords[0] * 1, _coords[1] * 1];
                myMap.setCenter(_center);
                myMap.setZoom(16);
            }
        }

        $('.branch').click(function (e) {
            e.preventDefault();
            if($(this).hasClass('active')) {
                $(this).removeClass('active');
                $('#map_coords li.inactive').each(function(){
                    myMap.geoObjects.add(placeMarks[$(this).index()]);
                    $(this).removeClass('inactive');
                })
            } else {
                $('.branch.active').removeClass('active');
                $(this).addClass('active');
                var type = $(this).data('type');
                $('#map_coords li').each(function () {
                    if($(this).data('single-type') != type){

                        myMap.geoObjects.remove(placeMarks[$(this).index()]);
                        $(this).addClass('inactive');
                    } else {
                        if($(this).hasClass('inactive')) {
                            myMap.geoObjects.add(placeMarks[$(this).index()]);
                            $(this).removeClass('inactive');
                        }
                    }
                })

            };

            // var positions = _coords.split(",");
            // var _center = [positions[0] * 1, positions[1] * 1];
            // myMap.setCenter(_center);
            // myMap.setZoom(18);
            // $('body,html').animate({scrollTop: $('#map-canvas').offset().top}, 700);
        });
        showAllMarkers();
    })
}


function initDatePicker($dateInput) {
    var $parrent = $dateInput.parent();
    var $dateFormat = $dateInput.data('format') ? $dateInput.data('format') : 'DDDD.MM.YY';
    var $dateLg = $dateInput.data('lg') ? $dateInput.data('lg') : 'en';
    var $minDate = $dateInput.data('mindate') ? eval($dateInput.data('mindate')) : false;
    var $maxDate = $dateInput.data('maxdate') ? eval($dateInput.data('maxdate')) : false;
    // var dropdowns = $dateInput.data('dropdowns') ? true : false;
    var daysList = {
        "en": ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"],
        "am": ["Կի", "Եկ", "Եք", "Չո", "Հի", "Ու", "Շա"],
        "ru": ["Вс", "Пн", "Вт", "Ср", "Чт", "Пт", "Сб"]
    };

    var monthsList = {
        "en": ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"],
        "am": ["Հունվար", "Փետրվար", "Մարտ", "Ապրիլ", "Մայիս", "Հունիս", "Հուլիս", "Օգոստոս", "Սեպտեմբեր", "Հոկտեմբեր", "Նոյեմբեր", "Դեկտեմբեր"],
        "ru": ["Январь", "Февраль", "Март", "Апрель", "Май", "Июнь", "Июль", "Август", "Сентябрь", "Октябрь", "Ноябрь", "Декабрь"]
    };

    $dateInput.daterangepicker({
        //opens: 'right',
        maxDate: $maxDate,
        minDate: $minDate,
        parentEl: $parrent,
        //showDropdowns: dropdowns,
        singleDatePicker: true,
        autoUpdateInput: false,
        autoApply: true,
        showDropdowns:true,
        locale: {
            daysOfWeek: daysList[$dateLg],
            monthNames: monthsList[$dateLg],
        },

    }, function (chosen_date, end) {
        $dateInput.val(chosen_date.format($dateFormat));
    });

}

$(document).ready(function () {
    //detect device type
    detectDevice();
    detectCallPosibillity();

    //close dropdowns with outside click
    $('body').click(closeAllMenues);

    //close dropdowns with outside click
    $('.menu_btn').click(mobMenuTrigger);
    $('.header .menu_list .submenu_btn').hover(openSubWithHover, mouseLeaveItem);
    $('.header .menu_list > li').hover(function () {
    }, closeSubWithHover);
    $('.header .submenu_btn').click(openSubWithClick);

    // form front validation
    if ($('.validate_btn').length > 0) {
        checkFields();
        $('.validate_btn').click(checkForm);
    }

    $('.tab_buttons li').click(function (e) {
        e.preventDefault();
        tabSwitch('.tab_section', '.tab_block', '.tab_buttons', $(this));
    });

    //hover effect with multiple links hover
    $('.combo_link').hover(comboHover, comboUnHover);

    //drop element open close
    $('.drop_btn').click(dropToggle);

    if ($('.exchange_btn').length > 0) {
        $('.exchange_btn').click(exchange);
        $('.exchange_rates').click(ignorBodyClick);
    }


    if ($('.for_sale_btn').length > 0) {
        $('.for_sale_btn').click(property_sale);
        $('.for_sale_list').click(ignorBodyClick);
    }

    //hidden search open/close
    $('.search_block button[type="submit"]').click(function (evt) {
        if ($('.search_block').data('type') && $('.search_block').data('type') == 'close') {
            toggleSearch(evt);
        } else {
            focusEmptySearch(evt);
        }
    });

    $('.search_block input').click(function (evt) {
        if ($('.search_block').data('type') && $('.search_block').data('type') == 'close') {
            ignorBodyClick(evt);
        }
    });

    $('.management_page .members_list li, .list_management li').each(function () {
        $(this).delay($(this).index() * 200).animate({opacity: 1}, 500);
    });


    $('.companies_page .companies_list li').each(function () {
        $(this).delay($(this).index() * 200).animate({opacity: 1}, 500);
    });

    $('.sale_page .sale_list li').each(function () {
        $(this).delay($(this).index() * 200).animate({opacity: 1}, 500);
    });

    $('.blog_page .news_list li').each(function () {
        $(this).delay($(this).index() * 200).animate({opacity: 1}, 500);
    });

    //pay online
    $('.popup_block').click(function (e) {
        if (!$(e.target).is('.popup_container') && !$(e.target).is('.popup_container *:not(.popup_close)')) {
            closePopup();
        }
    });
    $('.popup_btn').click(openPopup);
    $('.popup_close').click(function (e) {
        e.preventDefault();
    });

    if ($('.map_block').length > 0) {
        mapInit();
    }

    if ($('.sitemap_list,.option_list').length > 0) {
        $('.question_block').click(function (e) {
            e.preventDefault();
            dropList($(this), $('.faq_list'), 'li', '.answer_block');
        })
    }

    if ($('.sitemap_list').length > 0) {
        $('.bnt_answer').click(bnt_answer);
    }

    if ($('select').length > 0) {
        $('select').select2({
            minimumResultsForSearch: Infinity
        })
    }

    $(window).scroll(function () {
        if ($('.animation_transform').length > 0) {
            $('.animation_transform').each(function () {
                if ($(this).offset().top + $(this).height() / 2 - 250 < $(document).scrollTop() + window.innerHeight) {
                    $(this).addClass('active');
                }
            });
        }
        if ($('.animation_block').length > 0) {
            $('.animation_block').each(function () {
                if ($(this).offset().top + $(this).height() / 2 - 250 < $(document).scrollTop() + window.innerHeight) {
                    $(this).addClass('showed');
                }
            })
        }
    }).trigger('scroll');
});

$(window).on('load', function () {
    if ($('.footer_menu').length > 0) {
        $('.footer_menu .footer_menu_list').masonry({
            itemSelector: '.footer_menu .footer_menu_list > li',
            columnWidth: '.footer_menu .footer_menu_list > li',
            percentPosition: true,
            transitionDuration: 0
        })
    }

    $(window).resize(function () {
        //detect content min height and show footer
        detectContentHeight();
        if ($('.header .second_menu').length > 0) {
            moveSecondMenu();
        }
    }).trigger('resize');

    setTimeout(function () {
        if ($('.num_block[data-num]').length > 0) {
            $(window).scroll(function () {
                if ($('.num_block').length > 0) {
                    $('.num_block').each(function () {
                        if (!$(this).hasClass('showed') && $(document).scrollTop() + window.innerHeight >= $(this).offset().top + 100) {
                            var $this = $(this),
                                countTo = $this.data('num');
                            $(this).addClass('showed');
                            $({countNum: $this.text()}).animate({
                                    countNum: countTo
                                },

                                {
                                    duration: 2500,
                                    easing: 'linear',
                                    step: function () {
                                        $this.text(Math.floor(this.countNum));
                                    },
                                    complete: function () {
                                        $this.text(this.countNum);
                                    }

                                });
                        }

                    });
                }
            }).trigger('scroll')
        }
    }, 1000);

    $('.loader').fadeOut(300);
    $('body').removeClass('loading');
    setTimeout(function () {
        $('.loader').remove();
    }, 1000)

});