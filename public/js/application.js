function initDateRangePicker($dateInput) {
    var $parrent = $dateInput.parent();
    var $dateFormat = $dateInput.data('format') ? $dateInput.data('format') : 'DDDD.MM.YY';
    var $dateLg = $dateInput.data('lg') ? $dateInput.data('lg') : 'en';
    var $minDate = $dateInput.data('mindate') ? eval($dateInput.data('mindate')) : false;
    var $maxDate = $dateInput.data('maxdate') ? eval($dateInput.data('maxdate')) : false;
	var $singleDate = $dateInput.data('single') ? $dateInput.data('single') == true : false;
    // var dropdowns = $dateInput.data('dropdowns') ? true : false;
    var daysList = {
        "en": ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"],
        "am": ["Կի", "Եկ", "Եք", "Չո", "Հի", "Ու", "Շա"],
        "ru": ["Вс", "Пн", "Вт", "Ср", "Чт", "Пт", "Сб"]
    };

    var monthsList = {
        "en": ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"],
        "am": ["Հունվար", "Փետրվար", "Մարտ", "Ապրիլ", "Մայիս", "Հունիս", "Հուլիս", "Օգոստոս", "Սեպտեմբեր", "Հոկտեմբեր", "Նոյեմբեր", "Դեկտեմբեր"],
        "ru": ["Январь", "Февраль", "Март", "Апрель", "Май", "Июнь", "Июль", "Август", "Сентябрь", "Октябрь", "Ноябрь", "Декабрь"]
    };

    $dateInput.daterangepicker({
        maxDate: $maxDate,
        minDate: $minDate,
        parentEl: $parrent,
        autoUpdateInput: false,
        autoApply: true,
		singleDatePicker: $singleDate,
        locale: {
            daysOfWeek: daysList[$dateLg],
            monthNames: monthsList[$dateLg],
        },

    }, function (chosen_date, end) {
		$singleDate ? $dateInput.val(chosen_date.format($dateFormat)) : $dateInput.val(chosen_date.format($dateFormat) + ' - ' + end.format($dateFormat));
    });

}


$(document).ready(function(){
    

	$('select').on('change',function(){
		$(this).find('option').each(function(){
			console.log($(this).data('sub'));
			if($(this).data('sub')) {
				let $subElement = $('.'+$(this).data('sub'));
				if(!$(this).is(':selected')) {
					$subElement.hide().find('input, textarea, select').attr('disabled','disabled');
				} else {
					$subElement.show().find('input, textarea, select').removeAttr('disabled');
				}
			}
		})
	});

	$('input[type="radio"]').on('change',function(){
		let $radioGroup = $(this).closest('.radio_block').parent();
		$radioGroup.find('> .radio_block > .sub_fields').slideUp().find('input').attr('disabled', 'disabled');
		$(this).closest('.radio_block').find('> .sub_fields').stop(true,true).slideDown().find('> .radio_block > label > input, > .field_block input, .checkbox_block > label > input').removeAttr('disabled');
		$radioGroup.find('> .radio_block > label').removeClass('has-error');
	});

	if($('.date_input').length > 0) {
		$('.date_input').each(function(){
			initDateRangePicker($(this));
		})
	};

	$('input[type="checkbox"]').on('change',function(){
		if($(this).parent().next() && $(this).parent().next().hasClass('toggle_field')) {
			if($(this).is(':checked')) {
				$(this).parent().next().stop(true, true).slideDown().find('input, select').removeAttr('disabled');
			} else {
				$(this).parent().next().slideUp().find('input, select').attr('disabled','disabled');
			}
		}
	})
})

$(window).on('load',function(){
	if($('input[type="file"]').length > 0) {
		$('input[type="file"]').val('');
		$('input[type="file"]').each(function(){
			attachFile($(this));
		})
	};

	
	$('input[type="radio"]').each(function() {
		if($(this).is(':checked')) {
			let $radioGroup = $(this).closest('.radio_block').parent();
			$radioGroup.find('> .radio_block > .sub_fields').slideUp().find('input').attr('disabled', 'disabled');
			$(this).closest('.radio_block').find('> .sub_fields').stop(true,true).slideDown().find('> .radio_block > label > input, > .field_block input, .checkbox_block > label > input').removeAttr('disabled');
		}
	})

	$('input[type="checkbox"]').each(function() {
		if($(this).is(':checked')) {
			$(this).parent().next().stop(true, true).slideDown().find('input, select').removeAttr('disabled');
		}
	})

})