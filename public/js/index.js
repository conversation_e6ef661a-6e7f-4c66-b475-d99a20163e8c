$(document).ready(function () {
    //main sider
    if ($('.main_slider').length > 0) {
        $('.main_slider').slick({
            dots: true,
            autoplay: false,
            responsive: [
                {
                    breakpoint: 1361,
                    settings: {
                        dots: false,
                    }
                },
            ]
        })
    }
    if ($('.line_input').length > 0) {
        $('.line_input').each(function () {
            activateLineSlider($(this), $(this).parent().find('.count_input'));
        })
    }

    $('.calculator_btn').click(function () {
        $('.results_block').each(function () {
            $(this).stop(true, true).slideDown();
        })
    });

    $('.calculator_block .list_calculator .tab_buttons li a').click(function (e) {
        e.preventDefault();
        $('.results_block').slideUp(300);
    });

    $('.consumer_btn').click(function (e) {
       $('.custom_field').removeClass('open_custom_field');
       $('.young_block_inner').removeClass('open_inner');
       $('.young_block label input').attr("checked", false);
    });

    $('.mortgage_btn').click(function (e) {
        $('.custom_field').addClass('open_custom_field');
    });
    $('.credit1_young_btn').click(function (e) {
        // e.preventDefault();
        $('.credit1_filed').toggleClass('open_inner');
    });
    $('.young_btn').click(function (e) {
       // e.preventDefault();
        $('.young_block_inner').toggleClass('open_inner');
    });
});