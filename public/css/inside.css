.inside_page .list_info_inner {
  padding: 0;
  margin: 0;
  list-style-type: none;
}

.inside_page .small_images .image_block a > img {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.inside_page .small_images .image_block a > img {
  object-fit: cover;
}

.inside_page .small_images .image_block a iframe {
  position: absolute;
  left: 0;
  top: 0;
  width: 100% !important;
  height: 100% !important;
}

.inside_page .small_images .image_block a {
  position: relative;
  overflow: hidden;
}
.inside_page .small_images .image_block a:before {
  display: block;
  content: "";
  padding-bottom: 100%;
}

.inside_page .small_images .slick-arrow:before, .inside_page .product_images .slick-arrow:before {
  font-family: "icon" !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.inside_page {
  margin-top: 20px;
}
.inside_page .product_images {
  display: flex;
  align-items: flex-start;
  flex: 0 0 100%;
  max-width: 100%;
  margin: 0 -10px 0;
}
.inside_page .product_images .slick-arrow {
  position: absolute;
  z-index: 5;
  border: none;
  padding: 0;
  background: transparent;
  color: #ffffff;
  font-size: 0;
}
.inside_page .product_images .slick-arrow:before {
  display: block;
  font-size: 2rem;
}
.inside_page .product_images .slick-prev {
  left: 25px;
  top: 50%;
}
.inside_page .product_images .slick-prev:before {
  content: "\e902";
}
.inside_page .product_images .slick-next {
  right: 25px;
  top: 50%;
}
.inside_page .product_images .slick-next:before {
  content: "\e903";
}
.inside_page .product_images .slick-disabled {
  opacity: 0 !important;
  pointer-events: none;
  visibility: hidden;
  cursor: default;
}
@media screen and (min-width: 991px) and (max-width: 767px) {
  .inside_page .product_images {
    flex-direction: column-reverse;
    justify-content: flex-end;
  }
}
@media screen and (max-width: 990px) {
  .inside_page .product_images {
    margin-bottom: 13px;
  }
}
@media screen and (max-width: 767px) {
  .inside_page .product_images {
    flex-direction: column-reverse;
    justify-content: flex-end;
  }
}
.inside_page .small_images {
  width: 280px;
  min-width: 280px;
  padding: 0 10px;
  position: relative;
  overflow: hidden;
  height: 513px;
}
.inside_page .small_images .slick-list {
  height: 513px !important;
}
.inside_page .small_images .image_block {
  padding: 6px 0;
}
.inside_page .small_images .image_block a {
  display: block;
  border: 2px solid transparent;
  opacity: 1;
}
.inside_page .small_images .image_block a:before {
  padding-bottom: 61%;
}
.inside_page .small_images .image_block a.selected {
  opacity: 0.6;
}
.inside_page .small_images .slick-arrow {
  position: absolute;
  z-index: 5;
  border: none;
  padding: 0;
  background: transparent;
  color: #ffffff;
  width: 100%;
  font-size: 0;
  left: 0;
}
.inside_page .small_images .slick-arrow:before {
  display: block;
  font-size: 1.3rem;
}
.inside_page .small_images .slick-prev {
  left: 0;
  top: 10px;
}
.inside_page .small_images .slick-prev:before {
  content: "\e901";
  transform: rotate(-180deg);
  -moz-transform: rotate(-180deg);
  -ms-transform: rotate(-180deg);
  -webkit-transform: rotate(-180deg);
  -o-transform: rotate(-180deg);
}
.inside_page .small_images .slick-next {
  right: 0;
  bottom: 10px;
  top: auto;
  left: 0;
}
.inside_page .small_images .slick-next:before {
  content: "\e901";
}
@media screen and (min-width: 991px) and (max-width: 767px) {
  .inside_page .small_images {
    width: 100%;
    height: auto;
    flex: none;
    margin-left: 0;
    padding: 0;
    margin-top: 20px;
    position: relative;
    white-space: nowrap;
    font-size: 0;
    overflow: hidden;
  }
  .inside_page .small_images .image_block {
    position: relative;
    white-space: nowrap;
    font-size: 0;
    overflow: hidden;
    padding: 10px;
    width: 33.3%;
    vertical-align: top;
  }
  .inside_page .small_images .image_block a {
    height: 100px;
  }
  .inside_page .small_images .slick-prev {
    left: 0;
    width: 20px;
    top: 10px;
    height: 100px;
  }
  .inside_page .small_images .slick-prev:before {
    transform: rotate(0);
    -moz-transform: rotate(0);
    -ms-transform: rotate(0);
    -webkit-transform: rotate(0);
    -o-transform: rotate(0);
  }
  .inside_page .small_images .slick-next {
    width: 20px;
    right: 0;
    height: 100px;
    bottom: 10px;
  }
  .inside_page .small_images .slick-next:before {
    transform: rotate(0);
    -moz-transform: rotate(0);
    -ms-transform: rotate(0);
    -webkit-transform: rotate(0);
    -o-transform: rotate(0);
  }
}
@media screen and (max-width: 767px) {
  .inside_page .small_images {
    width: 100%;
    height: auto;
    flex: none;
    margin-left: 0;
    padding: 0;
    margin-top: 20px;
    position: relative;
    white-space: nowrap;
    font-size: 0;
    overflow: hidden;
  }
  .inside_page .small_images .slick-list {
    height: auto !important;
  }
  .inside_page .small_images .slick-prev {
    right: auto;
    bottom: 50%;
    left: 10px;
    width: max-content;
    margin-top: 20px;
  }
  .inside_page .small_images .slick-prev:before {
    transform: unset;
    color: #8E342A;
    content: "\e902";
  }
  .inside_page .small_images .slick-next {
    right: 10px;
    bottom: 50%;
    left: auto;
    width: max-content;
  }
  .inside_page .small_images .slick-next:before {
    transform: unset;
    color: #8E342A;
    content: "\e903";
  }
}
.inside_page .big_images {
  flex: 1;
  width: 50%;
  position: relative;
  z-index: 5;
  padding: 0 10px;
  position: relative;
  white-space: nowrap;
  font-size: 0;
  overflow: hidden;
}
.inside_page .big_images .image_block {
  display: inline-block;
  white-space: normal;
  font-size: 1rem;
  width: 100%;
  vertical-align: top;
  display: inline-flex;
  height: 400px;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.inside_page .big_images .image_block img {
  display: block;
  max-width: 100%;
  max-height: 100%;
}
@media screen and (min-width: 991px) and (max-width: 767px) {
  .inside_page .big_images {
    width: 100%;
    flex: none;
  }
}
@media screen and (max-width: 767px) {
  .inside_page .big_images {
    width: 100%;
    flex: none;
  }
  .inside_page .big_images .image_block {
    height: auto;
  }
}
.inside_page .up_date {
  font-size: 130%;
  line-height: 17px;
  color: #666666;
}
.inside_page .price_block {
  font-size: 200%;
  line-height: 27px;
  color: #333333;
  font-family: "notosans-bold", "notosansarm-bold";
}
.inside_page .model_info {
  font-family: "notosans-bold", "notosansarm-bold";
}
.inside_page .list_block {
  display: flex;
  justify-content: space-between;
  border-top: 1px solid #C4C4C4;
  padding-top: 20px;
  font-size: 160%;
  line-height: 21px;
  color: #333333;
}
.inside_page .list_info_inner {
  display: flex;
  flex-wrap: wrap;
  margin: 20px -30px 0;
}
.inside_page .list_info_inner li {
  flex: 0 0 50%;
  max-width: 50%;
  padding: 0 30px;
  margin-bottom: 15px;
}
.inside_page .info_block > .page_row {
  margin-bottom: 10px;
}
.inside_page .call_block {
  font-size: 200%;
  line-height: 27px;
  color: #333333;
  font-family: "notosans-bold", "notosansarm-bold";
}
.inside_page .call_block a {
  color: #8E342A;
}
.inside_page .page_row {
  align-items: center;
}
.inside_page .page_row .up_date,
.inside_page .page_row .call_block {
  flex: 0 0 50%;
  max-width: 50%;
  padding: 0 10px 0 30px;
}
.inside_page .page_row .price_block,
.inside_page .page_row .page_title {
  flex: 0 0 50%;
  max-width: 50%;
  padding: 0 10px;
}
@media screen and (max-width: 990px) {
  .inside_page .list_info_inner {
    margin: 20px -10px 0;
  }
  .inside_page .list_info_inner li {
    padding: 0 10px;
  }
  .inside_page .page_row {
    align-items: center;
  }
  .inside_page .page_row .up_date,
.inside_page .page_row .call_block {
    padding: 0 10px;
  }
  .inside_page .list_block {
    font-size: 140%;
    line-height: 19px;
  }
  .inside_page .call_block {
    font-size: 180%;
    line-height: 23px;
  }
}
@media screen and (max-width: 767px) {
  .inside_page .call_block {
    font-size: 160%;
    line-height: 21px;
  }
  .inside_page .page_row .call_block,
.inside_page .page_row .up_date,
.inside_page .page_row .page_title {
    flex: 0 0 100%;
    max-width: 100%;
    padding: 0 10px;
  }
  .inside_page .list_info_inner li {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.web .inside_page .product_images .slick-arrow, .inside_page .product_images .web .slick-arrow {
  -o-transition: color 0.3s;
  -ms-transition: color 0.3s;
  -moz-transition: color 0.3s;
  -webkit-transition: color 0.3s;
  transition: color 0.3s;
}

.web .inside_page .product_images .slick-arrow:hover, .inside_page .product_images .web .slick-arrow:hover {
  color: #8E342A;
}

/*# sourceMappingURL=inside.css.map */
