.application_page .step_list, .application_page .list_application {
  padding: 0;
  margin: 0;
  list-style-type: none;
}

.application_page .select2-container--default.select2-container--open .select2-selection__arrow {
  transform: scaleY(-1);
  -moz-transform: scaleY(-1);
  -ms-transform: scaleY(-1);
  -webkit-transform: scaleY(-1);
  -o-transform: scaleY(-1);
}

.application_page .select2-container--default .select2-selection--single .select2-selection__arrow:before, .application_page .radio_group .sub_fields .radio_block .radio_button:before, .application_page .checkbox_block .checkbox_btn:before {
  font-family: "icon" !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.application_page {
  padding-top: 60px;
}
.application_page .application_container {
  max-width: 820px;
  margin: 0 auto;
  padding: 0 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.application_page .application_description {
  color: #333333;
  margin-top: 20px;
  text-align: center;
}
.application_page .block_description {
  color: #333333;
}
.application_page .block_description + .field_block {
  margin-top: 30px;
}
.application_page .list_application {
  margin-top: 40px;
  width: 100%;
  text-align: center;
}
.application_page .list_application li {
  margin-bottom: 20px;
}
.application_page .list_application li a {
  background: #F9F9F9;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 119px;
  padding: 10px;
  font-size: 160%;
  line-height: 22px;
  color: #333333;
  font-family: "notosans-bold", "notosansarm-bold";
  border: 1px solid inherit;
  -o-transition: all 0.3s;
  -ms-transition: all 0.3s;
  -moz-transition: all 0.3s;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}
.application_page .list_application li a:hover {
  border: 1px solid #91332A;
  background: #ffffff;
  color: #91332A;
  -o-transition: all 0.3s;
  -ms-transition: all 0.3s;
  -moz-transition: all 0.3s;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}
.application_page .inner_step {
  max-width: 620px;
  width: 100%;
  margin: 0 auto;
}
.application_page .steps_block {
  background: #ffffff;
  padding: 40px;
  width: 100%;
  margin-top: 40px;
}
.application_page .step_list {
  margin-bottom: 27px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.application_page .step_list li {
  flex: 1;
  display: flex;
  align-items: center;
  position: relative;
}
.application_page .step_list li a {
  width: 50px;
  height: 50px;
  position: relative;
  background: #ffffff;
  border: 2px solid #91332A;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #91332A;
  margin: 0;
  padding: 0;
  border-radius: 50%;
  font-family: "notosans-bold", "notosansarm-bold";
}
@media screen and (min-width: 1440px) {
  .application_page .step_list li a {
    font-size: 320%;
    line-height: 43px;
  }
}
@media screen and (min-width: 1200px) and (max-width: 1439px) {
  .application_page .step_list li a {
    font-size: 300%;
    line-height: 38px;
  }
}
@media screen and (min-width: 991px) and (max-width: 1199px) {
  .application_page .step_list li a {
    font-size: 250%;
    line-height: 35px;
  }
}
@media screen and (min-width: 768px) and (max-width: 990px) {
  .application_page .step_list li a {
    font-size: 200%;
    line-height: 29px;
  }
}
@media screen and (max-width: 767px) {
  .application_page .step_list li a {
    font-size: 180%;
    line-height: 27px;
  }
}
.application_page .step_list li:before {
  content: "";
  position: absolute;
  top: 26px;
  height: 1px;
  background: #91332A;
  width: 100%;
}
.application_page .step_list li:last-child {
  flex: inherit;
}
.application_page .step_list li:last-child:before {
  display: none;
}
.application_page .step_list li.active a {
  background: #8E342A;
  color: #ffffff;
}
.application_page .step_list li.active ~ li a {
  background: #DDDDDD;
  border: 1px solid #DDDDDD;
  color: #ffffff;
}
.application_page .step_list li.active ~ li:before {
  background: #DDDDDD;
}
.application_page .step_list li.active:before {
  background: #DDDDDD;
}
.application_page .form_fields {
  flex-direction: column;
  margin-bottom: 0;
}
.application_page .form_fields > .block_description:last-child,
.application_page .form_fields > .promo_block:last-child {
  margin-bottom: 30px;
}
.application_page .form_fields .field_block {
  max-width: 320px;
  padding: 0 10px;
}
.application_page .form_fields .block_description {
  padding: 0 10px;
  flex: 0 0 100%;
  max-width: 100%;
}
.application_page .fields_group {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.application_page .field_block {
  flex: inherit;
  width: 100%;
  max-width: 300px;
  margin-bottom: 30px;
  padding-left: 0;
  padding-right: 0;
}
.application_page .field_block input,
.application_page .field_block textarea {
  background: none;
}
.application_page .field_block input[data-transform=uppercase],
.application_page .field_block textarea[data-transform=uppercase] {
  text-transform: uppercase;
}
.application_page .field_block.full_field {
  max-width: 100%;
  flex: 0 0 100%;
}
.application_page .field_block .attach_block {
  margin-top: 10px;
}
.application_page .field_block .attach_label {
  display: block;
  position: relative;
  width: 100%;
  max-width: 247px;
}
.application_page .field_block .attach_label.disable {
  pointer-events: none;
}
.application_page .field_block .attach_label.disable .attach_btn {
  color: #DDDDDD;
}
.application_page .field_block .attach_btn {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  border: 1px solid;
  color: #C9977F;
  font-size: 130%;
  line-height: 22px;
  border-radius: 5px;
  font-family: "notosans-bold", "notosansarm-bold";
  height: 49px;
}
.application_page .field_block .attach_error {
  display: block;
  color: #b94a48;
  margin-top: 2px;
}
.application_page .field_block .file_block {
  display: flex;
  align-items: center;
  margin-top: 10px;
  justify-content: space-between;
  max-width: 247px;
}
.application_page .field_block .file_name {
  font-size: 130%;
  line-height: 16px;
  display: block;
  flex: 1;
  width: 50%;
  white-space: nowrap;
  text-overflow: ellipsis;
  position: relative;
  overflow: hidden;
}
.application_page .field_block .attach_remove {
  display: block;
  font-size: 100%;
  padding: 5px;
  cursor: pointer;
  margin-left: 15px;
  margin-right: -5px;
}
.application_page .field_block .attach_remove:before {
  display: block;
  font-weight: 900 !important;
}
.application_page .field_block .date_input {
  background-image: url("images/svg/calendar.svg");
  background-size: auto 100%;
  background-position: top right;
  background-repeat: no-repeat;
  padding-right: 50px;
}
@media screen and (max-width: 399px) {
  .application_page .field_block .date_input {
    background-image: none;
    padding-right: 10px;
  }
}
@media screen and (max-width: 359px) {
  .application_page .field_block .date_input::-webkit-input-placeholder {
    font-size: 85%;
  }
  .application_page .field_block .date_input:-moz-placeholder {
    font-size: 85%;
  }
  .application_page .field_block .date_input::-moz-placeholder {
    font-size: 85%;
  }
  .application_page .field_block .date_input:-ms-input-placeholder {
    font-size: 85%;
  }
}
.application_page .submission_type,
.application_page .postal_address,
.application_page .toggle_field {
  display: none;
}
.application_page .buttons_block {
    display: flex;
    margin: 0 -10px -20px;
    padding: 20px 0;
    background: #ffffff;
    position: sticky;
    bottom: 0;

}
.application_page .buttons_block button,
.application_page .buttons_block a {
  background: #ffffff;
  border: 1px solid #91332A;
  border-radius: 5px;
  padding: 13px;
  min-width: 247px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 10px;
  font-family: "notosans-bold", "notosansarm-bold";
  font-size: 130%;
  line-height: 22px;
  color: #91332A;
}
.application_page .buttons_block button.btn_next,
.application_page .buttons_block a.btn_next {
  background: #91332A;
  color: #ffffff;
  position: relative;
}
.application_page .buttons_block button.btn_next.loading,
.application_page .buttons_block a.btn_next.loading {
  pointer-events: none;
  color: transparent !important;
}
.application_page .buttons_block button.btn_next.loading:after,
.application_page .buttons_block a.btn_next.loading:after {
  color: #ffffff;
  position: absolute;
  top: 50%;
  left: 50%;
  margin: -4px -8px;
  border-radius: 50%;
  display: block;
  width: 8px;
  height: 8px;
  content: "";
  animation: loadAnimation 1.6s infinite linear;
}
.application_page .buttons_block button.disabled,
.application_page .buttons_block a.disabled {
  background: #e5e5e5;
  pointer-events: none;
  border-color: #e5e5e5;
}
.application_page .promo_block {
  font-size: 160%;
  line-height: 22px;
  color: #333333;
}
.application_page .promo_block a {
  text-decoration: underline;
}
.application_page .promo_block + .field_block {
  margin-top: 30px;
}
.application_page .form_fields > .promo_block {
  padding: 0 10px;
}
.application_page .info_form .field_block {
  max-width: 100%;
  padding: 0 15px;
}
.application_page .info_form label {
  position: relative;
  overflow: hidden;
  vertical-align: top;
  margin: 0;
}
.application_page .info_form label input {
  max-width: 100%;
}
.application_page .info_form label input:checked + .radio_button {
  color: #666666 !important;
  cursor: default;
}
.application_page .info_form label input:checked + .radio_button:before {
  background: #ffffff;
  border-color: #8E342A;
}
.application_page .info_form label input:checked + .radio_button:after {
  opacity: 1;
}
.application_page .info_form label input:checked + .radio_button.no_selected {
  color: #000000 !important;
}
.application_page .info_form label input:checked + .radio_button.no_selected:before {
  background: none;
}
.application_page .info_form label input:checked + .radio_button.no_selected:after {
  opacity: 0;
}
.application_page .info_form label .radio_button {
  display: flex;
  position: relative;
  font-size: 140%;
  line-height: 16px;
  cursor: pointer;
}
.application_page .info_form label .radio_button > span {
  margin-left: 2px;
  color: #C4C4C4;
}
.application_page .info_form label .radio_button:before {
  display: block;
  content: "";
  margin-right: 10px;
  width: 20px;
  min-width: 20px;
  height: 20px;
  -o-transition: border-color 0.3s;
  -ms-transition: border-color 0.3s;
  -moz-transition: border-color 0.3s;
  -webkit-transition: border-color 0.3s;
  transition: border-color 0.3s;
  border: 1px solid #666666;
  border-radius: 50%;
}
.application_page .info_form label .radio_button:after {
  display: block;
  content: "";
  position: absolute;
  width: 12px;
  height: 12px;
  left: 4px;
  margin-top: 4px;
  border-radius: 50%;
  background: #8E342A;
  opacity: 0;
}
.application_page .checkbox_block {
  margin-top: 30px;
  position: relative;
}
.application_page .checkbox_block .error_hint {
  position: absolute;
  left: 0;
  bottom: 100%;
  margin-bottom: -5px;
}
.application_page .checkbox_block > label {
  display: inline-block;
  vertical-align: top;
  font-weight: normal;
  -o-transition: margin 0.3s;
  -ms-transition: margin 0.3s;
  -moz-transition: margin 0.3s;
  -webkit-transition: margin 0.3s;
  transition: margin 0.3s;
}
.application_page .checkbox_block > label.has-error {
  margin-top: 20px;
}
.application_page .checkbox_block > label input:checked + .checkbox_btn:before {
  border-color: transparent;
  background: #8E342A;
  font-size: 1rem;
}
.application_page .checkbox_block .checkbox_btn {
  display: flex;
  font-size: 140%;
  line-height: 20px;
  align-items: flex-start;
  cursor: pointer;
}
.application_page .checkbox_block .checkbox_btn:before {
  min-width: 20px;
  height: 20px;
  margin-right: 10px;
  border: 1px solid #C4C4C4;
  content: "\e905";
  border-radius: 3px;
  font-size: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
}
.application_page .radio_group {
  padding: 0 10px 20px;
  position: relative;
  margin-bottom: 30px;
}
.application_page .radio_group .radio_block label {
  display: inline-block;
  vertical-align: top;
  font-weight: normal;
}
.application_page .radio_group .radio_button {
  display: flex;
  font-size: 140%;
  line-height: 20px;
  align-items: flex-start;
  cursor: pointer;
}
.application_page .radio_group .radio_button:before {
  min-width: 20px;
  height: 20px;
  margin-right: 10px;
}
.application_page .radio_group > .radio_block:not(:first-child) {
  margin-top: 30px;
}
.application_page .radio_group > .radio_block > label {
  -o-transition: margin 0.3s;
  -ms-transition: margin 0.3s;
  -moz-transition: margin 0.3s;
  -webkit-transition: margin 0.3s;
  transition: margin 0.3s;
}
.application_page .radio_group > .radio_block > label > .radio_button:before {
  display: block;
  content: "";
  border-radius: 50%;
  box-shadow: 0 0 0 1px #C4C4C4;
}
.application_page .radio_group > .radio_block > label input:checked + .radio_button {
  pointer-events: none;
}
.application_page .radio_group > .radio_block > label input:checked + .radio_button:before {
  background: #8E342A;
  box-shadow: 0 0 0 1px #8E342A;
  border: 4px solid #ffffff;
}
.application_page .radio_group > .radio_block > label.has-error {
  margin-top: 20px;
}
.application_page .radio_group > .radio_block > .error_hint {
  position: absolute;
  top: -5px;
  left: 10px;
  font-size: 120%;
}
.application_page .radio_group > .radio_block > .sub_fields {
  padding-left: 40px;
}
@media screen and (max-width: 575px) {
  .application_page .radio_group > .radio_block > .sub_fields {
    padding-left: 30px;
  }
}
.application_page .radio_group .sub_fields {
  display: none;
  position: relative;
}
.application_page .radio_group .sub_fields .radio_block {
  padding-top: 20px;
  position: relative;
}
.application_page .radio_group .sub_fields .radio_block > .error_hint {
  position: absolute;
  top: auto;
  left: 0;
  bottom: 100%;
  margin-bottom: -35px;
}
.application_page .radio_group .sub_fields .radio_block > label {
  -o-transition: margin 0.3s;
  -ms-transition: margin 0.3s;
  -moz-transition: margin 0.3s;
  -webkit-transition: margin 0.3s;
  transition: margin 0.3s;
}
.application_page .radio_group .sub_fields .radio_block > label.has-error {
  margin-top: 20px;
}
.application_page .radio_group .sub_fields .radio_block .radio_button:before {
  border: 1px solid #C4C4C4;
  content: "\e905";
  border-radius: 3px;
  font-size: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
}
.application_page .radio_group .sub_fields .radio_block input:checked + .radio_button {
  pointer-events: none;
}
.application_page .radio_group .sub_fields .radio_block input:checked + .radio_button:before {
  border-color: transparent;
  background: #8E342A;
  font-size: 1rem;
}
.application_page .radio_group .sub_fields .field_block {
  padding: 20px 0 0;
  margin-bottom: 0;
}
.application_page .radio_group .sub_fields .field_block:not(.full_field) {
  max-width: 300px;
}
.application_page .select2-container .select2-selection--single {
  height: auto;
}
.application_page .select2-container--default .select2-selection--single {
  background-color: transparent;
  border: 1px solid #C4C4C4;
  border-radius: 4px;
}
.application_page .select2-container--default .select2-selection--single .select2-selection__rendered {
  color: #333333;
  font-size: 130%;
  line-height: 16px;
  padding: 15px 30px 15px 14px;
}
.application_page .select2-container--default .select2-selection--single .select2-selection__arrow {
  height: auto;
  bottom: 1px;
  width: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  right: 10px;
}
.application_page .select2-container--default .select2-selection--single .select2-selection__arrow * {
  display: none;
}
.application_page .select2-container--default .select2-selection--single .select2-selection__arrow:before {
  content: "\e901";
  font-size: 10px;
  display: block;
}
.application_page .select2-container--default.select2-container--open .select2-selection--single {
  border-bottom-right-radius: 4px !important;
  border-bottom-left-radius: 4px !important;
}
.application_page .select2-container {
  width: 100% !important;
}
@media screen and (min-width: 576px) {
  .application_page .daterangepicker {
    white-space: nowrap;
  }
  .application_page .daterangepicker .drp-calendar {
    display: inline-block;
    vertical-align: top;
    float: none;
  }
}
@media screen and (max-width: 399px) {
  .application_page .daterangepicker {
    left: auto !important;
    right: 0 !important;
    max-width: 260px;
  }
}
.application_page .daterangepicker .drp-buttons .btn {
  border: none;
  background: #8E342A;
  color: #ffffff;
  padding: 8px 15px;
}
.application_page .daterangepicker .drp-selected {
  display: none !important;
}
.application_page .daterangepicker td.active,
.application_page .daterangepicker td.active {
  background: #8E342A;
}
.application_page .daterangepicker td.in-range:not(.active) {
  background: rgba(142, 52, 42, 0.1);
}
@media screen and (max-width: 767px) {
  .application_page {
    padding-top: 20px;
  }
  .application_page .promo_block {
    font-size: 140%;
    line-height: 20px;
  }
  .application_page .list_application {
    margin-top: 20px;
  }
  .application_page .list_application li a {
    min-height: 70px;
    font-size: 140%;
    line-height: 22px;
  }
  .application_page .steps_block {
    padding: 25px;
  }
  .application_page .form_fields {
    margin: 20px -10px 15px;
  }
  .application_page .buttons_block button,
.application_page .buttons_block a {
    min-width: inherit;
    flex: 1;
  }
}

.footer .upload_text {
  display: none;
}

.select2-dropdown {
  border: 1px solid #C4C4C4;
  font-size: 120%;
  line-height: 15px;
  box-shadow: none;
}

.select2-results__options {
  overflow-x: hidden;
  scrollbar-color: #8E342A #f2f2f2;
  scrollbar-width: thin;
}
.select2-results__options::-webkit-scrollbar {
  width: 6px;
  height: auto;
  border-radius: 5px;
  background: #f2f2f2;
}
.select2-results__options::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background: #8E342A;
}
.select2-results__options li {
  padding: 7px 10px;
}

.select2-results__option--highlighted.select2-results__option--selectable:not(.select2-results__option--selected) {
  background: transparent !important;
  color: #8E342A !important;
}
.select2-results__option--selected {
  background: #8E342A !important;
  color: #ffffff !important;
  cursor: default;
}

.response_error {
  font-size: 160%;
  line-height: 21px;
  color: #b94a48;
  font-family: "notosans-bold", "notosansarm-bold";
  padding-bottom: 10px;
}
.response_error:empty {
  display: none;
}
@media screen and (max-width: 990px) {
  .response_error {
    font-size: 140%;
    line-height: 19px;
  }
}

@keyframes loadAnimation {
  9% {
    box-shadow: none;
  }
  10% {
    box-shadow: -8px 0 0;
  }
  35% {
    box-shadow: -8px 0 0;
  }
  36% {
    box-shadow: -8px 0 0, 8px 0 0;
  }
  60% {
    box-shadow: -8px 0 0, 8px 0 0;
  }
  61% {
    box-shadow: -8px 0 0, 8px 0 0, 24px 0 0;
  }
  85% {
    box-shadow: -8px 0 0, 8px 0 0, 24px 0 0;
  }
  86% {
    box-shadow: none;
  }
}
.result_block {
  display: flex;
  text-align: center;
  flex-direction: column;
  align-items: center;
}
.result_block .buttons_block {
  margin-top: 40px;
}
@media screen and (max-width: 767px) {
  .result_block .buttons_block {
    margin-top: 30px;
  }
}

.web .application_page .buttons_block button.btn_next, .application_page .buttons_block .web button.btn_next,
.web .application_page .buttons_block a.btn_next,
.application_page .buttons_block .web a.btn_next, .web .application_page .promo_block a, .application_page .promo_block .web a, .web .application_page .daterangepicker .drp-buttons .btn, .application_page .daterangepicker .drp-buttons .web .btn {
  -o-transition: opacity 0.3s;
  -ms-transition: opacity 0.3s;
  -moz-transition: opacity 0.3s;
  -webkit-transition: opacity 0.3s;
  transition: opacity 0.3s;
}

.web .application_page .field_block .attach_remove, .application_page .field_block .web .attach_remove, .web .application_page .checkbox_block .checkbox_btn, .application_page .checkbox_block .web .checkbox_btn, .web .application_page .radio_group .radio_button, .application_page .radio_group .web .radio_button {
  -o-transition: color 0.3s;
  -ms-transition: color 0.3s;
  -moz-transition: color 0.3s;
  -webkit-transition: color 0.3s;
  transition: color 0.3s;
}

.web .application_page .buttons_block button, .application_page .buttons_block .web button,
.web .application_page .buttons_block a,
.application_page .buttons_block .web a {
  -o-transition: background-color 0.3s;
  -ms-transition: background-color 0.3s;
  -moz-transition: background-color 0.3s;
  -webkit-transition: background-color 0.3s;
  transition: background-color 0.3s;
}

.select2-results__options li {
  -o-transition: all 0.3s;
  -ms-transition: all 0.3s;
  -moz-transition: all 0.3s;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.touch .application_page .buttons_block button.btn_next, .application_page .buttons_block .touch button.btn_next,
.touch .application_page .buttons_block a.btn_next,
.application_page .buttons_block .touch a.btn_next, .touch .application_page .promo_block a, .application_page .promo_block .touch a, .touch .application_page .daterangepicker .drp-buttons .btn, .application_page .daterangepicker .drp-buttons .touch .btn {
  -o-transition: opacity 0.15s;
  -ms-transition: opacity 0.15s;
  -moz-transition: opacity 0.15s;
  -webkit-transition: opacity 0.15s;
  transition: opacity 0.15s;
}

.touch .application_page .field_block .attach_remove, .application_page .field_block .touch .attach_remove, .touch .application_page .checkbox_block .checkbox_btn, .application_page .checkbox_block .touch .checkbox_btn, .touch .application_page .radio_group .radio_button, .application_page .radio_group .touch .radio_button {
  -o-transition: color 0.15s;
  -ms-transition: color 0.15s;
  -moz-transition: color 0.15s;
  -webkit-transition: color 0.15s;
  transition: color 0.15s;
}

.touch .application_page .buttons_block button, .application_page .buttons_block .touch button,
.touch .application_page .buttons_block a,
.application_page .buttons_block .touch a {
  -o-transition: background-color 0.15s;
  -ms-transition: background-color 0.15s;
  -moz-transition: background-color 0.15s;
  -webkit-transition: background-color 0.15s;
  transition: background-color 0.15s;
}

.application_page .field_block .attach_btn, .application_page .field_block .attach_remove, .application_page .buttons_block button.btn_next.loading,
.application_page .buttons_block a.btn_next.loading, .application_page .checkbox_block .checkbox_btn {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.application_page .field_block .attach_label[hidden],
.application_page .field_block .attach_label input, .application_page .info_form label input, .application_page .checkbox_block > label input, .application_page .radio_group input[type=radio] {
  position: absolute;
  width: 0;
  height: 0;
  top: 0;
  left: 0;
  visibility: hidden;
}

.web .application_page .field_block .attach_remove:hover, .application_page .field_block .web .attach_remove:hover, .web .application_page .checkbox_block .checkbox_btn:hover, .application_page .checkbox_block .web .checkbox_btn:hover, .web .application_page .radio_group .radio_button:hover, .application_page .radio_group .web .radio_button:hover {
  color: #8E342A;
}
.web .application_page .buttons_block button:hover, .application_page .buttons_block .web button:hover,
.web .application_page .buttons_block a:hover,
.application_page .buttons_block .web a:hover {
  background: #8E342A;
  color: #ffffff;
}
.web .application_page .buttons_block button.btn_next:hover, .application_page .buttons_block .web button.btn_next:hover,
.web .application_page .buttons_block a.btn_next:hover,
.application_page .buttons_block .web a.btn_next:hover, .web .application_page .promo_block a:hover, .application_page .promo_block .web a:hover, .web .application_page .daterangepicker .drp-buttons .btn:hover, .application_page .daterangepicker .drp-buttons .web .btn:hover {
  opacity: 0.7;
}
.touch .application_page .field_block .attach_remove:active, .application_page .field_block .touch .attach_remove:active, .touch .application_page .checkbox_block .checkbox_btn:active, .application_page .checkbox_block .touch .checkbox_btn:active, .touch .application_page .radio_group .radio_button:active, .application_page .radio_group .touch .radio_button:active {
  color: #8E342A;
}
.touch .application_page .buttons_block button:active, .application_page .buttons_block .touch button:active,
.touch .application_page .buttons_block a:active,
.application_page .buttons_block .touch a:active {
  background: #8E342A;
}
.touch .application_page .buttons_block button.btn_next:active, .application_page .buttons_block .touch button.btn_next:active,
.touch .application_page .buttons_block a.btn_next:active,
.application_page .buttons_block .touch a.btn_next:active, .touch .application_page .promo_block a:active, .application_page .promo_block .touch a:active, .touch .application_page .daterangepicker .drp-buttons .btn:active, .application_page .daterangepicker .drp-buttons .touch .btn:active {
  opacity: 0.7;
}

/*# sourceMappingURL=application.css.map */
