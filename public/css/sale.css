.sale_page .sale_list {
  padding: 0;
  margin: 0;
  list-style-type: none;
}

.sale_page .sale_image img {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.sale_page .sale_block.combo_hover img {
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  transform-style: preserve-3d;
  -webkit-transform-style: preserve-3d;
}

.sale_page {
  margin-top: 20px;
}
.sale_page .sale_inner {
  margin-top: 30px;
}
.sale_page .sale_block.hovered .sale_title {
  color: #8E342A;
}
.sale_page .sale_info {
  display: flex;
  align-items: baseline;
  margin: 15px 0 5px;
}
.sale_page .price_block {
  font-family: "notosans-bold", "notosansarm-bold";
  font-size: 160%;
  line-height: 21px;
  color: #333333;
}
.sale_page .news_date {
  font-size: 130%;
  line-height: 17px;
  color: #666666;
  margin-left: auto;
  display: block;
}
.sale_page .sale_title {
  font-size: 160%;
  line-height: 21px;
  font-family: "notosans-bold", "notosansarm-bold";
  color: #333333;
  display: block;
  max-width: 300px;
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
.sale_page .view_more {
  color: #8E342A;
  font-size: 160%;
  line-height: 21px;
  margin-top: 10px;
  display: block;
}
.sale_page .sale_list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10px;
}
.sale_page .sale_list li {
  flex: 0 0 33.333333%;
  max-width: 33.333333%;
  padding: 0 10px;
  margin-bottom: 60px;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  transform-style: preserve-3d;
  -webkit-transform-style: preserve-3d;
  opacity: 0;
}
.sale_page .sale_image {
  position: relative;
  overflow: hidden;
  border-radius: 10px;
  padding-bottom: 58%;
  display: block;
}
.sale_page .sale_image img {
  object-fit: cover;
  border-radius: 10px;
}
@media screen and (max-width: 1199px) {
  .sale_page .price_block,
.sale_page .view_more,
.sale_page .sale_title {
    font-size: 140%;
    line-height: 19px;
  }
}
@media screen and (max-width: 990px) {
  .sale_page .sale_info {
    flex-direction: column-reverse;
    margin: 8px 0 5px;
  }
  .sale_page .news_date {
    margin-left: unset;
    margin-bottom: 5px;
  }
  .sale_page .sale_list li {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .sale_page .sale_inner {
    margin-top: 15px;
  }
}
@media screen and (max-width: 479px) {
  .sale_page .sale_list li {
    flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: 20px;
  }
}

.web .sale_page .view_more, .sale_page .web .view_more {
  -o-transition: opacity 0.3s;
  -ms-transition: opacity 0.3s;
  -moz-transition: opacity 0.3s;
  -webkit-transition: opacity 0.3s;
  transition: opacity 0.3s;
}

.web .sale_page .view_more:hover, .sale_page .web .view_more:hover {
  opacity: 0.7;
}
.sale_page .sale_block.combo_hover .combo_link img {
  -o-transition: transform 0.3s;
  -ms-transition: transform 0.3s;
  -moz-transition: transform 0.3s;
  -webkit-transition: transform 0.3s;
  transition: transform 0.3s;
}
.sale_page .hovered.sale_block.combo_hover .combo_link img {
  transform: scale(1.2);
  -moz-transform: scale(1.2);
  -ms-transform: scale(1.2);
  -webkit-transform: scale(1.2);
  -o-transform: scale(1.2);
}

/*# sourceMappingURL=sale.css.map */
