/*
Copyright (c) 2003-2012, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'codemirror', 'nl', {
	toolbar: 'Broncode',
	searchCode: '<PERSON><PERSON> in broncode',
	autoFormat: 'Formatteer selectie',
	commentSelectedRange: 'Zet selectie in commentaar',
	uncommentSelectedRange: 'Haal selectie uit commentaar',
	autoCompleteToggle: 'Zet automatisch aanvullen van HTML tags aan/uit'
});
