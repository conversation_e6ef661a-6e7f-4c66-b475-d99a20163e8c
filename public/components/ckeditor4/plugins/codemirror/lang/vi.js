/*
Copyright (c) 2003-2012, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'codemirror', 'vi', {
	toolbar: 'Mã HTML',
	searchCode: 'Search Source',
	autoFormat: 'Format Selection',
	commentSelectedRange: 'Comment Selection',
	uncommentSelectedRange: 'Uncomment Selection',
	autoCompleteToggle: 'Enable/Disable HTML Tag Autocomplete'
});
