!function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define("mode/xml/xml",["../../lib/codemirror"],e):e(CodeMirror)}(function(e){"use strict";var t={autoSelfClosers:{area:!0,base:!0,br:!0,col:!0,command:!0,embed:!0,frame:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0,menuitem:!0},implicitlyClosed:{dd:!0,li:!0,optgroup:!0,option:!0,p:!0,rp:!0,rt:!0,tbody:!0,td:!0,tfoot:!0,th:!0,tr:!0},contextGrabbers:{dd:{dd:!0,dt:!0},dt:{dd:!0,dt:!0},li:{li:!0},option:{option:!0,optgroup:!0},optgroup:{optgroup:!0},p:{address:!0,article:!0,aside:!0,blockquote:!0,dir:!0,div:!0,dl:!0,fieldset:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,menu:!0,nav:!0,ol:!0,p:!0,pre:!0,section:!0,table:!0,ul:!0},rp:{rp:!0,rt:!0},rt:{rp:!0,rt:!0},tbody:{tbody:!0,tfoot:!0},td:{td:!0,th:!0},tfoot:{tbody:!0},th:{td:!0,th:!0},thead:{tbody:!0,tfoot:!0},tr:{tr:!0}},doNotIndent:{pre:!0},allowUnquoted:!0,allowMissing:!0,caseFold:!0},r={autoSelfClosers:{},implicitlyClosed:{},contextGrabbers:{},doNotIndent:{},allowUnquoted:!1,allowMissing:!1,allowMissingTagName:!1,caseFold:!1};e.defineMode("xml",function(n,o){function a(e,t){function r(r){return t.tokenize=r,r(e,t)}var n=e.next();if("<"==n)return e.eat("!")?e.eat("[")?e.match("CDATA[")?r(s("atom","]]>")):null:e.match("--")?r(s("comment","--\x3e")):e.match("DOCTYPE",!0,!0)?(e.eatWhile(/[\w\._\-]/),r(c(1))):null:e.eat("?")?(e.eatWhile(/[\w\._\-]/),t.tokenize=s("meta","?>"),"meta"):(S=e.eat("/")?"closeTag":"openTag",t.tokenize=i,"tag bracket");if("&"==n){var o;return o=e.eat("#")?e.eat("x")?e.eatWhile(/[a-fA-F\d]/)&&e.eat(";"):e.eatWhile(/[\d]/)&&e.eat(";"):e.eatWhile(/[\w\.\-:]/)&&e.eat(";"),o?"atom":"error"}return e.eatWhile(/[^&<]/),null}function i(e,t){var r=e.next();if(">"==r||"/"==r&&e.eat(">"))return t.tokenize=a,S=">"==r?"endTag":"selfcloseTag","tag bracket";if("="==r)return S="equals",null;if("<"==r){t.tokenize=a,t.state=m,t.tagName=t.tagStart=null;var n=t.tokenize(e,t);return n?n+" tag error":"tag error"}return/[\'\"]/.test(r)?(t.tokenize=l(r),t.stringStartCol=e.column(),t.tokenize(e,t)):(e.match(/^[^\s\u00a0=<>\"\']*[^\s\u00a0=<>\"\'\/]/),"word")}function l(e){var t=function(t,r){for(;!t.eol();)if(t.next()==e){r.tokenize=i;break}return"string"};return t.isInAttribute=!0,t}function s(e,t){return function(r,n){for(;!r.eol();){if(r.match(t)){n.tokenize=a;break}r.next()}return e}}function c(e){return function(t,r){for(var n;null!=(n=t.next());){if("<"==n)return r.tokenize=c(e+1),r.tokenize(t,r);if(">"==n){if(1==e){r.tokenize=a;break}return r.tokenize=c(e-1),r.tokenize(t,r)}}return"meta"}}function d(e,t,r){this.prev=e.context,this.tagName=t,this.indent=e.indented,this.startOfLine=r,(M.doNotIndent.hasOwnProperty(t)||e.context&&e.context.noIndent)&&(this.noIndent=!0)}function u(e){e.context&&(e.context=e.context.prev)}function p(e,t){for(var r;;){if(!e.context)return;if(r=e.context.tagName,!M.contextGrabbers.hasOwnProperty(r)||!M.contextGrabbers[r].hasOwnProperty(t))return;u(e)}}function m(e,t,r){return"openTag"==e?(r.tagStart=t.column(),f):"closeTag"==e?h:m}function f(e,t,r){return"word"==e?(r.tagName=t.current(),T="tag",y):M.allowMissingTagName&&"endTag"==e?(T="tag bracket",y(e,t,r)):(T="error",f)}function h(e,t,r){if("word"==e){var n=t.current();return r.context&&r.context.tagName!=n&&M.implicitlyClosed.hasOwnProperty(r.context.tagName)&&u(r),r.context&&r.context.tagName==n||!1===M.matchClosing?(T="tag",g):(T="tag error",b)}return M.allowMissingTagName&&"endTag"==e?(T="tag bracket",g(e,t,r)):(T="error",b)}function g(e,t,r){return"endTag"!=e?(T="error",g):(u(r),m)}function b(e,t,r){return T="error",g(e,t,r)}function y(e,t,r){if("word"==e)return T="attribute",v;if("endTag"==e||"selfcloseTag"==e){var n=r.tagName,o=r.tagStart;return r.tagName=r.tagStart=null,"selfcloseTag"==e||M.autoSelfClosers.hasOwnProperty(n)?p(r,n):(p(r,n),r.context=new d(r,n,o==r.indented)),m}return T="error",y}function v(e,t,r){return"equals"==e?k:(M.allowMissing||(T="error"),y(e,t,r))}function k(e,t,r){return"string"==e?w:"word"==e&&M.allowUnquoted?(T="string",y):(T="error",y(e,t,r))}function w(e,t,r){return"string"==e?w:y(e,t,r)}var x=n.indentUnit,M={},z=o.htmlMode?t:r;for(var j in z)M[j]=z[j];for(var j in o)M[j]=o[j];var S,T;return a.isInText=!0,{startState:function(e){var t={tokenize:a,state:m,indented:e||0,tagName:null,tagStart:null,context:null};return null!=e&&(t.baseIndent=e),t},token:function(e,t){if(!t.tagName&&e.sol()&&(t.indented=e.indentation()),e.eatSpace())return null;S=null;var r=t.tokenize(e,t);return(r||S)&&"comment"!=r&&(T=null,t.state=t.state(S||r,e,t),T&&(r="error"==T?r+" error":T)),r},indent:function(t,r,n){var o=t.context;if(t.tokenize.isInAttribute)return t.tagStart==t.indented?t.stringStartCol+1:t.indented+x;if(o&&o.noIndent)return e.Pass;if(t.tokenize!=i&&t.tokenize!=a)return n?n.match(/^(\s*)/)[0].length:0;if(t.tagName)return!1!==M.multilineTagIndentPastTag?t.tagStart+t.tagName.length+2:t.tagStart+x*(M.multilineTagIndentFactor||1);if(M.alignCDATA&&/<!\[CDATA\[/.test(r))return 0;var l=r&&/^<(\/)?([\w_:\.-]*)/.exec(r);if(l&&l[1])for(;o;){if(o.tagName==l[2]){o=o.prev;break}if(!M.implicitlyClosed.hasOwnProperty(o.tagName))break;o=o.prev}else if(l)for(;o;){var s=M.contextGrabbers[o.tagName];if(!s||!s.hasOwnProperty(l[2]))break;o=o.prev}for(;o&&o.prev&&!o.startOfLine;)o=o.prev;return o?o.indent+x:t.baseIndent||0},electricInput:/<\/[\s\w:]+>$/,blockCommentStart:"\x3c!--",blockCommentEnd:"--\x3e",configuration:M.htmlMode?"html":"xml",helperType:M.htmlMode?"html":"xml",skipAttribute:function(e){e.state==k&&(e.state=y)}}}),e.defineMIME("text/xml","xml"),e.defineMIME("application/xml","xml"),e.mimeModes.hasOwnProperty("text/html")||e.defineMIME("text/html",{name:"xml",htmlMode:!0})}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define("mode/javascript/javascript",["../../lib/codemirror"],e):e(CodeMirror)}(function(e){"use strict";e.defineMode("javascript",function(t,r){function n(e){for(var t,r=!1,n=!1;null!=(t=e.next());){if(!r){if("/"==t&&!n)return;"["==t?n=!0:n&&"]"==t&&(n=!1)}r=!r&&"\\"==t}}function o(e,t,r){return Be=e,_e=r,t}function a(e,t){var r=e.next();if('"'==r||"'"==r)return t.tokenize=i(r),t.tokenize(e,t);if("."==r&&e.match(/^\d+(?:[eE][+\-]?\d+)?/))return o("number","number");if("."==r&&e.match(".."))return o("spread","meta");if(/[\[\]{}\(\),;\:\.]/.test(r))return o(r);if("="==r&&e.eat(">"))return o("=>","operator");if("0"==r&&e.match(/^(?:x[\da-f]+|o[0-7]+|b[01]+)n?/i))return o("number","number");if(/\d/.test(r))return e.match(/^\d*(?:n|(?:\.\d*)?(?:[eE][+\-]?\d+)?)?/),o("number","number");if("/"==r)return e.eat("*")?(t.tokenize=l,l(e,t)):e.eat("/")?(e.skipToEnd(),o("comment","comment")):Ve(e,t,1)?(n(e),e.match(/^\b(([gimyus])(?![gimyus]*\2))+\b/),o("regexp","string-2")):(e.eat("="),o("operator","operator",e.current()));if("`"==r)return t.tokenize=s,s(e,t);if("#"==r)return e.skipToEnd(),o("error","error");if(Ye.test(r))return">"==r&&t.lexical&&">"==t.lexical.type||(e.eat("=")?"!"!=r&&"="!=r||e.eat("="):/[<>*+\-]/.test(r)&&(e.eat(r),">"==r&&e.eat(r))),o("operator","operator",e.current());if(He.test(r)){e.eatWhile(He);var a=e.current();if("."!=t.lastType){if(Ge.propertyIsEnumerable(a)){var c=Ge[a];return o(c.type,c.style,a)}if("async"==a&&e.match(/^(\s|\/\*.*?\*\/)*[\[\(\w]/,!1))return o("async","keyword",a)}return o("variable","variable",a)}}function i(e){return function(t,r){var n,i=!1;if(Fe&&"@"==t.peek()&&t.match(Re))return r.tokenize=a,o("jsonld-keyword","meta");for(;null!=(n=t.next())&&(n!=e||i);)i=!i&&"\\"==n;return i||(r.tokenize=a),o("string","string")}}function l(e,t){for(var r,n=!1;r=e.next();){if("/"==r&&n){t.tokenize=a;break}n="*"==r}return o("comment","comment")}function s(e,t){for(var r,n=!1;null!=(r=e.next());){if(!n&&("`"==r||"$"==r&&e.eat("{"))){t.tokenize=a;break}n=!n&&"\\"==r}return o("quasi","string-2",e.current())}function c(e,t){t.fatArrowAt&&(t.fatArrowAt=null);var r=e.string.indexOf("=>",e.start);if(!(r<0)){if(Ue){var n=/:\s*(?:\w+(?:<[^>]*>|\[\])?|\{[^}]*\})\s*$/.exec(e.string.slice(e.start,r));n&&(r=n.index)}for(var o=0,a=!1,i=r-1;i>=0;--i){var l=e.string.charAt(i),s=Xe.indexOf(l);if(s>=0&&s<3){if(!o){++i;break}if(0==--o){"("==l&&(a=!0);break}}else if(s>=3&&s<6)++o;else if(He.test(l))a=!0;else{if(/["'\/]/.test(l))return;if(a&&!o){++i;break}}}a&&!o&&(t.fatArrowAt=i)}}function d(e,t,r,n,o,a){this.indented=e,this.column=t,this.type=r,this.prev=o,this.info=a,null!=n&&(this.align=n)}function u(e,t){for(var r=e.localVars;r;r=r.next)if(r.name==t)return!0;for(var n=e.context;n;n=n.prev)for(var r=n.vars;r;r=r.next)if(r.name==t)return!0}function p(e,t,r,n,o){var a=e.cc;for(Je.state=e,Je.stream=o,Je.marked=null,Je.cc=a,Je.style=t,e.lexical.hasOwnProperty("align")||(e.lexical.align=!0);;){if((a.length?a.pop():De?C:T)(r,n)){for(;a.length&&a[a.length-1].lex;)a.pop()();return Je.marked?Je.marked:"variable"==r&&u(e,n)?"variable-2":t}}}function m(){for(var e=arguments.length-1;e>=0;e--)Je.cc.push(arguments[e])}function f(){return m.apply(null,arguments),!0}function h(e,t){for(var r=t;r;r=r.next)if(r.name==e)return!0;return!1}function g(e){var t=Je.state;if(Je.marked="def",t.context)if("var"==t.lexical.info&&t.context&&t.context.block){var n=b(e,t.context);if(null!=n)return void(t.context=n)}else if(!h(e,t.localVars))return void(t.localVars=new k(e,t.localVars));r.globalVars&&!h(e,t.globalVars)&&(t.globalVars=new k(e,t.globalVars))}function b(e,t){if(t){if(t.block){var r=b(e,t.prev);return r?r==t.prev?t:new v(r,t.vars,!0):null}return h(e,t.vars)?t:new v(t.prev,new k(e,t.vars),!1)}return null}function y(e){return"public"==e||"private"==e||"protected"==e||"abstract"==e||"readonly"==e}function v(e,t,r){this.prev=e,this.vars=t,this.block=r}function k(e,t){this.name=e,this.next=t}function w(){Je.state.context=new v(Je.state.context,Je.state.localVars,!1),Je.state.localVars=Qe}function x(){Je.state.context=new v(Je.state.context,Je.state.localVars,!0),Je.state.localVars=null}function M(){Je.state.localVars=Je.state.context.vars,Je.state.context=Je.state.context.prev}function z(e,t){var r=function(){var r=Je.state,n=r.indented;if("stat"==r.lexical.type)n=r.lexical.indented;else for(var o=r.lexical;o&&")"==o.type&&o.align;o=o.prev)n=o.indented;r.lexical=new d(n,Je.stream.column(),e,null,r.lexical,t)};return r.lex=!0,r}function j(){var e=Je.state;e.lexical.prev&&(")"==e.lexical.type&&(e.indented=e.lexical.indented),e.lexical=e.lexical.prev)}function S(e){function t(r){return r==e?f():";"==e||"}"==r||")"==r||"]"==r?m():f(t)}return t}function T(e,t){return"var"==e?f(z("vardef",t),se,S(";"),j):"keyword a"==e?f(z("form"),P,T,j):"keyword b"==e?f(z("form"),T,j):"keyword d"==e?Je.stream.match(/^\s*$/,!1)?f():f(z("stat"),E,S(";"),j):"debugger"==e?f(S(";")):"{"==e?f(z("}"),x,X,j,M):";"==e?f():"if"==e?("else"==Je.state.lexical.info&&Je.state.cc[Je.state.cc.length-1]==j&&Je.state.cc.pop()(),f(z("form"),P,T,j,fe)):"function"==e?f(ye):"for"==e?f(z("form"),he,T,j):"class"==e||Ue&&"interface"==t?(Je.marked="keyword",f(z("form","class"==e?e:t),Me,j)):"variable"==e?Ue&&"declare"==t?(Je.marked="keyword",f(T)):Ue&&("module"==t||"enum"==t||"type"==t)&&Je.stream.match(/^\s*\w/,!1)?(Je.marked="keyword","enum"==t?f(Ne):"type"==t?f(ke,S("operator"),ee,S(";")):f(z("form"),ce,S("{"),z("}"),X,j,j)):Ue&&"namespace"==t?(Je.marked="keyword",f(z("form"),C,T,j)):Ue&&"abstract"==t?(Je.marked="keyword",f(T)):f(z("stat"),F):"switch"==e?f(z("form"),P,S("{"),z("}","switch"),x,X,j,j,M):"case"==e?f(C,S(":")):"default"==e?f(S(":")):"catch"==e?f(z("form"),w,A,T,j,M):"export"==e?f(z("stat"),Te,j):"import"==e?f(z("stat"),Ce,j):"async"==e?f(T):"@"==t?f(C,T):m(z("stat"),C,S(";"),j)}function A(e){if("("==e)return f(we,S(")"))}function C(e,t){return I(e,t,!1)}function q(e,t){return I(e,t,!0)}function P(e){return"("!=e?m():f(z(")"),C,S(")"),j)}function I(e,t,r){if(Je.state.fatArrowAt==Je.stream.start){var n=r?B:V;if("("==e)return f(w,z(")"),Y(we,")"),j,S("=>"),n,M);if("variable"==e)return m(w,ce,S("=>"),n,M)}var o=r?N:O;return Ze.hasOwnProperty(e)?f(o):"function"==e?f(ye,o):"class"==e||Ue&&"interface"==t?(Je.marked="keyword",f(z("form"),xe,j)):"keyword c"==e||"async"==e?f(r?q:C):"("==e?f(z(")"),E,S(")"),j,o):"operator"==e||"spread"==e?f(r?q:C):"["==e?f(z("]"),Oe,j,o):"{"==e?R(U,"}",null,o):"quasi"==e?m($,o):"new"==e?f(_(r)):"import"==e?f(C):f()}function E(e){return e.match(/[;\}\)\],]/)?m():m(C)}function O(e,t){return","==e?f(C):N(e,t,!1)}function N(e,t,r){var n=0==r?O:N,o=0==r?C:q;return"=>"==e?f(w,r?B:V,M):"operator"==e?/\+\+|--/.test(t)||Ue&&"!"==t?f(n):Ue&&"<"==t&&Je.stream.match(/^([^>]|<.*?>)*>\s*\(/,!1)?f(z(">"),Y(ee,">"),j,n):"?"==t?f(C,S(":"),o):f(o):"quasi"==e?m($,n):";"!=e?"("==e?R(q,")","call",n):"."==e?f(D,n):"["==e?f(z("]"),E,S("]"),j,n):Ue&&"as"==t?(Je.marked="keyword",f(ee,n)):"regexp"==e?(Je.state.lastType=Je.marked="operator",Je.stream.backUp(Je.stream.pos-Je.stream.start-1),f(o)):void 0:void 0}function $(e,t){return"quasi"!=e?m():"${"!=t.slice(t.length-2)?f($):f(C,K)}function K(e){if("}"==e)return Je.marked="string-2",Je.state.tokenize=s,f($)}function V(e){return c(Je.stream,Je.state),m("{"==e?T:C)}function B(e){return c(Je.stream,Je.state),m("{"==e?T:q)}function _(e){return function(t){return"."==t?f(e?L:W):"variable"==t&&Ue?f(ae,e?N:O):m(e?q:C)}}function W(e,t){if("target"==t)return Je.marked="keyword",f(O)}function L(e,t){if("target"==t)return Je.marked="keyword",f(N)}function F(e){return":"==e?f(j,T):m(O,S(";"),j)}function D(e){if("variable"==e)return Je.marked="property",f()}function U(e,t){if("async"==e)return Je.marked="property",f(U);if("variable"==e||"keyword"==Je.style){if(Je.marked="property","get"==t||"set"==t)return f(H);var r;return Ue&&Je.state.fatArrowAt==Je.stream.start&&(r=Je.stream.match(/^\s*:\s*/,!1))&&(Je.state.fatArrowAt=Je.stream.pos+r[0].length),f(G)}return"number"==e||"string"==e?(Je.marked=Fe?"property":Je.style+" property",f(G)):"jsonld-keyword"==e?f(G):Ue&&y(t)?(Je.marked="keyword",f(U)):"["==e?f(C,Z,S("]"),G):"spread"==e?f(q,G):"*"==t?(Je.marked="keyword",f(U)):":"==e?m(G):void 0}function H(e){return"variable"!=e?m(G):(Je.marked="property",f(ye))}function G(e){return":"==e?f(q):"("==e?m(ye):void 0}function Y(e,t,r){function n(o,a){if(r?r.indexOf(o)>-1:","==o){var i=Je.state.lexical;return"call"==i.info&&(i.pos=(i.pos||0)+1),f(function(r,n){return r==t||n==t?m():m(e)},n)}return o==t||a==t?f():r&&r.indexOf(";")>-1?m(e):f(S(t))}return function(r,o){return r==t||o==t?f():m(e,n)}}function R(e,t,r){for(var n=3;n<arguments.length;n++)Je.cc.push(arguments[n]);return f(z(t,r),Y(e,t),j)}function X(e){return"}"==e?f():m(T,X)}function Z(e,t){if(Ue){if(":"==e||"in"==t)return f(ee);if("?"==t)return f(Z)}}function J(e){if(Ue&&":"==e)return Je.stream.match(/^\s*\w+\s+is\b/,!1)?f(C,Q,ee):f(ee)}function Q(e,t){if("is"==t)return Je.marked="keyword",f()}function ee(e,t){return"keyof"==t||"typeof"==t||"infer"==t?(Je.marked="keyword",f("typeof"==t?q:ee)):"variable"==e||"void"==t?(Je.marked="type",f(oe)):"|"==t||"&"==t?f(ee):"string"==e||"number"==e||"atom"==e?f(oe):"["==e?f(z("]"),Y(ee,"]",","),j,oe):"{"==e?f(z("}"),Y(re,"}",",;"),j,oe):"("==e?f(Y(ne,")"),te,oe):"<"==e?f(Y(ee,">"),ee):void 0}function te(e){if("=>"==e)return f(ee)}function re(e,t){return"variable"==e||"keyword"==Je.style?(Je.marked="property",f(re)):"?"==t||"number"==e||"string"==e?f(re):":"==e?f(ee):"["==e?f(S("variable"),Z,S("]"),re):"("==e?m(ve,re):void 0}function ne(e,t){return"variable"==e&&Je.stream.match(/^\s*[?:]/,!1)||"?"==t?f(ne):":"==e?f(ee):"spread"==e?f(ne):m(ee)}function oe(e,t){return"<"==t?f(z(">"),Y(ee,">"),j,oe):"|"==t||"."==e||"&"==t?f(ee):"["==e?f(ee,S("]"),oe):"extends"==t||"implements"==t?(Je.marked="keyword",f(ee)):"?"==t?f(ee,S(":"),ee):void 0}function ae(e,t){if("<"==t)return f(z(">"),Y(ee,">"),j,oe)}function ie(){return m(ee,le)}function le(e,t){if("="==t)return f(ee)}function se(e,t){return"enum"==t?(Je.marked="keyword",f(Ne)):m(ce,Z,pe,me)}function ce(e,t){return Ue&&y(t)?(Je.marked="keyword",f(ce)):"variable"==e?(g(t),f()):"spread"==e?f(ce):"["==e?R(ue,"]"):"{"==e?R(de,"}"):void 0}function de(e,t){return"variable"!=e||Je.stream.match(/^\s*:/,!1)?("variable"==e&&(Je.marked="property"),"spread"==e?f(ce):"}"==e?m():"["==e?f(C,S("]"),S(":"),de):f(S(":"),ce,pe)):(g(t),f(pe))}function ue(){return m(ce,pe)}function pe(e,t){if("="==t)return f(q)}function me(e){if(","==e)return f(se)}function fe(e,t){if("keyword b"==e&&"else"==t)return f(z("form","else"),T,j)}function he(e,t){return"await"==t?f(he):"("==e?f(z(")"),ge,j):void 0}function ge(e){return"var"==e?f(se,be):"variable"==e?f(be):m(be)}function be(e,t){return")"==e?f():";"==e?f(be):"in"==t||"of"==t?(Je.marked="keyword",f(C,be)):m(C,be)}function ye(e,t){return"*"==t?(Je.marked="keyword",f(ye)):"variable"==e?(g(t),f(ye)):"("==e?f(w,z(")"),Y(we,")"),j,J,T,M):Ue&&"<"==t?f(z(">"),Y(ie,">"),j,ye):void 0}function ve(e,t){return"*"==t?(Je.marked="keyword",f(ve)):"variable"==e?(g(t),f(ve)):"("==e?f(w,z(")"),Y(we,")"),j,J,M):Ue&&"<"==t?f(z(">"),Y(ie,">"),j,ve):void 0}function ke(e,t){return"keyword"==e||"variable"==e?(Je.marked="type",f(ke)):"<"==t?f(z(">"),Y(ie,">"),j):void 0}function we(e,t){return"@"==t&&f(C,we),"spread"==e?f(we):Ue&&y(t)?(Je.marked="keyword",f(we)):Ue&&"this"==e?f(Z,pe):m(ce,Z,pe)}function xe(e,t){return"variable"==e?Me(e,t):ze(e,t)}function Me(e,t){if("variable"==e)return g(t),f(ze)}function ze(e,t){return"<"==t?f(z(">"),Y(ie,">"),j,ze):"extends"==t||"implements"==t||Ue&&","==e?("implements"==t&&(Je.marked="keyword"),f(Ue?ee:C,ze)):"{"==e?f(z("}"),je,j):void 0}function je(e,t){return"async"==e||"variable"==e&&("static"==t||"get"==t||"set"==t||Ue&&y(t))&&Je.stream.match(/^\s+[\w$\xa1-\uffff]/,!1)?(Je.marked="keyword",f(je)):"variable"==e||"keyword"==Je.style?(Je.marked="property",f(Ue?Se:ye,je)):"number"==e||"string"==e?f(Ue?Se:ye,je):"["==e?f(C,Z,S("]"),Ue?Se:ye,je):"*"==t?(Je.marked="keyword",f(je)):Ue&&"("==e?m(ve,je):";"==e||","==e?f(je):"}"==e?f():"@"==t?f(C,je):void 0}function Se(e,t){if("?"==t)return f(Se);if(":"==e)return f(ee,pe);if("="==t)return f(q);var r=Je.state.lexical.prev;return m(r&&"interface"==r.info?ve:ye)}function Te(e,t){return"*"==t?(Je.marked="keyword",f(Ee,S(";"))):"default"==t?(Je.marked="keyword",f(C,S(";"))):"{"==e?f(Y(Ae,"}"),Ee,S(";")):m(T)}function Ae(e,t){return"as"==t?(Je.marked="keyword",f(S("variable"))):"variable"==e?m(q,Ae):void 0}function Ce(e){return"string"==e?f():"("==e?m(C):m(qe,Pe,Ee)}function qe(e,t){return"{"==e?R(qe,"}"):("variable"==e&&g(t),"*"==t&&(Je.marked="keyword"),f(Ie))}function Pe(e){if(","==e)return f(qe,Pe)}function Ie(e,t){if("as"==t)return Je.marked="keyword",f(qe)}function Ee(e,t){if("from"==t)return Je.marked="keyword",f(C)}function Oe(e){return"]"==e?f():m(Y(q,"]"))}function Ne(){return m(z("form"),ce,S("{"),z("}"),Y($e,"}"),j,j)}function $e(){return m(ce,pe)}function Ke(e,t){return"operator"==e.lastType||","==e.lastType||Ye.test(t.charAt(0))||/[,.]/.test(t.charAt(0))}function Ve(e,t,r){return t.tokenize==a&&/^(?:operator|sof|keyword [bcd]|case|new|export|default|spread|[\[{}\(,;:]|=>)$/.test(t.lastType)||"quasi"==t.lastType&&/\{\s*$/.test(e.string.slice(0,e.pos-(r||0)))}var Be,_e,We=t.indentUnit,Le=r.statementIndent,Fe=r.jsonld,De=r.json||Fe,Ue=r.typescript,He=r.wordCharacters||/[\w$\xa1-\uffff]/,Ge=function(){function e(e){return{type:e,style:"keyword"}}var t=e("keyword a"),r=e("keyword b"),n=e("keyword c"),o=e("keyword d"),a=e("operator"),i={type:"atom",style:"atom"};return{if:e("if"),while:t,with:t,else:r,do:r,try:r,finally:r,return:o,break:o,continue:o,new:e("new"),delete:n,void:n,throw:n,debugger:e("debugger"),var:e("var"),const:e("var"),let:e("var"),function:e("function"),catch:e("catch"),for:e("for"),switch:e("switch"),case:e("case"),default:e("default"),in:a,typeof:a,instanceof:a,true:i,false:i,null:i,undefined:i,NaN:i,Infinity:i,this:e("this"),class:e("class"),super:e("atom"),yield:n,export:e("export"),import:e("import"),extends:n,await:n}}(),Ye=/[+\-*&%=<>!?|~^@]/,Re=/^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)"/,Xe="([{}])",Ze={atom:!0,number:!0,variable:!0,string:!0,regexp:!0,this:!0,"jsonld-keyword":!0},Je={state:null,column:null,marked:null,cc:null},Qe=new k("this",new k("arguments",null));return M.lex=!0,j.lex=!0,{startState:function(e){var t={tokenize:a,lastType:"sof",cc:[],lexical:new d((e||0)-We,0,"block",!1),localVars:r.localVars,context:r.localVars&&new v(null,null,!1),indented:e||0};return r.globalVars&&"object"==typeof r.globalVars&&(t.globalVars=r.globalVars),t},token:function(e,t){if(e.sol()&&(t.lexical.hasOwnProperty("align")||(t.lexical.align=!1),t.indented=e.indentation(),c(e,t)),t.tokenize!=l&&e.eatSpace())return null;var r=t.tokenize(e,t);return"comment"==Be?r:(t.lastType="operator"!=Be||"++"!=_e&&"--"!=_e?Be:"incdec",p(t,r,Be,_e,e))},indent:function(t,n){if(t.tokenize==l)return e.Pass;if(t.tokenize!=a)return 0;var o,i=n&&n.charAt(0),s=t.lexical;if(!/^\s*else\b/.test(n))for(var c=t.cc.length-1;c>=0;--c){var d=t.cc[c];if(d==j)s=s.prev;else if(d!=fe)break}for(;("stat"==s.type||"form"==s.type)&&("}"==i||(o=t.cc[t.cc.length-1])&&(o==O||o==N)&&!/^[,\.=+\-*:?[\(]/.test(n));)s=s.prev;Le&&")"==s.type&&"stat"==s.prev.type&&(s=s.prev);var u=s.type,p=i==u;return"vardef"==u?s.indented+("operator"==t.lastType||","==t.lastType?s.info.length+1:0):"form"==u&&"{"==i?s.indented:"form"==u?s.indented+We:"stat"==u?s.indented+(Ke(t,n)?Le||We:0):"switch"!=s.info||p||0==r.doubleIndentSwitch?s.align?s.column+(p?0:1):s.indented+(p?0:We):s.indented+(/^(?:case|default)\b/.test(n)?We:2*We)},electricInput:/^\s*(?:case .*?:|default:|\{|\})$/,blockCommentStart:De?null:"/*",blockCommentEnd:De?null:"*/",blockCommentContinue:De?null:" * ",lineComment:De?null:"//",fold:"brace",closeBrackets:"()[]{}''\"\"``",helperType:De?"json":"javascript",jsonldMode:Fe,jsonMode:De,expressionAllowed:Ve,skipExpression:function(e){var t=e.cc[e.cc.length-1];t!=C&&t!=q||e.cc.pop()}}}),e.registerHelper("wordChars","javascript",/[\w$]/),e.defineMIME("text/javascript","javascript"),e.defineMIME("text/ecmascript","javascript"),e.defineMIME("application/javascript","javascript"),e.defineMIME("application/x-javascript","javascript"),e.defineMIME("application/ecmascript","javascript"),e.defineMIME("application/json",{name:"javascript",json:!0}),e.defineMIME("application/x-json",{name:"javascript",json:!0}),e.defineMIME("application/ld+json",{name:"javascript",jsonld:!0}),e.defineMIME("text/typescript",{name:"javascript",typescript:!0}),e.defineMIME("application/typescript",{name:"javascript",typescript:!0})}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define("mode/css/css",["../../lib/codemirror"],e):e(CodeMirror)}(function(e){"use strict";function t(e){for(var t={},r=0;r<e.length;++r)t[e[r].toLowerCase()]=!0;return t}function r(e,t){for(var r,n=!1;null!=(r=e.next());){if(n&&"/"==r){t.tokenize=null;break}n="*"==r}return["comment","comment"]}e.defineMode("css",function(t,r){function n(e,t){return f=t,e}function o(e,t){var r=e.next();if(b[r]){var o=b[r](e,t);if(!1!==o)return o}return"@"==r?(e.eatWhile(/[\w\\\-]/),n("def",e.current())):"="==r||("~"==r||"|"==r)&&e.eat("=")?n(null,"compare"):'"'==r||"'"==r?(t.tokenize=a(r),t.tokenize(e,t)):"#"==r?(e.eatWhile(/[\w\\\-]/),n("atom","hash")):"!"==r?(e.match(/^\s*\w*/),n("keyword","important")):/\d/.test(r)||"."==r&&e.eat(/\d/)?(e.eatWhile(/[\w.%]/),n("number","unit")):"-"!==r?/[,+>*\/]/.test(r)?n(null,"select-op"):"."==r&&e.match(/^-?[_a-z][_a-z0-9-]*/i)?n("qualifier","qualifier"):/[:;{}\[\]\(\)]/.test(r)?n(null,r):e.match(/[\w-.]+(?=\()/)?(/^(url(-prefix)?|domain|regexp)$/.test(e.current().toLowerCase())&&(t.tokenize=i),n("variable callee","variable")):/[\w\\\-]/.test(r)?(e.eatWhile(/[\w\\\-]/),n("property","word")):n(null,null):/[\d.]/.test(e.peek())?(e.eatWhile(/[\w.%]/),n("number","unit")):e.match(/^-[\w\\\-]*/)?(e.eatWhile(/[\w\\\-]/),e.match(/^\s*:/,!1)?n("variable-2","variable-definition"):n("variable-2","variable")):e.match(/^\w+-/)?n("meta","meta"):void 0}function a(e){return function(t,r){for(var o,a=!1;null!=(o=t.next());){if(o==e&&!a){")"==e&&t.backUp(1);break}a=!a&&"\\"==o}return(o==e||!a&&")"!=e)&&(r.tokenize=null),n("string","string")}}function i(e,t){return e.next(),e.match(/\s*[\"\')]/,!1)?t.tokenize=null:t.tokenize=a(")"),n(null,"(")}function l(e,t,r){this.type=e,this.indent=t,this.prev=r}function s(e,t,r,n){return e.context=new l(r,t.indentation()+(!1===n?0:g),e.context),r}function c(e){return e.context.prev&&(e.context=e.context.prev),e.context.type}function d(e,t,r){return P[r.context.type](e,t,r)}function u(e,t,r,n){for(var o=n||1;o>0;o--)r.context=r.context.prev;return d(e,t,r)}function p(e){var t=e.current().toLowerCase();h=T.hasOwnProperty(t)?"atom":S.hasOwnProperty(t)?"keyword":"variable"}var m=r.inline;r.propertyKeywords||(r=e.resolveMode("text/css"));var f,h,g=t.indentUnit,b=r.tokenHooks,y=r.documentTypes||{},v=r.mediaTypes||{},k=r.mediaFeatures||{},w=r.mediaValueKeywords||{},x=r.propertyKeywords||{},M=r.nonStandardPropertyKeywords||{},z=r.fontProperties||{},j=r.counterDescriptors||{},S=r.colorKeywords||{},T=r.valueKeywords||{},A=r.allowNested,C=r.lineComment,q=!0===r.supportsAtComponent,P={};return P.top=function(e,t,r){if("{"==e)return s(r,t,"block");if("}"==e&&r.context.prev)return c(r);if(q&&/@component/i.test(e))return s(r,t,"atComponentBlock");if(/^@(-moz-)?document$/i.test(e))return s(r,t,"documentTypes");if(/^@(media|supports|(-moz-)?document|import)$/i.test(e))return s(r,t,"atBlock");if(/^@(font-face|counter-style)/i.test(e))return r.stateArg=e,"restricted_atBlock_before";if(/^@(-(moz|ms|o|webkit)-)?keyframes$/i.test(e))return"keyframes";if(e&&"@"==e.charAt(0))return s(r,t,"at");if("hash"==e)h="builtin";else if("word"==e)h="tag";else{if("variable-definition"==e)return"maybeprop";if("interpolation"==e)return s(r,t,"interpolation");if(":"==e)return"pseudo";if(A&&"("==e)return s(r,t,"parens")}return r.context.type},P.block=function(e,t,r){if("word"==e){var n=t.current().toLowerCase();return x.hasOwnProperty(n)?(h="property","maybeprop"):M.hasOwnProperty(n)?(h="string-2","maybeprop"):A?(h=t.match(/^\s*:(?:\s|$)/,!1)?"property":"tag","block"):(h+=" error","maybeprop")}return"meta"==e?"block":A||"hash"!=e&&"qualifier"!=e?P.top(e,t,r):(h="error","block")},P.maybeprop=function(e,t,r){return":"==e?s(r,t,"prop"):d(e,t,r)},P.prop=function(e,t,r){if(";"==e)return c(r);if("{"==e&&A)return s(r,t,"propBlock");if("}"==e||"{"==e)return u(e,t,r);if("("==e)return s(r,t,"parens");if("hash"!=e||/^#([0-9a-fA-f]{3,4}|[0-9a-fA-f]{6}|[0-9a-fA-f]{8})$/.test(t.current())){if("word"==e)p(t);else if("interpolation"==e)return s(r,t,"interpolation")}else h+=" error";return"prop"},P.propBlock=function(e,t,r){return"}"==e?c(r):"word"==e?(h="property","maybeprop"):r.context.type},P.parens=function(e,t,r){return"{"==e||"}"==e?u(e,t,r):")"==e?c(r):"("==e?s(r,t,"parens"):"interpolation"==e?s(r,t,"interpolation"):("word"==e&&p(t),"parens")},P.pseudo=function(e,t,r){return"meta"==e?"pseudo":"word"==e?(h="variable-3",r.context.type):d(e,t,r)},P.documentTypes=function(e,t,r){return"word"==e&&y.hasOwnProperty(t.current())?(h="tag",r.context.type):P.atBlock(e,t,r)},P.atBlock=function(e,t,r){if("("==e)return s(r,t,"atBlock_parens");if("}"==e||";"==e)return u(e,t,r);if("{"==e)return c(r)&&s(r,t,A?"block":"top");if("interpolation"==e)return s(r,t,"interpolation");if("word"==e){var n=t.current().toLowerCase();h="only"==n||"not"==n||"and"==n||"or"==n?"keyword":v.hasOwnProperty(n)?"attribute":k.hasOwnProperty(n)?"property":w.hasOwnProperty(n)?"keyword":x.hasOwnProperty(n)?"property":M.hasOwnProperty(n)?"string-2":T.hasOwnProperty(n)?"atom":S.hasOwnProperty(n)?"keyword":"error"}return r.context.type},P.atComponentBlock=function(e,t,r){return"}"==e?u(e,t,r):"{"==e?c(r)&&s(r,t,A?"block":"top",!1):("word"==e&&(h="error"),r.context.type)},P.atBlock_parens=function(e,t,r){return")"==e?c(r):"{"==e||"}"==e?u(e,t,r,2):P.atBlock(e,t,r)},P.restricted_atBlock_before=function(e,t,r){return"{"==e?s(r,t,"restricted_atBlock"):"word"==e&&"@counter-style"==r.stateArg?(h="variable","restricted_atBlock_before"):d(e,t,r)},P.restricted_atBlock=function(e,t,r){return"}"==e?(r.stateArg=null,c(r)):"word"==e?(h="@font-face"==r.stateArg&&!z.hasOwnProperty(t.current().toLowerCase())||"@counter-style"==r.stateArg&&!j.hasOwnProperty(t.current().toLowerCase())?"error":"property","maybeprop"):"restricted_atBlock"},P.keyframes=function(e,t,r){return"word"==e?(h="variable","keyframes"):"{"==e?s(r,t,"top"):d(e,t,r)},P.at=function(e,t,r){return";"==e?c(r):"{"==e||"}"==e?u(e,t,r):("word"==e?h="tag":"hash"==e&&(h="builtin"),"at")},P.interpolation=function(e,t,r){return"}"==e?c(r):"{"==e||";"==e?u(e,t,r):("word"==e?h="variable":"variable"!=e&&"("!=e&&")"!=e&&(h="error"),"interpolation")},{startState:function(e){return{tokenize:null,state:m?"block":"top",stateArg:null,context:new l(m?"block":"top",e||0,null)}},token:function(e,t){if(!t.tokenize&&e.eatSpace())return null;var r=(t.tokenize||o)(e,t);return r&&"object"==typeof r&&(f=r[1],r=r[0]),h=r,"comment"!=f&&(t.state=P[t.state](f,e,t)),h},indent:function(e,t){var r=e.context,n=t&&t.charAt(0),o=r.indent;return"prop"!=r.type||"}"!=n&&")"!=n||(r=r.prev),r.prev&&("}"!=n||"block"!=r.type&&"top"!=r.type&&"interpolation"!=r.type&&"restricted_atBlock"!=r.type?(")"!=n||"parens"!=r.type&&"atBlock_parens"!=r.type)&&("{"!=n||"at"!=r.type&&"atBlock"!=r.type)||(o=Math.max(0,r.indent-g)):(r=r.prev,o=r.indent)),o},electricChars:"}",blockCommentStart:"/*",blockCommentEnd:"*/",blockCommentContinue:" * ",lineComment:C,fold:"brace"}})
;var n=["domain","regexp","url","url-prefix"],o=t(n),a=["all","aural","braille","handheld","print","projection","screen","tty","tv","embossed"],i=t(a),l=["width","min-width","max-width","height","min-height","max-height","device-width","min-device-width","max-device-width","device-height","min-device-height","max-device-height","aspect-ratio","min-aspect-ratio","max-aspect-ratio","device-aspect-ratio","min-device-aspect-ratio","max-device-aspect-ratio","color","min-color","max-color","color-index","min-color-index","max-color-index","monochrome","min-monochrome","max-monochrome","resolution","min-resolution","max-resolution","scan","grid","orientation","device-pixel-ratio","min-device-pixel-ratio","max-device-pixel-ratio","pointer","any-pointer","hover","any-hover"],s=t(l),c=["landscape","portrait","none","coarse","fine","on-demand","hover","interlace","progressive"],d=t(c),u=["align-content","align-items","align-self","alignment-adjust","alignment-baseline","anchor-point","animation","animation-delay","animation-direction","animation-duration","animation-fill-mode","animation-iteration-count","animation-name","animation-play-state","animation-timing-function","appearance","azimuth","backface-visibility","background","background-attachment","background-blend-mode","background-clip","background-color","background-image","background-origin","background-position","background-repeat","background-size","baseline-shift","binding","bleed","bookmark-label","bookmark-level","bookmark-state","bookmark-target","border","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-style","border-bottom-width","border-collapse","border-color","border-image","border-image-outset","border-image-repeat","border-image-slice","border-image-source","border-image-width","border-left","border-left-color","border-left-style","border-left-width","border-radius","border-right","border-right-color","border-right-style","border-right-width","border-spacing","border-style","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-style","border-top-width","border-width","bottom","box-decoration-break","box-shadow","box-sizing","break-after","break-before","break-inside","caption-side","caret-color","clear","clip","color","color-profile","column-count","column-fill","column-gap","column-rule","column-rule-color","column-rule-style","column-rule-width","column-span","column-width","columns","content","counter-increment","counter-reset","crop","cue","cue-after","cue-before","cursor","direction","display","dominant-baseline","drop-initial-after-adjust","drop-initial-after-align","drop-initial-before-adjust","drop-initial-before-align","drop-initial-size","drop-initial-value","elevation","empty-cells","fit","fit-position","flex","flex-basis","flex-direction","flex-flow","flex-grow","flex-shrink","flex-wrap","float","float-offset","flow-from","flow-into","font","font-feature-settings","font-family","font-kerning","font-language-override","font-size","font-size-adjust","font-stretch","font-style","font-synthesis","font-variant","font-variant-alternates","font-variant-caps","font-variant-east-asian","font-variant-ligatures","font-variant-numeric","font-variant-position","font-weight","grid","grid-area","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-column","grid-column-end","grid-column-gap","grid-column-start","grid-gap","grid-row","grid-row-end","grid-row-gap","grid-row-start","grid-template","grid-template-areas","grid-template-columns","grid-template-rows","hanging-punctuation","height","hyphens","icon","image-orientation","image-rendering","image-resolution","inline-box-align","justify-content","justify-items","justify-self","left","letter-spacing","line-break","line-height","line-stacking","line-stacking-ruby","line-stacking-shift","line-stacking-strategy","list-style","list-style-image","list-style-position","list-style-type","margin","margin-bottom","margin-left","margin-right","margin-top","marks","marquee-direction","marquee-loop","marquee-play-count","marquee-speed","marquee-style","max-height","max-width","min-height","min-width","mix-blend-mode","move-to","nav-down","nav-index","nav-left","nav-right","nav-up","object-fit","object-position","opacity","order","orphans","outline","outline-color","outline-offset","outline-style","outline-width","overflow","overflow-style","overflow-wrap","overflow-x","overflow-y","padding","padding-bottom","padding-left","padding-right","padding-top","page","page-break-after","page-break-before","page-break-inside","page-policy","pause","pause-after","pause-before","perspective","perspective-origin","pitch","pitch-range","place-content","place-items","place-self","play-during","position","presentation-level","punctuation-trim","quotes","region-break-after","region-break-before","region-break-inside","region-fragment","rendering-intent","resize","rest","rest-after","rest-before","richness","right","rotation","rotation-point","ruby-align","ruby-overhang","ruby-position","ruby-span","shape-image-threshold","shape-inside","shape-margin","shape-outside","size","speak","speak-as","speak-header","speak-numeral","speak-punctuation","speech-rate","stress","string-set","tab-size","table-layout","target","target-name","target-new","target-position","text-align","text-align-last","text-decoration","text-decoration-color","text-decoration-line","text-decoration-skip","text-decoration-style","text-emphasis","text-emphasis-color","text-emphasis-position","text-emphasis-style","text-height","text-indent","text-justify","text-outline","text-overflow","text-shadow","text-size-adjust","text-space-collapse","text-transform","text-underline-position","text-wrap","top","transform","transform-origin","transform-style","transition","transition-delay","transition-duration","transition-property","transition-timing-function","unicode-bidi","user-select","vertical-align","visibility","voice-balance","voice-duration","voice-family","voice-pitch","voice-range","voice-rate","voice-stress","voice-volume","volume","white-space","widows","width","will-change","word-break","word-spacing","word-wrap","z-index","clip-path","clip-rule","mask","enable-background","filter","flood-color","flood-opacity","lighting-color","stop-color","stop-opacity","pointer-events","color-interpolation","color-interpolation-filters","color-rendering","fill","fill-opacity","fill-rule","image-rendering","marker","marker-end","marker-mid","marker-start","shape-rendering","stroke","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","text-rendering","baseline-shift","dominant-baseline","glyph-orientation-horizontal","glyph-orientation-vertical","text-anchor","writing-mode"],p=t(u),m=["scrollbar-arrow-color","scrollbar-base-color","scrollbar-dark-shadow-color","scrollbar-face-color","scrollbar-highlight-color","scrollbar-shadow-color","scrollbar-3d-light-color","scrollbar-track-color","shape-inside","searchfield-cancel-button","searchfield-decoration","searchfield-results-button","searchfield-results-decoration","zoom"],f=t(m),h=["font-family","src","unicode-range","font-variant","font-feature-settings","font-stretch","font-weight","font-style"],g=t(h),b=["additive-symbols","fallback","negative","pad","prefix","range","speak-as","suffix","symbols","system"],y=t(b),v=["aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","grey","green","greenyellow","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen"],k=t(v),w=["above","absolute","activeborder","additive","activecaption","afar","after-white-space","ahead","alias","all","all-scroll","alphabetic","alternate","always","amharic","amharic-abegede","antialiased","appworkspace","arabic-indic","armenian","asterisks","attr","auto","auto-flow","avoid","avoid-column","avoid-page","avoid-region","background","backwards","baseline","below","bidi-override","binary","bengali","blink","block","block-axis","bold","bolder","border","border-box","both","bottom","break","break-all","break-word","bullets","button","button-bevel","buttonface","buttonhighlight","buttonshadow","buttontext","calc","cambodian","capitalize","caps-lock-indicator","caption","captiontext","caret","cell","center","checkbox","circle","cjk-decimal","cjk-earthly-branch","cjk-heavenly-stem","cjk-ideographic","clear","clip","close-quote","col-resize","collapse","color","color-burn","color-dodge","column","column-reverse","compact","condensed","contain","content","contents","content-box","context-menu","continuous","copy","counter","counters","cover","crop","cross","crosshair","currentcolor","cursive","cyclic","darken","dashed","decimal","decimal-leading-zero","default","default-button","dense","destination-atop","destination-in","destination-out","destination-over","devanagari","difference","disc","discard","disclosure-closed","disclosure-open","document","dot-dash","dot-dot-dash","dotted","double","down","e-resize","ease","ease-in","ease-in-out","ease-out","element","ellipse","ellipsis","embed","end","ethiopic","ethiopic-abegede","ethiopic-abegede-am-et","ethiopic-abegede-gez","ethiopic-abegede-ti-er","ethiopic-abegede-ti-et","ethiopic-halehame-aa-er","ethiopic-halehame-aa-et","ethiopic-halehame-am-et","ethiopic-halehame-gez","ethiopic-halehame-om-et","ethiopic-halehame-sid-et","ethiopic-halehame-so-et","ethiopic-halehame-ti-er","ethiopic-halehame-ti-et","ethiopic-halehame-tig","ethiopic-numeric","ew-resize","exclusion","expanded","extends","extra-condensed","extra-expanded","fantasy","fast","fill","fixed","flat","flex","flex-end","flex-start","footnotes","forwards","from","geometricPrecision","georgian","graytext","grid","groove","gujarati","gurmukhi","hand","hangul","hangul-consonant","hard-light","hebrew","help","hidden","hide","higher","highlight","highlighttext","hiragana","hiragana-iroha","horizontal","hsl","hsla","hue","icon","ignore","inactiveborder","inactivecaption","inactivecaptiontext","infinite","infobackground","infotext","inherit","initial","inline","inline-axis","inline-block","inline-flex","inline-grid","inline-table","inset","inside","intrinsic","invert","italic","japanese-formal","japanese-informal","justify","kannada","katakana","katakana-iroha","keep-all","khmer","korean-hangul-formal","korean-hanja-formal","korean-hanja-informal","landscape","lao","large","larger","left","level","lighter","lighten","line-through","linear","linear-gradient","lines","list-item","listbox","listitem","local","logical","loud","lower","lower-alpha","lower-armenian","lower-greek","lower-hexadecimal","lower-latin","lower-norwegian","lower-roman","lowercase","ltr","luminosity","malayalam","match","matrix","matrix3d","media-controls-background","media-current-time-display","media-fullscreen-button","media-mute-button","media-play-button","media-return-to-realtime-button","media-rewind-button","media-seek-back-button","media-seek-forward-button","media-slider","media-sliderthumb","media-time-remaining-display","media-volume-slider","media-volume-slider-container","media-volume-sliderthumb","medium","menu","menulist","menulist-button","menulist-text","menulist-textfield","menutext","message-box","middle","min-intrinsic","mix","mongolian","monospace","move","multiple","multiply","myanmar","n-resize","narrower","ne-resize","nesw-resize","no-close-quote","no-drop","no-open-quote","no-repeat","none","normal","not-allowed","nowrap","ns-resize","numbers","numeric","nw-resize","nwse-resize","oblique","octal","opacity","open-quote","optimizeLegibility","optimizeSpeed","oriya","oromo","outset","outside","outside-shape","overlay","overline","padding","padding-box","painted","page","paused","persian","perspective","plus-darker","plus-lighter","pointer","polygon","portrait","pre","pre-line","pre-wrap","preserve-3d","progress","push-button","radial-gradient","radio","read-only","read-write","read-write-plaintext-only","rectangle","region","relative","repeat","repeating-linear-gradient","repeating-radial-gradient","repeat-x","repeat-y","reset","reverse","rgb","rgba","ridge","right","rotate","rotate3d","rotateX","rotateY","rotateZ","round","row","row-resize","row-reverse","rtl","run-in","running","s-resize","sans-serif","saturation","scale","scale3d","scaleX","scaleY","scaleZ","screen","scroll","scrollbar","scroll-position","se-resize","searchfield","searchfield-cancel-button","searchfield-decoration","searchfield-results-button","searchfield-results-decoration","self-start","self-end","semi-condensed","semi-expanded","separate","serif","show","sidama","simp-chinese-formal","simp-chinese-informal","single","skew","skewX","skewY","skip-white-space","slide","slider-horizontal","slider-vertical","sliderthumb-horizontal","sliderthumb-vertical","slow","small","small-caps","small-caption","smaller","soft-light","solid","somali","source-atop","source-in","source-out","source-over","space","space-around","space-between","space-evenly","spell-out","square","square-button","start","static","status-bar","stretch","stroke","sub","subpixel-antialiased","super","sw-resize","symbolic","symbols","system-ui","table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row","table-row-group","tamil","telugu","text","text-bottom","text-top","textarea","textfield","thai","thick","thin","threeddarkshadow","threedface","threedhighlight","threedlightshadow","threedshadow","tibetan","tigre","tigrinya-er","tigrinya-er-abegede","tigrinya-et","tigrinya-et-abegede","to","top","trad-chinese-formal","trad-chinese-informal","transform","translate","translate3d","translateX","translateY","translateZ","transparent","ultra-condensed","ultra-expanded","underline","unset","up","upper-alpha","upper-armenian","upper-greek","upper-hexadecimal","upper-latin","upper-norwegian","upper-roman","uppercase","urdu","url","var","vertical","vertical-text","visible","visibleFill","visiblePainted","visibleStroke","visual","w-resize","wait","wave","wider","window","windowframe","windowtext","words","wrap","wrap-reverse","x-large","x-small","xor","xx-large","xx-small"],x=t(w),M=n.concat(a).concat(l).concat(c).concat(u).concat(m).concat(v).concat(w);e.registerHelper("hintWords","css",M),e.defineMIME("text/css",{documentTypes:o,mediaTypes:i,mediaFeatures:s,mediaValueKeywords:d,propertyKeywords:p,nonStandardPropertyKeywords:f,fontProperties:g,counterDescriptors:y,colorKeywords:k,valueKeywords:x,tokenHooks:{"/":function(e,t){return!!e.eat("*")&&(t.tokenize=r,r(e,t))}},name:"css"}),e.defineMIME("text/x-scss",{mediaTypes:i,mediaFeatures:s,mediaValueKeywords:d,propertyKeywords:p,nonStandardPropertyKeywords:f,colorKeywords:k,valueKeywords:x,fontProperties:g,allowNested:!0,lineComment:"//",tokenHooks:{"/":function(e,t){return e.eat("/")?(e.skipToEnd(),["comment","comment"]):e.eat("*")?(t.tokenize=r,r(e,t)):["operator","operator"]},":":function(e){return!!e.match(/\s*\{/,!1)&&[null,null]},$:function(e){return e.match(/^[\w-]+/),e.match(/^\s*:/,!1)?["variable-2","variable-definition"]:["variable-2","variable"]},"#":function(e){return!!e.eat("{")&&[null,"interpolation"]}},name:"css",helperType:"scss"}),e.defineMIME("text/x-less",{mediaTypes:i,mediaFeatures:s,mediaValueKeywords:d,propertyKeywords:p,nonStandardPropertyKeywords:f,colorKeywords:k,valueKeywords:x,fontProperties:g,allowNested:!0,lineComment:"//",tokenHooks:{"/":function(e,t){return e.eat("/")?(e.skipToEnd(),["comment","comment"]):e.eat("*")?(t.tokenize=r,r(e,t)):["operator","operator"]},"@":function(e){return e.eat("{")?[null,"interpolation"]:!e.match(/^(charset|document|font-face|import|(-(moz|ms|o|webkit)-)?keyframes|media|namespace|page|supports)\b/i,!1)&&(e.eatWhile(/[\w\\\-]/),e.match(/^\s*:/,!1)?["variable-2","variable-definition"]:["variable-2","variable"])},"&":function(){return["atom","atom"]}},name:"css",helperType:"less"}),e.defineMIME("text/x-gss",{documentTypes:o,mediaTypes:i,mediaFeatures:s,propertyKeywords:p,nonStandardPropertyKeywords:f,fontProperties:g,counterDescriptors:y,colorKeywords:k,valueKeywords:x,supportsAtComponent:!0,tokenHooks:{"/":function(e,t){return!!e.eat("*")&&(t.tokenize=r,r(e,t))}},name:"css",helperType:"gss"})}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror"),require("../xml/xml"),require("../javascript/javascript"),require("../css/css")):"function"==typeof define&&define.amd?define("mode/htmlmixed/htmlmixed",["../../lib/codemirror","../xml/xml","../javascript/javascript","../css/css"],e):e(CodeMirror)}(function(e){"use strict";function t(e,t,r){var n=e.current(),o=n.search(t);return o>-1?e.backUp(n.length-o):n.match(/<\/?$/)&&(e.backUp(n.length),e.match(t,!1)||e.match(n)),r}function r(e){var t=s[e];return t||(s[e]=new RegExp("\\s+"+e+"\\s*=\\s*('|\")?([^'\"]+)('|\")?\\s*"))}function n(e,t){var n=e.match(r(t));return n?/^\s*(.*?)\s*$/.exec(n[2])[1]:""}function o(e,t){return new RegExp((t?"^":"")+"</s*"+e+"s*>","i")}function a(e,t){for(var r in e)for(var n=t[r]||(t[r]=[]),o=e[r],a=o.length-1;a>=0;a--)n.unshift(o[a])}function i(e,t){for(var r=0;r<e.length;r++){var o=e[r];if(!o[0]||o[1].test(n(t,o[0])))return o[2]}}var l={script:[["lang",/(javascript|babel)/i,"javascript"],["type",/^(?:text|application)\/(?:x-)?(?:java|ecma)script$|^module$|^$/i,"javascript"],["type",/./,"text/plain"],[null,null,"javascript"]],style:[["lang",/^css$/i,"css"],["type",/^(text\/)?(x-)?(stylesheet|css)$/i,"css"],["type",/./,"text/plain"],[null,null,"css"]]},s={};e.defineMode("htmlmixed",function(r,n){function s(n,a){var l,u=c.token(n,a.htmlState),p=/\btag\b/.test(u);if(p&&!/[<>\s\/]/.test(n.current())&&(l=a.htmlState.tagName&&a.htmlState.tagName.toLowerCase())&&d.hasOwnProperty(l))a.inTag=l+" ";else if(a.inTag&&p&&/>$/.test(n.current())){var m=/^([\S]+) (.*)/.exec(a.inTag);a.inTag=null;var f=">"==n.current()&&i(d[m[1]],m[2]),h=e.getMode(r,f),g=o(m[1],!0),b=o(m[1],!1);a.token=function(e,r){return e.match(g,!1)?(r.token=s,r.localState=r.localMode=null,null):t(e,b,r.localMode.token(e,r.localState))},a.localMode=h,a.localState=e.startState(h,c.indent(a.htmlState,"",""))}else a.inTag&&(a.inTag+=n.current(),n.eol()&&(a.inTag+=" "));return u}var c=e.getMode(r,{name:"xml",htmlMode:!0,multilineTagIndentFactor:n.multilineTagIndentFactor,multilineTagIndentPastTag:n.multilineTagIndentPastTag}),d={},u=n&&n.tags,p=n&&n.scriptTypes;if(a(l,d),u&&a(u,d),p)for(var m=p.length-1;m>=0;m--)d.script.unshift(["type",p[m].matches,p[m].mode]);return{startState:function(){return{token:s,inTag:null,localMode:null,localState:null,htmlState:e.startState(c)}},copyState:function(t){var r;return t.localState&&(r=e.copyState(t.localMode,t.localState)),{token:t.token,inTag:t.inTag,localMode:t.localMode,localState:r,htmlState:e.copyState(c,t.htmlState)}},token:function(e,t){return t.token(e,t)},indent:function(t,r,n){return!t.localMode||/^\s*<\//.test(r)?c.indent(t.htmlState,r,n):t.localMode.indent?t.localMode.indent(t.localState,r,n):e.Pass},innerMode:function(e){return{state:e.localState||e.htmlState,mode:e.localMode||c}}}},"xml","javascript","css"),e.defineMIME("text/html","htmlmixed")}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define("addon/mode/multiplex",["../../lib/codemirror"],e):e(CodeMirror)}(function(e){"use strict";e.multiplexingMode=function(t){function r(e,t,r,n){if("string"==typeof t){var o=e.indexOf(t,r);return n&&o>-1?o+t.length:o}var a=t.exec(r?e.slice(r):e);return a?a.index+r+(n?a[0].length:0):-1}var n=Array.prototype.slice.call(arguments,1);return{startState:function(){return{outer:e.startState(t),innerActive:null,inner:null}},copyState:function(r){return{outer:e.copyState(t,r.outer),innerActive:r.innerActive,inner:r.innerActive&&e.copyState(r.innerActive.mode,r.inner)}},token:function(o,a){if(a.innerActive){var i=a.innerActive,l=o.string;if(!i.close&&o.sol())return a.innerActive=a.inner=null,this.token(o,a);var s=i.close?r(l,i.close,o.pos,i.parseDelimiters):-1;if(s==o.pos&&!i.parseDelimiters)return o.match(i.close),a.innerActive=a.inner=null,i.delimStyle&&i.delimStyle+" "+i.delimStyle+"-close";s>-1&&(o.string=l.slice(0,s));var c=i.mode.token(o,a.inner);return s>-1&&(o.string=l),s==o.pos&&i.parseDelimiters&&(a.innerActive=a.inner=null),i.innerStyle&&(c=c?c+" "+i.innerStyle:i.innerStyle),c}for(var d=1/0,l=o.string,u=0;u<n.length;++u){var p=n[u],s=r(l,p.open,o.pos);if(s==o.pos){p.parseDelimiters||o.match(p.open),a.innerActive=p;var m=0;if(t.indent){var f=t.indent(a.outer,"","");f!==e.Pass&&(m=f)}return a.inner=e.startState(p.mode,m),p.delimStyle&&p.delimStyle+" "+p.delimStyle+"-open"}-1!=s&&s<d&&(d=s)}d!=1/0&&(o.string=l.slice(0,d));var h=t.token(o,a.outer);return d!=1/0&&(o.string=l),h},indent:function(r,n,o){var a=r.innerActive?r.innerActive.mode:t;return a.indent?a.indent(r.innerActive?r.inner:r.outer,n,o):e.Pass},blankLine:function(r){var o=r.innerActive?r.innerActive.mode:t;if(o.blankLine&&o.blankLine(r.innerActive?r.inner:r.outer),r.innerActive)"\n"===r.innerActive.close&&(r.innerActive=r.inner=null);else for(var a=0;a<n.length;++a){var i=n[a];"\n"===i.open&&(r.innerActive=i,r.inner=e.startState(i.mode,o.indent?o.indent(r.outer,"",""):0))}},electricChars:t.electricChars,innerMode:function(e){return e.inner?{state:e.inner,mode:e.innerActive.mode}:{state:e.outer,mode:t}}}}}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror"),require("../htmlmixed/htmlmixed"),require("../../addon/mode/multiplex")):"function"==typeof define&&define.amd?define("mode/htmlembedded/htmlembedded.js",["../../lib/codemirror","../htmlmixed/htmlmixed","../../addon/mode/multiplex"],e):e(CodeMirror)}(function(e){"use strict";e.defineMode("htmlembedded",function(t,r){var n=r.closeComment||"--%>";return e.multiplexingMode(e.getMode(t,"htmlmixed"),{open:r.openComment||"<%--",close:n,delimStyle:"comment",mode:{token:function(e){return e.skipTo(n)||e.skipToEnd(),"comment"}}},{open:r.open||r.scriptStartRegex||"<%",close:r.close||r.scriptEndRegex||"%>",mode:e.getMode(t,r.scriptingModeSpec)})},"htmlmixed"),e.defineMIME("application/x-ejs",{name:"htmlembedded",scriptingModeSpec:"javascript"}),e.defineMIME("application/x-aspx",{name:"htmlembedded",scriptingModeSpec:"text/x-csharp"}),e.defineMIME("application/x-jsp",{name:"htmlembedded",scriptingModeSpec:"text/x-java"}),e.defineMIME("application/x-erb",{name:"htmlembedded",scriptingModeSpec:"ruby"})}),function(e){"function"==typeof define&&define("modeHtml",["mode/htmlembedded/htmlembedded.js"],function(){})}();