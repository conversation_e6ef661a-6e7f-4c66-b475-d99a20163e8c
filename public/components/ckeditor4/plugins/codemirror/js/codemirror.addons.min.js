!function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define("addon/comment/continuecomment.js",["../../lib/codemirror"],e):e(CodeMirror)}(function(e){function t(t){if(t.getOption("disableInput"))return e.Pass;for(var i,o=t.listSelections(),r=[],s=0;s<o.length;s++){var a=o[s].head;if(!/\bcomment\b/.test(t.getTokenTypeAt(a)))return e.Pass;var l=t.getModeAt(a);if(i){if(i!=l)return e.Pass}else i=l;var c=null;if(i.blockCommentStart&&i.blockCommentContinue){var f,u=t.getLine(a.line).slice(0,a.ch),h=u.lastIndexOf(i.blockCommentEnd);if(-1!=h&&h==a.ch-i.blockCommentEnd.length);else if((f=u.lastIndexOf(i.blockCommentStart))>-1&&f>h){if(c=u.slice(0,f),/\S/.test(c)){c="";for(var d=0;d<f;++d)c+=" "}}else(f=u.indexOf(i.blockCommentContinue))>-1&&!/\S/.test(u.slice(0,f))&&(c=u.slice(0,f));null!=c&&(c+=i.blockCommentContinue)}if(null==c&&i.lineComment&&n(t)){var u=t.getLine(a.line),f=u.indexOf(i.lineComment);f>-1&&(c=u.slice(0,f),/\S/.test(c)?c=null:c+=i.lineComment+u.slice(f+i.lineComment.length).match(/^\s*/)[0])}if(null==c)return e.Pass;r[s]="\n"+c}t.operation(function(){for(var e=o.length-1;e>=0;e--)t.replaceRange(r[e],o[e].from(),o[e].to(),"+insert")})}function n(e){var t=e.getOption("continueComments");return!t||"object"!=typeof t||!1!==t.continueLineComment}e.defineOption("continueComments",null,function(n,i,o){if(o&&o!=e.Init&&n.removeKeyMap("continueComment"),i){var r="Enter";"string"==typeof i?r=i:"object"==typeof i&&i.key&&(r=i.key);var s={name:"continueComment"};s[r]=t,n.addKeyMap(s)}})}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define("addon/edit/closebrackets.js",["../../lib/codemirror"],e):e(CodeMirror)}(function(e){function t(e,t){return"pairs"==t&&"string"==typeof e?e:"object"==typeof e&&null!=e[t]?e[t]:u[t]}function n(e){for(var t=0;t<e.length;t++){var n=e.charAt(t),o="'"+n+"'";d[o]||(d[o]=i(n))}}function i(e){return function(t){return l(t,e)}}function o(e){var t=e.state.closeBrackets;return!t||t.override?t:e.getModeAt(e.getCursor()).closeBrackets||t}function r(n){var i=o(n);if(!i||n.getOption("disableInput"))return e.Pass;for(var r=t(i,"pairs"),s=n.listSelections(),a=0;a<s.length;a++){if(!s[a].empty())return e.Pass;var l=c(n,s[a].head);if(!l||r.indexOf(l)%2!=0)return e.Pass}for(var a=s.length-1;a>=0;a--){var f=s[a].head;n.replaceRange("",h(f.line,f.ch-1),h(f.line,f.ch+1),"+delete")}}function s(n){var i=o(n),r=i&&t(i,"explode");if(!r||n.getOption("disableInput"))return e.Pass;for(var s=n.listSelections(),a=0;a<s.length;a++){if(!s[a].empty())return e.Pass;var l=c(n,s[a].head);if(!l||r.indexOf(l)%2!=0)return e.Pass}n.operation(function(){var e=n.lineSeparator()||"\n";n.replaceSelection(e+e,null),n.execCommand("goCharLeft"),s=n.listSelections();for(var t=0;t<s.length;t++){var i=s[t].head.line;n.indentLine(i,null,!0),n.indentLine(i+1,null,!0)}})}function a(t){var n=e.cmpPos(t.anchor,t.head)>0;return{anchor:new h(t.anchor.line,t.anchor.ch+(n?-1:1)),head:new h(t.head.line,t.head.ch+(n?1:-1))}}function l(n,i){var r=o(n);if(!r||n.getOption("disableInput"))return e.Pass;var s=t(r,"pairs"),l=s.indexOf(i);if(-1==l)return e.Pass;for(var c,u=t(r,"closeBefore"),d=t(r,"triples"),g=s.charAt(l+1)==i,m=n.listSelections(),p=l%2==0,v=0;v<m.length;v++){var y,b=m[v],x=b.head,C=n.getRange(x,h(x.line,x.ch+1));if(p&&!b.empty())y="surround";else if(!g&&p||C!=i)if(g&&x.ch>1&&d.indexOf(i)>=0&&n.getRange(h(x.line,x.ch-2),x)==i+i){if(x.ch>2&&/\bstring/.test(n.getTokenTypeAt(h(x.line,x.ch-2))))return e.Pass;y="addFour"}else if(g){var k=0==x.ch?" ":n.getRange(h(x.line,x.ch-1),x);if(e.isWordChar(C)||k==i||e.isWordChar(k))return e.Pass;y="both"}else{if(!p||!(0===C.length||/\s/.test(C)||u.indexOf(C)>-1))return e.Pass;y="both"}else y=g&&f(n,x)?"both":d.indexOf(i)>=0&&n.getRange(x,h(x.line,x.ch+3))==i+i+i?"skipThree":"skip";if(c){if(c!=y)return e.Pass}else c=y}var w=l%2?s.charAt(l-1):i,S=l%2?i:s.charAt(l+1);n.operation(function(){if("skip"==c)n.execCommand("goCharRight");else if("skipThree"==c)for(var e=0;e<3;e++)n.execCommand("goCharRight");else if("surround"==c){for(var t=n.getSelections(),e=0;e<t.length;e++)t[e]=w+t[e]+S;n.replaceSelections(t,"around"),t=n.listSelections().slice();for(var e=0;e<t.length;e++)t[e]=a(t[e]);n.setSelections(t)}else"both"==c?(n.replaceSelection(w+S,null),n.triggerElectric(w+S),n.execCommand("goCharLeft")):"addFour"==c&&(n.replaceSelection(w+w+w+w,"before"),n.execCommand("goCharRight"))})}function c(e,t){var n=e.getRange(h(t.line,t.ch-1),h(t.line,t.ch+1));return 2==n.length?n:null}function f(e,t){var n=e.getTokenAt(h(t.line,t.ch+1));return/\bstring/.test(n.type)&&n.start==t.ch&&(0==t.ch||!/\bstring/.test(e.getTokenTypeAt(t)))}var u={pairs:"()[]{}''\"\"",closeBefore:")]}'\":;>",triples:"",explode:"[]{}"},h=e.Pos;e.defineOption("autoCloseBrackets",!1,function(i,o,r){r&&r!=e.Init&&(i.removeKeyMap(d),i.state.closeBrackets=null),o&&(n(t(o,"pairs")),i.state.closeBrackets=o,i.addKeyMap(d))});var d={Backspace:r,Enter:s};n(u.pairs+"`")}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define("addon/fold/xml-fold",["../../lib/codemirror"],e):e(CodeMirror)}(function(e){"use strict";function t(e,t){return e.line-t.line||e.ch-t.ch}function n(e,t,n,i){this.line=t,this.ch=n,this.cm=e,this.text=e.getLine(t),this.min=i?Math.max(i.from,e.firstLine()):e.firstLine(),this.max=i?Math.min(i.to-1,e.lastLine()):e.lastLine()}function i(e,t){var n=e.cm.getTokenTypeAt(h(e.line,t));return n&&/\btag\b/.test(n)}function o(e){if(!(e.line>=e.max))return e.ch=0,e.text=e.cm.getLine(++e.line),!0}function r(e){if(!(e.line<=e.min))return e.text=e.cm.getLine(--e.line),e.ch=e.text.length,!0}function s(e){for(;;){var t=e.text.indexOf(">",e.ch);if(-1==t){if(o(e))continue;return}{if(i(e,t+1)){var n=e.text.lastIndexOf("/",t),r=n>-1&&!/\S/.test(e.text.slice(n+1,t));return e.ch=t+1,r?"selfClose":"regular"}e.ch=t+1}}}function a(e){for(;;){var t=e.ch?e.text.lastIndexOf("<",e.ch-1):-1;if(-1==t){if(r(e))continue;return}if(i(e,t+1)){g.lastIndex=t,e.ch=t;var n=g.exec(e.text);if(n&&n.index==t)return n}else e.ch=t}}function l(e){for(;;){g.lastIndex=e.ch;var t=g.exec(e.text);if(!t){if(o(e))continue;return}{if(i(e,t.index+1))return e.ch=t.index+t[0].length,t;e.ch=t.index+1}}}function c(e){for(;;){var t=e.ch?e.text.lastIndexOf(">",e.ch-1):-1;if(-1==t){if(r(e))continue;return}{if(i(e,t+1)){var n=e.text.lastIndexOf("/",t),o=n>-1&&!/\S/.test(e.text.slice(n+1,t));return e.ch=t+1,o?"selfClose":"regular"}e.ch=t}}}function f(e,t){for(var n=[];;){var i,o=l(e),r=e.line,a=e.ch-(o?o[0].length:0);if(!o||!(i=s(e)))return;if("selfClose"!=i)if(o[1]){for(var c=n.length-1;c>=0;--c)if(n[c]==o[2]){n.length=c;break}if(c<0&&(!t||t==o[2]))return{tag:o[2],from:h(r,a),to:h(e.line,e.ch)}}else n.push(o[2])}}function u(e,t){for(var n=[];;){var i=c(e);if(!i)return;if("selfClose"!=i){var o=e.line,r=e.ch,s=a(e);if(!s)return;if(s[1])n.push(s[2]);else{for(var l=n.length-1;l>=0;--l)if(n[l]==s[2]){n.length=l;break}if(l<0&&(!t||t==s[2]))return{tag:s[2],from:h(e.line,e.ch),to:h(o,r)}}}else a(e)}}var h=e.Pos,d="A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD",g=new RegExp("<(/?)(["+d+"][A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD-:.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*)","g");e.registerHelper("fold","xml",function(e,i){for(var o=new n(e,i.line,0);;){var r=l(o);if(!r||o.line!=i.line)return;var a=s(o);if(!a)return;if(!r[1]&&"selfClose"!=a){var c=h(o.line,o.ch),u=f(o,r[2]);return u&&t(u.from,c)>0?{from:c,to:u.from}:null}}}),e.findMatchingTag=function(e,i,o){var r=new n(e,i.line,i.ch,o);if(-1!=r.text.indexOf(">")||-1!=r.text.indexOf("<")){var l=s(r),c=l&&h(r.line,r.ch),d=l&&a(r);if(l&&d&&!(t(r,i)>0)){var g={from:h(r.line,r.ch),to:c,tag:d[2]};return"selfClose"==l?{open:g,close:null,at:"open"}:d[1]?{open:u(r,d[2]),close:g,at:"close"}:(r=new n(e,c.line,c.ch,o),{open:g,close:f(r,d[2]),at:"open"})}}},e.findEnclosingTag=function(e,t,i,o){for(var r=new n(e,t.line,t.ch,i);;){var s=u(r,o);if(!s)break;var a=new n(e,t.line,t.ch,i),l=f(a,s.tag);if(l)return{open:s,close:l}}},e.scanForClosingTag=function(e,t,i,o){return f(new n(e,t.line,t.ch,o?{from:0,to:o}:null),i)}}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror"),require("../fold/xml-fold")):"function"==typeof define&&define.amd?define("addon/edit/closetag.js",["../../lib/codemirror","../fold/xml-fold"],e):e(CodeMirror)}(function(e){function t(t){if(t.getOption("disableInput"))return e.Pass;for(var n=t.listSelections(),i=[],l=t.getOption("autoCloseTags"),c=0;c<n.length;c++){if(!n[c].empty())return e.Pass;var f=n[c].head,u=t.getTokenAt(f),h=e.innerMode(t.getMode(),u.state),d=h.state;if("xml"!=h.mode.name||!d.tagName)return e.Pass;var g="html"==h.mode.configuration,m="object"==typeof l&&l.dontCloseTags||g&&s,p="object"==typeof l&&l.indentTags||g&&a,v=d.tagName;u.end>f.ch&&(v=v.slice(0,v.length-u.end+f.ch));var y=v.toLowerCase();if(!v||"string"==u.type&&(u.end!=f.ch||!/[\"\']/.test(u.string.charAt(u.string.length-1))||1==u.string.length)||"tag"==u.type&&"closeTag"==d.type||u.string.indexOf("/")==u.string.length-1||m&&o(m,y)>-1||r(t,v,f,d,!0))return e.Pass;var b="object"==typeof l&&l.emptyTags;if(b&&o(b,v)>-1)i[c]={text:"/>",newPos:e.Pos(f.line,f.ch+2)};else{var x=p&&o(p,y)>-1;i[c]={indent:x,text:">"+(x?"\n\n":"")+"</"+v+">",newPos:x?e.Pos(f.line+1,0):e.Pos(f.line,f.ch+1)}}}for(var C="object"==typeof l&&l.dontIndentOnAutoClose,c=n.length-1;c>=0;c--){var k=i[c];t.replaceRange(k.text,n[c].head,n[c].anchor,"+insert");var w=t.listSelections().slice(0);w[c]={head:k.newPos,anchor:k.newPos},t.setSelections(w),!C&&k.indent&&(t.indentLine(k.newPos.line,null,!0),t.indentLine(k.newPos.line+1,null,!0))}}function n(t,n){for(var i=t.listSelections(),o=[],s=n?"/":"</",a=t.getOption("autoCloseTags"),l="object"==typeof a&&a.dontIndentOnSlash,c=0;c<i.length;c++){if(!i[c].empty())return e.Pass;var f=i[c].head,u=t.getTokenAt(f),h=e.innerMode(t.getMode(),u.state),d=h.state;if(n&&("string"==u.type||"<"!=u.string.charAt(0)||u.start!=f.ch-1))return e.Pass;var g;if("xml"!=h.mode.name)if("htmlmixed"==t.getMode().name&&"javascript"==h.mode.name)g=s+"script";else{if("htmlmixed"!=t.getMode().name||"css"!=h.mode.name)return e.Pass;g=s+"style"}else{if(!d.context||!d.context.tagName||r(t,d.context.tagName,f,d))return e.Pass;g=s+d.context.tagName}">"!=t.getLine(f.line).charAt(u.end)&&(g+=">"),o[c]=g}if(t.replaceSelections(o),i=t.listSelections(),!l)for(var c=0;c<i.length;c++)(c==i.length-1||i[c].head.line<i[c+1].head.line)&&t.indentLine(i[c].head.line)}function i(t){return t.getOption("disableInput")?e.Pass:n(t,!0)}function o(e,t){if(e.indexOf)return e.indexOf(t);for(var n=0,i=e.length;n<i;++n)if(e[n]==t)return n;return-1}function r(t,n,i,o,r){if(!e.scanForClosingTag)return!1;var s=Math.min(t.lastLine()+1,i.line+500),a=e.scanForClosingTag(t,i,null,s);if(!a||a.tag!=n)return!1;for(var l=o.context,c=r?1:0;l&&l.tagName==n;l=l.prev)++c;i=a.to;for(var f=1;f<c;f++){var u=e.scanForClosingTag(t,i,null,s);if(!u||u.tag!=n)return!1;i=u.to}return!0}e.defineOption("autoCloseTags",!1,function(n,o,r){if(r!=e.Init&&r&&n.removeKeyMap("autoCloseTags"),o){var s={name:"autoCloseTags"};("object"!=typeof o||o.whenClosing)&&(s["'/'"]=function(e){return i(e)}),("object"!=typeof o||o.whenOpening)&&(s["'>'"]=function(e){return t(e)}),n.addKeyMap(s)}});var s=["area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"],a=["applet","blockquote","body","button","div","dl","fieldset","form","frameset","h1","h2","h3","h4","h5","h6","head","html","iframe","layer","legend","object","ol","p","select","table","ul"];e.commands.closeTag=function(e){return n(e)}}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define("addon/edit/matchbrackets.js",["../../lib/codemirror"],e):e(CodeMirror)}(function(e){function t(e){return e&&e.bracketRegex||/[(){}[\]]/}function n(e,n,o){var r=e.getLineHandle(n.line),s=n.ch-1,c=o&&o.afterCursor;null==c&&(c=/(^| )cm-fat-cursor($| )/.test(e.getWrapperElement().className));var f=t(o),u=!c&&s>=0&&f.test(r.text.charAt(s))&&l[r.text.charAt(s)]||f.test(r.text.charAt(s+1))&&l[r.text.charAt(++s)];if(!u)return null;var h=">"==u.charAt(1)?1:-1;if(o&&o.strict&&h>0!=(s==n.ch))return null;var d=e.getTokenTypeAt(a(n.line,s+1)),g=i(e,a(n.line,s+(h>0?1:0)),h,d||null,o);return null==g?null:{from:a(n.line,s),to:g&&g.pos,match:g&&g.ch==u.charAt(0),forward:h>0}}function i(e,n,i,o,r){for(var s=r&&r.maxScanLineLength||1e4,c=r&&r.maxScanLines||1e3,f=[],u=t(r),h=i>0?Math.min(n.line+c,e.lastLine()+1):Math.max(e.firstLine()-1,n.line-c),d=n.line;d!=h;d+=i){var g=e.getLine(d);if(g){var m=i>0?0:g.length-1,p=i>0?g.length:-1;if(!(g.length>s))for(d==n.line&&(m=n.ch-(i<0?1:0));m!=p;m+=i){var v=g.charAt(m);if(u.test(v)&&(void 0===o||e.getTokenTypeAt(a(d,m+1))==o)){var y=l[v];if(y&&">"==y.charAt(1)==i>0)f.push(v);else{if(!f.length)return{pos:a(d,m),ch:v};f.pop()}}}}}return d-i!=(i>0?e.lastLine():e.firstLine())&&null}function o(e,t,i){for(var o=e.state.matchBrackets.maxHighlightLineLength||1e3,r=[],l=e.listSelections(),c=0;c<l.length;c++){var f=l[c].empty()&&n(e,l[c].head,i);if(f&&e.getLine(f.from.line).length<=o){var u=f.match?"CodeMirror-matchingbracket":"CodeMirror-nonmatchingbracket";r.push(e.markText(f.from,a(f.from.line,f.from.ch+1),{className:u})),f.to&&e.getLine(f.to.line).length<=o&&r.push(e.markText(f.to,a(f.to.line,f.to.ch+1),{className:u}))}}if(r.length){s&&e.state.focused&&e.focus();var h=function(){e.operation(function(){for(var e=0;e<r.length;e++)r[e].clear()})};if(!t)return h;setTimeout(h,800)}}function r(e){e.operation(function(){e.state.matchBrackets.currentlyHighlighted&&(e.state.matchBrackets.currentlyHighlighted(),e.state.matchBrackets.currentlyHighlighted=null),e.state.matchBrackets.currentlyHighlighted=o(e,!1,e.state.matchBrackets)})}var s=/MSIE \d/.test(navigator.userAgent)&&(null==document.documentMode||document.documentMode<8),a=e.Pos,l={"(":")>",")":"(<","[":"]>","]":"[<","{":"}>","}":"{<","<":">>",">":"<<"};e.defineOption("matchBrackets",!1,function(t,n,i){i&&i!=e.Init&&(t.off("cursorActivity",r),t.state.matchBrackets&&t.state.matchBrackets.currentlyHighlighted&&(t.state.matchBrackets.currentlyHighlighted(),t.state.matchBrackets.currentlyHighlighted=null)),n&&(t.state.matchBrackets="object"==typeof n?n:{},t.on("cursorActivity",r))}),e.defineExtension("matchBrackets",function(){o(this,!0)}),e.defineExtension("findMatchingBracket",function(e,t,i){return(i||"boolean"==typeof t)&&(i?(i.strict=t,t=i):t=t?{strict:!0}:null),n(this,e,t)}),e.defineExtension("scanForBracket",function(e,t,n,o){return i(this,e,t,n,o)})}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror"),require("../fold/xml-fold")):"function"==typeof define&&define.amd?define("addon/edit/matchtags.js",["../../lib/codemirror","../fold/xml-fold"],e):e(CodeMirror)}(function(e){"use strict";function t(e){e.state.tagHit&&e.state.tagHit.clear(),e.state.tagOther&&e.state.tagOther.clear(),e.state.tagHit=e.state.tagOther=null}function n(n){n.state.failedTagMatch=!1,n.operation(function(){if(t(n),!n.somethingSelected()){var i=n.getCursor(),o=n.getViewport();o.from=Math.min(o.from,i.line),o.to=Math.max(i.line+1,o.to);var r=e.findMatchingTag(n,i,o);if(r){if(n.state.matchBothTags){var s="open"==r.at?r.open:r.close;s&&(n.state.tagHit=n.markText(s.from,s.to,{className:"CodeMirror-matchingtag"}))}var a="close"==r.at?r.open:r.close;a?n.state.tagOther=n.markText(a.from,a.to,{className:"CodeMirror-matchingtag"}):n.state.failedTagMatch=!0}}})}function i(e){e.state.failedTagMatch&&n(e)}e.defineOption("matchTags",!1,function(o,r,s){s&&s!=e.Init&&(o.off("cursorActivity",n),o.off("viewportChange",i),t(o)),r&&(o.state.matchBothTags="object"==typeof r&&r.bothTags,o.on("cursorActivity",n),o.on("viewportChange",i),n(o))}),e.commands.toMatchingTag=function(t){var n=e.findMatchingTag(t,t.getCursor());if(n){var i="close"==n.at?n.open:n.close;i&&t.extendSelection(i.to,i.from)}}}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define("addon/edit/trailingspace.js",["../../lib/codemirror"],e):e(CodeMirror)}(function(e){e.defineOption("showTrailingSpace",!1,function(t,n,i){i==e.Init&&(i=!1),i&&!n?t.removeOverlay("trailingspace"):!i&&n&&t.addOverlay({token:function(e){for(var t=e.string.length,n=t;n&&/\s/.test(e.string.charAt(n-1));--n);return n>e.pos?(e.pos=n,null):(e.pos=t,"trailingspace")},name:"trailingspace"})})}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define("addon/fold/foldcode",["../../lib/codemirror"],e):e(CodeMirror)}(function(e){"use strict";function t(t,o,r,s){function a(e){var n=l(t,o);if(!n||n.to.line-n.from.line<c)return null;for(var i=t.findMarksAt(n.from),r=0;r<i.length;++r)if(i[r].__isFold&&"fold"!==s){if(!e)return null;n.cleared=!0,i[r].clear()}return n}if(r&&r.call){var l=r;r=null}else var l=i(t,r,"rangeFinder");"number"==typeof o&&(o=e.Pos(o,0));var c=i(t,r,"minFoldSize"),f=a(!0);if(i(t,r,"scanUp"))for(;!f&&o.line>t.firstLine();)o=e.Pos(o.line-1,0),f=a(!1);if(f&&!f.cleared&&"unfold"!==s){var u=n(t,r);e.on(u,"mousedown",function(t){h.clear(),e.e_preventDefault(t)});var h=t.markText(f.from,f.to,{replacedWith:u,clearOnEnter:i(t,r,"clearOnEnter"),__isFold:!0});h.on("clear",function(n,i){e.signal(t,"unfold",t,n,i)}),e.signal(t,"fold",t,f.from,f.to)}}function n(e,t){var n=i(e,t,"widget");if("string"==typeof n){var o=document.createTextNode(n);n=document.createElement("span"),n.appendChild(o),n.className="CodeMirror-foldmarker"}else n&&(n=n.cloneNode(!0));return n}function i(e,t,n){if(t&&void 0!==t[n])return t[n];var i=e.options.foldOptions;return i&&void 0!==i[n]?i[n]:o[n]}e.newFoldFunction=function(e,n){return function(i,o){t(i,o,{rangeFinder:e,widget:n})}},e.defineExtension("foldCode",function(e,n,i){t(this,e,n,i)}),e.defineExtension("isFolded",function(e){for(var t=this.findMarksAt(e),n=0;n<t.length;++n)if(t[n].__isFold)return!0}),e.commands.toggleFold=function(e){e.foldCode(e.getCursor())},e.commands.fold=function(e){e.foldCode(e.getCursor(),null,"fold")},e.commands.unfold=function(e){e.foldCode(e.getCursor(),null,"unfold")},e.commands.foldAll=function(t){t.operation(function(){for(var n=t.firstLine(),i=t.lastLine();n<=i;n++)t.foldCode(e.Pos(n,0),null,"fold")})},e.commands.unfoldAll=function(t){t.operation(function(){for(var n=t.firstLine(),i=t.lastLine();n<=i;n++)t.foldCode(e.Pos(n,0),null,"unfold")})},e.registerHelper("fold","combine",function(){var e=Array.prototype.slice.call(arguments,0);return function(t,n){for(var i=0;i<e.length;++i){var o=e[i](t,n);if(o)return o}}}),e.registerHelper("fold","auto",function(e,t){for(var n=e.getHelpers(t,"fold"),i=0;i<n.length;i++){var o=n[i](e,t);if(o)return o}});var o={rangeFinder:e.fold.auto,widget:"↔",minFoldSize:0,scanUp:!1,clearOnEnter:!0};e.defineOption("foldOptions",null),e.defineExtension("foldOption",function(e,t){return i(this,e,t)})}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror"),require("./foldcode")):"function"==typeof define&&define.amd?define("addon/fold/foldgutter.js",["../../lib/codemirror","./foldcode"],e):e(CodeMirror)}(function(e){"use strict";function t(e){this.options=e,this.from=this.to=0}function n(e){return!0===e&&(e={}),null==e.gutter&&(e.gutter="CodeMirror-foldgutter"),null==e.indicatorOpen&&(e.indicatorOpen="CodeMirror-foldgutter-open"),null==e.indicatorFolded&&(e.indicatorFolded="CodeMirror-foldgutter-folded"),e}function i(e,t){for(var n=e.findMarks(u(t,0),u(t+1,0)),i=0;i<n.length;++i)if(n[i].__isFold&&n[i].find().from.line==t)return n[i]}function o(e){if("string"==typeof e){var t=document.createElement("div");return t.className=e+" CodeMirror-guttermarker-subtle",t}return e.cloneNode(!0)}function r(e,t,n){var r=e.state.foldGutter.options,s=t,a=e.foldOption(r,"minFoldSize"),l=e.foldOption(r,"rangeFinder");e.eachLine(t,n,function(t){var n=null;if(i(e,s))n=o(r.indicatorFolded);else{var c=u(s,0),f=l&&l(e,c);f&&f.to.line-f.from.line>=a&&(n=o(r.indicatorOpen))}e.setGutterMarker(t,r.gutter,n),++s})}function s(e){var t=e.getViewport(),n=e.state.foldGutter;n&&(e.operation(function(){r(e,t.from,t.to)}),n.from=t.from,n.to=t.to)}function a(e,t,n){var o=e.state.foldGutter;if(o){var r=o.options;if(n==r.gutter){var s=i(e,t);s?s.clear():e.foldCode(u(t,0),r.rangeFinder)}}}function l(e){var t=e.state.foldGutter;if(t){var n=t.options;t.from=t.to=0,clearTimeout(t.changeUpdate),t.changeUpdate=setTimeout(function(){s(e)},n.foldOnChangeTimeSpan||600)}}function c(e){var t=e.state.foldGutter;if(t){var n=t.options;clearTimeout(t.changeUpdate),t.changeUpdate=setTimeout(function(){var n=e.getViewport();t.from==t.to||n.from-t.to>20||t.from-n.to>20?s(e):e.operation(function(){n.from<t.from&&(r(e,n.from,t.from),t.from=n.from),n.to>t.to&&(r(e,t.to,n.to),t.to=n.to)})},n.updateViewportTimeSpan||400)}}function f(e,t){var n=e.state.foldGutter;if(n){var i=t.line;i>=n.from&&i<n.to&&r(e,i,i+1)}}e.defineOption("foldGutter",!1,function(i,o,r){r&&r!=e.Init&&(i.clearGutter(i.state.foldGutter.options.gutter),i.state.foldGutter=null,i.off("gutterClick",a),i.off("change",l),i.off("viewportChange",c),i.off("fold",f),i.off("unfold",f),i.off("swapDoc",l)),o&&(i.state.foldGutter=new t(n(o)),s(i),i.on("gutterClick",a),i.on("change",l),i.on("viewportChange",c),i.on("fold",f),i.on("unfold",f),i.on("swapDoc",l))});var u=e.Pos}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define("addon/fold/brace-fold.js",["../../lib/codemirror"],e):e(CodeMirror)}(function(e){"use strict";e.registerHelper("fold","brace",function(t,n){function i(i){for(var a=n.ch,l=0;;){var c=a<=0?-1:s.lastIndexOf(i,a-1);if(-1!=c){if(1==l&&c<n.ch)break;if(o=t.getTokenTypeAt(e.Pos(r,c+1)),!/^(comment|string)/.test(o))return c+1;a=c-1}else{if(1==l)break;l=1,a=s.length}}}var o,r=n.line,s=t.getLine(r),a="{",l="}",c=i("{");if(null==c&&(a="[",l="]",c=i("[")),null!=c){var f,u,h=1,d=t.lastLine();e:for(var g=r;g<=d;++g)for(var m=t.getLine(g),p=g==r?c:0;;){var v=m.indexOf(a,p),y=m.indexOf(l,p);if(v<0&&(v=m.length),y<0&&(y=m.length),(p=Math.min(v,y))==m.length)break;if(t.getTokenTypeAt(e.Pos(g,p+1))==o)if(p==v)++h;else if(!--h){f=g,u=p;break e}++p}if(null!=f&&r!=f)return{from:e.Pos(r,c),to:e.Pos(f,u)}}}),e.registerHelper("fold","import",function(t,n){function i(n){if(n<t.firstLine()||n>t.lastLine())return null;var i=t.getTokenAt(e.Pos(n,1));if(/\S/.test(i.string)||(i=t.getTokenAt(e.Pos(n,i.end+1))),"keyword"!=i.type||"import"!=i.string)return null;for(var o=n,r=Math.min(t.lastLine(),n+10);o<=r;++o){var s=t.getLine(o),a=s.indexOf(";");if(-1!=a)return{startCh:i.end,end:e.Pos(o,a)}}}var o,r=n.line,s=i(r);if(!s||i(r-1)||(o=i(r-2))&&o.end.line==r-1)return null;for(var a=s.end;;){var l=i(a.line+1);if(null==l)break;a=l.end}return{from:t.clipPos(e.Pos(r,s.startCh+1)),to:a}}),e.registerHelper("fold","include",function(t,n){function i(n){if(n<t.firstLine()||n>t.lastLine())return null;var i=t.getTokenAt(e.Pos(n,1));return/\S/.test(i.string)||(i=t.getTokenAt(e.Pos(n,i.end+1))),"meta"==i.type&&"#include"==i.string.slice(0,8)?i.start+8:void 0}var o=n.line,r=i(o);if(null==r||null!=i(o-1))return null;for(var s=o;;){if(null==i(s+1))break;++s}return{from:e.Pos(o,r+1),to:t.clipPos(e.Pos(s))}})}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define("addon/fold/comment-fold.js",["../../lib/codemirror"],e):e(CodeMirror)}(function(e){"use strict";e.registerGlobalHelper("fold","comment",function(e){return e.blockCommentStart&&e.blockCommentEnd},function(t,n){var i=t.getModeAt(n),o=i.blockCommentStart,r=i.blockCommentEnd;if(o&&r){for(var s,a=n.line,l=t.getLine(a),c=n.ch,f=0;;){var u=c<=0?-1:l.lastIndexOf(o,c-1);if(-1!=u){if(1==f&&u<n.ch)return;if(/comment/.test(t.getTokenTypeAt(e.Pos(a,u+1)))&&(0==u||l.slice(u-r.length,u)==r||!/comment/.test(t.getTokenTypeAt(e.Pos(a,u))))){s=u+o.length;break}c=u-1}else{if(1==f)return;f=1,c=l.length}}var h,d,g=1,m=t.lastLine();e:for(var p=a;p<=m;++p)for(var v=t.getLine(p),y=p==a?s:0;;){var b=v.indexOf(o,y),x=v.indexOf(r,y);if(b<0&&(b=v.length),x<0&&(x=v.length),(y=Math.min(b,x))==v.length)break;if(y==b)++g;else if(!--g){h=p,d=y;break e}++y}if(null!=h&&(a!=h||d!=s))return{from:e.Pos(a,s),to:e.Pos(h,d)}}})}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define("addon/fold/indent-fold.js",["../../lib/codemirror"],e):e(CodeMirror)}(function(e){"use strict";function t(t,n){var i=t.getLine(n),o=i.search(/\S/);return-1==o||/\bcomment\b/.test(t.getTokenTypeAt(e.Pos(n,o+1)))?-1:e.countColumn(i,null,t.getOption("tabSize"))}e.registerHelper("fold","indent",function(n,i){var o=t(n,i.line);if(!(o<0)){for(var r=null,s=i.line+1,a=n.lastLine();s<=a;++s){var l=t(n,s);if(-1==l);else{if(!(l>o))break;r=s}}return r?{from:e.Pos(i.line,n.getLine(i.line).length),to:e.Pos(r,n.getLine(r).length)}:void 0}})}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define("addon/hint/show-hint.js",["../../lib/codemirror"],e):e(CodeMirror)}(function(e){"use strict";function t(e,t){this.cm=e,this.options=t,this.widget=null,this.debounce=0,this.tick=0,this.startPos=this.cm.getCursor("start"),this.startLen=this.cm.getLine(this.startPos.line).length-this.cm.getSelection().length;var n=this;e.on("cursorActivity",this.activityFunc=function(){n.cursorActivity()})}function n(e,t,n){var i=e.options.hintOptions,o={};for(var r in g)o[r]=g[r];if(i)for(var r in i)void 0!==i[r]&&(o[r]=i[r]);if(n)for(var r in n)void 0!==n[r]&&(o[r]=n[r]);return o.hint.resolve&&(o.hint=o.hint.resolve(e,t)),o}function i(e){return"string"==typeof e?e:e.text}function o(e,t){function n(e,n){var o;o="string"!=typeof n?function(e){return n(e,t)}:i.hasOwnProperty(n)?i[n]:n,r[e]=o}var i={Up:function(){t.moveFocus(-1)},Down:function(){t.moveFocus(1)},PageUp:function(){t.moveFocus(1-t.menuSize(),!0)},PageDown:function(){t.moveFocus(t.menuSize()-1,!0)},Home:function(){t.setFocus(0)},End:function(){t.setFocus(t.length-1)},Enter:t.pick,Tab:t.pick,Esc:t.close};/Mac/.test(navigator.platform)&&(i["Ctrl-P"]=function(){t.moveFocus(-1)},i["Ctrl-N"]=function(){t.moveFocus(1)});var o=e.options.customKeys,r=o?{}:i;if(o)for(var s in o)o.hasOwnProperty(s)&&n(s,o[s]);var a=e.options.extraKeys;if(a)for(var s in a)a.hasOwnProperty(s)&&n(s,a[s]);return r}function r(e,t){for(;t&&t!=e;){if("LI"===t.nodeName.toUpperCase()&&t.parentNode==e)return t;t=t.parentNode}}function s(t,n){this.completion=t,this.data=n,this.picked=!1;var s=this,a=t.cm,l=a.getInputField().ownerDocument,c=l.defaultView||l.parentWindow,h=this.hints=l.createElement("ul"),d=t.cm.options.theme;h.className="CodeMirror-hints "+d,this.selectedHint=n.selectedHint||0;for(var g=n.list,m=0;m<g.length;++m){var p=h.appendChild(l.createElement("li")),v=g[m],y=f+(m!=this.selectedHint?"":" "+u);null!=v.className&&(y=v.className+" "+y),p.className=y,v.render?v.render(p,n,v):p.appendChild(l.createTextNode(v.displayText||i(v))),p.hintId=m}var b=a.cursorCoords(t.options.alignWithWord?n.from:null),x=b.left,C=b.bottom,k=!0;h.style.left=x+"px",h.style.top=C+"px";var w=c.innerWidth||Math.max(l.body.offsetWidth,l.documentElement.offsetWidth),S=c.innerHeight||Math.max(l.body.offsetHeight,l.documentElement.offsetHeight);(t.options.container||l.body).appendChild(h);var O=h.getBoundingClientRect(),A=O.bottom-S,L=h.scrollHeight>h.clientHeight+1,M=a.getScrollInfo();if(A>0){var T=O.bottom-O.top;if(b.top-(b.bottom-O.top)-T>0)h.style.top=(C=b.top-T)+"px",k=!1;else if(T>S){h.style.height=S-5+"px",h.style.top=(C=b.bottom-O.top)+"px";var j=a.getCursor();n.from.ch!=j.ch&&(b=a.cursorCoords(j),h.style.left=(x=b.left)+"px",O=h.getBoundingClientRect())}}var F=O.right-w;if(F>0&&(O.right-O.left>w&&(h.style.width=w-5+"px",F-=O.right-O.left-w),h.style.left=(x=b.left-F)+"px"),L)for(var P=h.firstChild;P;P=P.nextSibling)P.style.paddingRight=a.display.nativeBarWidth+"px";if(a.addKeyMap(this.keyMap=o(t,{moveFocus:function(e,t){s.changeActive(s.selectedHint+e,t)},setFocus:function(e){s.changeActive(e)},menuSize:function(){return s.screenAmount()},length:g.length,close:function(){t.close()},pick:function(){s.pick()},data:n})),t.options.closeOnUnfocus){var H;a.on("blur",this.onBlur=function(){H=setTimeout(function(){t.close()},100)}),a.on("focus",this.onFocus=function(){clearTimeout(H)})}return a.on("scroll",this.onScroll=function(){var e=a.getScrollInfo(),n=a.getWrapperElement().getBoundingClientRect(),i=C+M.top-e.top,o=i-(c.pageYOffset||(l.documentElement||l.body).scrollTop);if(k||(o+=h.offsetHeight),o<=n.top||o>=n.bottom)return t.close();h.style.top=i+"px",h.style.left=x+M.left-e.left+"px"}),e.on(h,"dblclick",function(e){var t=r(h,e.target||e.srcElement);t&&null!=t.hintId&&(s.changeActive(t.hintId),s.pick())}),e.on(h,"click",function(e){var n=r(h,e.target||e.srcElement);n&&null!=n.hintId&&(s.changeActive(n.hintId),t.options.completeOnSingleClick&&s.pick())}),e.on(h,"mousedown",function(){setTimeout(function(){a.focus()},20)}),e.signal(n,"select",g[this.selectedHint],h.childNodes[this.selectedHint]),!0}function a(e,t){if(!e.somethingSelected())return t;for(var n=[],i=0;i<t.length;i++)t[i].supportsSelection&&n.push(t[i]);return n}function l(e,t,n,i){if(e.async)e(t,i,n);else{var o=e(t,n);o&&o.then?o.then(i):i(o)}}function c(t,n){var i,o=t.getHelpers(n,"hint");if(o.length){var r=function(e,t,n){function i(o){if(o==r.length)return t(null);l(r[o],e,n,function(e){e&&e.list.length>0?t(e):i(o+1)})}var r=a(e,o);i(0)};return r.async=!0,r.supportsSelection=!0,r}return(i=t.getHelper(t.getCursor(),"hintWords"))?function(t){return e.hint.fromList(t,{words:i})}:e.hint.anyword?function(t,n){return e.hint.anyword(t,n)}:function(){}}var f="CodeMirror-hint",u="CodeMirror-hint-active";e.showHint=function(e,t,n){if(!t)return e.showHint(n);n&&n.async&&(t.async=!0);var i={hint:t};if(n)for(var o in n)i[o]=n[o];return e.showHint(i)},e.defineExtension("showHint",function(i){i=n(this,this.getCursor("start"),i);var o=this.listSelections();if(!(o.length>1)){if(this.somethingSelected()){if(!i.hint.supportsSelection)return;for(var r=0;r<o.length;r++)if(o[r].head.line!=o[r].anchor.line)return}this.state.completionActive&&this.state.completionActive.close();var s=this.state.completionActive=new t(this,i);s.options.hint&&(e.signal(this,"startCompletion",this),s.update(!0))}}),e.defineExtension("closeHint",function(){this.state.completionActive&&this.state.completionActive.close()});var h=window.requestAnimationFrame||function(e){return setTimeout(e,1e3/60)},d=window.cancelAnimationFrame||clearTimeout;t.prototype={close:function(){this.active()&&(this.cm.state.completionActive=null,this.tick=null,this.cm.off("cursorActivity",this.activityFunc),this.widget&&this.data&&e.signal(this.data,"close"),this.widget&&this.widget.close(),e.signal(this.cm,"endCompletion",this.cm))},active:function(){return this.cm.state.completionActive==this},pick:function(t,n){var o=t.list[n];o.hint?o.hint(this.cm,t,o):this.cm.replaceRange(i(o),o.from||t.from,o.to||t.to,"complete"),e.signal(t,"pick",o),this.close()},cursorActivity:function(){this.debounce&&(d(this.debounce),this.debounce=0);var e=this.cm.getCursor(),t=this.cm.getLine(e.line)
;if(e.line!=this.startPos.line||t.length-e.ch!=this.startLen-this.startPos.ch||e.ch<this.startPos.ch||this.cm.somethingSelected()||!e.ch||this.options.closeCharacters.test(t.charAt(e.ch-1)))this.close();else{var n=this;this.debounce=h(function(){n.update()}),this.widget&&this.widget.disable()}},update:function(e){if(null!=this.tick){var t=this,n=++this.tick;l(this.options.hint,this.cm,this.options,function(i){t.tick==n&&t.finishUpdate(i,e)})}},finishUpdate:function(t,n){this.data&&e.signal(this.data,"update");var i=this.widget&&this.widget.picked||n&&this.options.completeSingle;this.widget&&this.widget.close(),this.data=t,t&&t.list.length&&(i&&1==t.list.length?this.pick(t,0):(this.widget=new s(this,t),e.signal(t,"shown")))}},s.prototype={close:function(){if(this.completion.widget==this){this.completion.widget=null,this.hints.parentNode.removeChild(this.hints),this.completion.cm.removeKeyMap(this.keyMap);var e=this.completion.cm;this.completion.options.closeOnUnfocus&&(e.off("blur",this.onBlur),e.off("focus",this.onFocus)),e.off("scroll",this.onScroll)}},disable:function(){this.completion.cm.removeKeyMap(this.keyMap);var e=this;this.keyMap={Enter:function(){e.picked=!0}},this.completion.cm.addKeyMap(this.keyMap)},pick:function(){this.completion.pick(this.data,this.selectedHint)},changeActive:function(t,n){if(t>=this.data.list.length?t=n?this.data.list.length-1:0:t<0&&(t=n?0:this.data.list.length-1),this.selectedHint!=t){var i=this.hints.childNodes[this.selectedHint];i&&(i.className=i.className.replace(" "+u,"")),i=this.hints.childNodes[this.selectedHint=t],i.className+=" "+u,i.offsetTop<this.hints.scrollTop?this.hints.scrollTop=i.offsetTop-3:i.offsetTop+i.offsetHeight>this.hints.scrollTop+this.hints.clientHeight&&(this.hints.scrollTop=i.offsetTop+i.offsetHeight-this.hints.clientHeight+3),e.signal(this.data,"select",this.data.list[this.selectedHint],i)}},screenAmount:function(){return Math.floor(this.hints.clientHeight/this.hints.firstChild.offsetHeight)||1}},e.registerHelper("hint","auto",{resolve:c}),e.registerHelper("hint","fromList",function(t,n){var i,o=t.getCursor(),r=t.getTokenAt(o),s=e.Pos(o.line,r.start),a=o;r.start<o.ch&&/\w/.test(r.string.charAt(o.ch-r.start-1))?i=r.string.substr(0,o.ch-r.start):(i="",s=o);for(var l=[],c=0;c<n.words.length;c++){var f=n.words[c];f.slice(0,i.length)==i&&l.push(f)}if(l.length)return{list:l,from:s,to:a}}),e.commands.autocomplete=e.showHint;var g={hint:e.hint.auto,completeSingle:!0,alignWithWord:!0,closeCharacters:/[\s()\[\]{};:>,]/,closeOnUnfocus:!0,completeOnSingleClick:!0,container:null,customKeys:null,extraKeys:null};e.defineOption("hintOptions",null)}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define("addon/hint/javascript-hint.js",["../../lib/codemirror"],e):e(CodeMirror)}(function(e){function t(e,t){for(var n=0,i=e.length;n<i;++n)t(e[n])}function n(e,t){if(!Array.prototype.indexOf){for(var n=e.length;n--;)if(e[n]===t)return!0;return!1}return-1!=e.indexOf(t)}function i(t,n,i,o){var r=t.getCursor(),s=i(t,r);if(!/\b(?:string|comment)\b/.test(s.type)){var a=e.innerMode(t.getMode(),s.state);if("json"!==a.mode.helperType){s.state=a.state,/^[\w$_]*$/.test(s.string)?s.end>r.ch&&(s.end=r.ch,s.string=s.string.slice(0,r.ch-s.start)):s={start:r.ch,end:r.ch,string:"",state:s.state,type:"."==s.string?"property":null};for(var f=s;"property"==f.type;){if(f=i(t,c(r.line,f.start)),"."!=f.string)return;if(f=i(t,c(r.line,f.start)),!u)var u=[];u.push(f)}return{list:l(s,u,n,o),from:c(r.line,s.start),to:c(r.line,s.end)}}}}function o(e,t){return i(e,d,function(e,t){return e.getTokenAt(t)},t)}function r(e,t){var n=e.getTokenAt(t);return t.ch==n.start+1&&"."==n.string.charAt(0)?(n.end=n.start,n.string=".",n.type="property"):/^\.[\w$_]*$/.test(n.string)&&(n.type="property",n.start++,n.string=n.string.replace(/\./,"")),n}function s(e,t){return i(e,g,r,t)}function a(e,t){if(Object.getOwnPropertyNames&&Object.getPrototypeOf)for(var n=e;n;n=Object.getPrototypeOf(n))Object.getOwnPropertyNames(n).forEach(t);else for(var i in e)t(i)}function l(e,i,o,r){function s(e){0!=e.lastIndexOf(d,0)||n(c,e)||c.push(e)}function l(e){"string"==typeof e?t(f,s):e instanceof Array?t(u,s):e instanceof Function&&t(h,s),a(e,s)}var c=[],d=e.string,g=r&&r.globalScope||window;if(i&&i.length){var m,p=i.pop();for(p.type&&0===p.type.indexOf("variable")?(r&&r.additionalContext&&(m=r.additionalContext[p.string]),r&&!1===r.useGlobalScope||(m=m||g[p.string])):"string"==p.type?m="":"atom"==p.type?m=1:"function"==p.type&&(null==g.jQuery||"$"!=p.string&&"jQuery"!=p.string||"function"!=typeof g.jQuery?null!=g._&&"_"==p.string&&"function"==typeof g._&&(m=g._()):m=g.jQuery());null!=m&&i.length;)m=m[i.pop().string];null!=m&&l(m)}else{for(var v=e.state.localVars;v;v=v.next)s(v.name);for(var v=e.state.globalVars;v;v=v.next)s(v.name);r&&!1===r.useGlobalScope||l(g),t(o,s)}return c}var c=e.Pos;e.registerHelper("hint","javascript",o),e.registerHelper("hint","coffeescript",s);var f="charAt charCodeAt indexOf lastIndexOf substring substr slice trim trimLeft trimRight toUpperCase toLowerCase split concat match replace search".split(" "),u="length concat join splice push pop shift unshift slice reverse sort indexOf lastIndexOf every some filter forEach map reduce reduceRight ".split(" "),h="prototype apply call bind".split(" "),d="break case catch class const continue debugger default delete do else export extends false finally for function if in import instanceof new null return super switch this throw true try typeof var void while with yield".split(" "),g="and break catch class continue delete do else extends false finally for if in instanceof isnt new no not null of off on or return switch then throw true try typeof until void while with yes".split(" ")}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define("addon/format/autoFormatAll.js",["../../lib/codemirror"],e):e(CodeMirror)}(function(e){e.defineExtension("autoFormatAll",function(t,n){function i(){c+="\n",u=!0,++f}for(var o=this,r=o.getMode(),s=o.getRange(t,n).split("\n"),a=e.copyState(r,o.getTokenAt(t).state),l=o.getOption("tabSize"),c="",f=0,u=0==t.ch,h=0;h<s.length;++h){for(var d=new e.StringStream(s[h],l);!d.eol();){var g=e.innerMode(r,a),m=r.token(d,a),p=d.current();d.start=d.pos,u&&!/\S/.test(p)||(c+=p,u=!1),!u&&g.mode.newlineAfterToken&&g.mode.newlineAfterToken(m,p,d.string.slice(d.pos)||s[h+1]||"",g.state)&&i()}!d.pos&&r.blankLine&&r.blankLine(a),!u&&h<s.length-1&&i()}o.operation(function(){o.replaceRange(c,t,n);for(var e=t.line+1,i=t.line+f;e<=i;++e)o.indentLine(e,"smart");o.setCursor({line:0,ch:0})})})}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define("addon/format/formatting.js",["../../lib/codemirror"],e):e(CodeMirror)}(function(e){function t(e){for(var t=[/for\s*?\((.*?)\)/g,/&#?[a-z0-9]+;[\s\S]/g,/\"(.*?)((\")|$)/g,/\/\*(.*?)(\*\/|$)/g,/^\/\/.*/g],n=[],i=0;i<t.length;i++)for(var o=0;o<e.length;){var r=e.substr(o).match(t[i]);if(null==r)break;n.push({start:o+r.index,end:o+r.index+r[0].length}),o+=r.index+Math.max(1,r[0].length)}return n.sort(function(e,t){return e.start-t.start}),n}e.extendMode("css",{commentStart:"/*",commentEnd:"*/",newlineAfterToken:function(e,t){return/^[;{}]$/.test(t)}}),e.extendMode("javascript",{commentStart:"/*",commentEnd:"*/",wordWrapChars:[";","\\{","\\}"],autoFormatLineBreaks:function(e){var n=0,i=this.jsonMode?function(e){return e.replace(/([,{])/g,"$1\n").replace(/}/g,"\n}")}:function(e){return e.replace(/(;|\{|\})([^\r\n;])/g,"$1\n$2")},o=t(e),r="";if(null!=o){for(var s=0;s<o.length;s++)o[s].start>n&&(r+=i(e.substring(n,o[s].start)),n=o[s].start),o[s].start<=n&&o[s].end>=n&&(r+=e.substring(n,o[s].end),n=o[s].end);n<e.length&&(r+=i(e.substr(n)))}else r=i(e);return r.replace(/^\n*|\n*$/,"")}});e.extendMode("xml",{commentStart:"\x3c!--",commentEnd:"--\x3e",noBreak:!1,noBreakEmpty:null,tagType:"",tagName:"",isXML:!1,newlineAfterToken:function(e,t,n,i){var o="a|b|bdi|bdo|big|center|cite|del|em|font|i|img|ins|s|small|span|strike|strong|sub|sup|u",r="label|li|option|textarea|title|"+o,s=!1,a=null,l="";if(this.isXML="xml"==this.configuration,"comment"==e||/<!--/.test(n))return!1;if("tag"==e){if(0==t.indexOf("<")&&0==!t.indexOf("</")){this.tagType="open",a=t.match(/^<\s*?([\w]+?)$/i),this.tagName=null!=a?a[1]:"";var l=this.tagName.toLowerCase();-1!=("|"+r+"|").indexOf("|"+l+"|")&&(this.noBreak=!0)}if(0==t.indexOf(">")&&"open"==this.tagType){this.tagType="";var c=this.isXML?"[^<]*?":"";return RegExp("^"+c+"</s*?"+this.tagName+"s*?>","i").test(n)?(this.noBreak=!1,this.isXML||(this.tagName=""),!1):(s=this.noBreak,this.noBreak=!1,!s)}if(0==t.indexOf("</")&&(this.tagType="close",a=t.match(/^<\/\s*?([\w]+?)$/i),null!=a&&(l=a[1].toLowerCase()),-1!=("|"+o+"|").indexOf("|"+l+"|")&&(this.noBreak=!0)),0==t.indexOf(">")&&"close"==this.tagType)return this.tagType="",0==n.indexOf("<")&&(a=n.match(/^<\/?\s*?([\w]+?)(\s|>)/i),l=null!=a?a[1].toLowerCase():"",-1==("|"+r+"|").indexOf("|"+l+"|"))?(this.noBreak=!1,!0):(s=this.noBreak,this.noBreak=!1,!s)}return 0==n.indexOf("<")&&(this.noBreak=!1,this.isXML&&""!=this.tagName?(this.tagName="",!1):(a=n.match(/^<\/?\s*?([\w]+?)(\s|>)/i),l=null!=a?a[1].toLowerCase():"",-1==("|"+r+"|").indexOf("|"+l+"|")))}}),e.defineExtension("commentRange",function(t,n,i){var o=this,r=e.innerMode(o.getMode(),o.getTokenAt(n).state).mode;o.operation(function(){if(t)o.replaceRange(r.commentEnd,i),o.replaceRange(r.commentStart,n),o.setSelection(n,{line:i.line,ch:i.ch+r.commentStart.length+r.commentEnd.length}),n.line==i.line&&n.ch==i.ch&&o.setCursor(n.line,n.ch+r.commentStart.length);else{var e=o.getRange(n,i),s=e.indexOf(r.commentStart),a=e.lastIndexOf(r.commentEnd);s>-1&&a>-1&&a>s&&(e=e.substr(0,s)+e.substring(s+r.commentStart.length,a)+e.substr(a+r.commentEnd.length)),o.replaceRange(e,n,i),o.setSelection(n,{line:i.line,ch:i.ch-r.commentStart.length-r.commentEnd.length})}})}),e.defineExtension("autoIndentRange",function(e,t){var n=this;this.operation(function(){for(var i=e.line;i<=t.line;i++)n.indentLine(i,"smart")})}),e.defineExtension("autoFormatRange",function(t,n){function i(){c+="\n",u=!0,++f}for(var o=this,r=o.getMode(),s=o.getRange(t,n).split("\n"),a=e.copyState(r,o.getTokenAt(t).state),l=o.getOption("tabSize"),c="",f=0,u=0==t.ch,h=0;h<s.length;++h){for(var d=new e.StringStream(s[h],l);!d.eol();){var g=e.innerMode(r,a),m=r.token(d,a),p=d.current();d.start=d.pos,u&&!/\S/.test(p)||(c+=p,u=!1),!u&&g.mode.newlineAfterToken&&g.mode.newlineAfterToken(m,p,d.string.slice(d.pos)||s[h+1]||"",g.state)&&i()}!d.pos&&r.blankLine&&r.blankLine(a),!u&&h<s.length-1&&i()}o.operation(function(){o.replaceRange(c,t,n);for(var e=t.line+1,i=t.line+f;e<=i;++e)o.indentLine(e,"smart");o.setSelection(t,o.getCursor(!1))})})}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define("addon/selection/active-line.js",["../../lib/codemirror"],e):e(CodeMirror)}(function(e){"use strict";function t(e){for(var t=0;t<e.state.activeLines.length;t++)e.removeLineClass(e.state.activeLines[t],"wrap",r),e.removeLineClass(e.state.activeLines[t],"background",s),e.removeLineClass(e.state.activeLines[t],"gutter",a)}function n(e,t){if(e.length!=t.length)return!1;for(var n=0;n<e.length;n++)if(e[n]!=t[n])return!1;return!0}function i(e,i){for(var o=[],l=0;l<i.length;l++){var c=i[l],f=e.getOption("styleActiveLine");if("object"==typeof f&&f.nonEmpty?c.anchor.line==c.head.line:c.empty()){var u=e.getLineHandleVisualStart(c.head.line);o[o.length-1]!=u&&o.push(u)}}n(e.state.activeLines,o)||e.operation(function(){t(e);for(var n=0;n<o.length;n++)e.addLineClass(o[n],"wrap",r),e.addLineClass(o[n],"background",s),e.addLineClass(o[n],"gutter",a);e.state.activeLines=o})}function o(e,t){i(e,t.ranges)}var r="CodeMirror-activeline",s="CodeMirror-activeline-background",a="CodeMirror-activeline-gutter";e.defineOption("styleActiveLine",!1,function(n,r,s){var a=s!=e.Init&&s;r!=a&&(a&&(n.off("beforeSelectionChange",o),t(n),delete n.state.activeLines),r&&(n.state.activeLines=[],i(n,n.listSelections()),n.on("beforeSelectionChange",o)))})}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define("addon/search/searchcursor",["../../lib/codemirror"],e):e(CodeMirror)}(function(e){"use strict";function t(e){var t=e.flags;return null!=t?t:(e.ignoreCase?"i":"")+(e.global?"g":"")+(e.multiline?"m":"")}function n(e,n){for(var i=t(e),o=i,r=0;r<n.length;r++)-1==o.indexOf(n.charAt(r))&&(o+=n.charAt(r));return i==o?e:new RegExp(e.source,o)}function i(e){return/\\s|\\n|\n|\\W|\\D|\[\^/.test(e.source)}function o(e,t,i){t=n(t,"g");for(var o=i.line,r=i.ch,s=e.lastLine();o<=s;o++,r=0){t.lastIndex=r;var a=e.getLine(o),l=t.exec(a);if(l)return{from:m(o,l.index),to:m(o,l.index+l[0].length),match:l}}}function r(e,t,r){if(!i(t))return o(e,t,r);t=n(t,"gm");for(var s,a=1,l=r.line,c=e.lastLine();l<=c;){for(var f=0;f<a&&!(l>c);f++){var u=e.getLine(l++);s=null==s?u:s+"\n"+u}a*=2,t.lastIndex=r.ch;var h=t.exec(s);if(h){var d=s.slice(0,h.index).split("\n"),g=h[0].split("\n"),p=r.line+d.length-1,v=d[d.length-1].length;return{from:m(p,v),to:m(p+g.length-1,1==g.length?v+g[0].length:g[g.length-1].length),match:h}}}}function s(e,t){for(var n,i=0;;){t.lastIndex=i;var o=t.exec(e);if(!o)return n;if(n=o,(i=n.index+(n[0].length||1))==e.length)return n}}function a(e,t,i){t=n(t,"g");for(var o=i.line,r=i.ch,a=e.firstLine();o>=a;o--,r=-1){var l=e.getLine(o);r>-1&&(l=l.slice(0,r));var c=s(l,t);if(c)return{from:m(o,c.index),to:m(o,c.index+c[0].length),match:c}}}function l(e,t,i){t=n(t,"gm");for(var o,r=1,a=i.line,l=e.firstLine();a>=l;){for(var c=0;c<r;c++){var f=e.getLine(a--);o=null==o?f.slice(0,i.ch):f+"\n"+o}r*=2;var u=s(o,t);if(u){var h=o.slice(0,u.index).split("\n"),d=u[0].split("\n"),g=a+h.length,p=h[h.length-1].length;return{from:m(g,p),to:m(g+d.length-1,1==d.length?p+d[0].length:d[d.length-1].length),match:u}}}}function c(e,t,n,i){if(e.length==t.length)return n;for(var o=0,r=n+Math.max(0,e.length-t.length);;){if(o==r)return o;var s=o+r>>1,a=i(e.slice(0,s)).length;if(a==n)return s;a>n?r=s:o=s+1}}function f(e,t,n,i){if(!t.length)return null;var o=i?d:g,r=o(t).split(/\r|\n\r?/);e:for(var s=n.line,a=n.ch,l=e.lastLine()+1-r.length;s<=l;s++,a=0){var f=e.getLine(s).slice(a),u=o(f);if(1==r.length){var h=u.indexOf(r[0]);if(-1==h)continue e;var n=c(f,u,h,o)+a;return{from:m(s,c(f,u,h,o)+a),to:m(s,c(f,u,h+r[0].length,o)+a)}}var p=u.length-r[0].length;if(u.slice(p)==r[0]){for(var v=1;v<r.length-1;v++)if(o(e.getLine(s+v))!=r[v])continue e;var y=e.getLine(s+r.length-1),b=o(y),x=r[r.length-1];if(b.slice(0,x.length)==x)return{from:m(s,c(f,u,p,o)+a),to:m(s+r.length-1,c(y,b,x.length,o))}}}}function u(e,t,n,i){if(!t.length)return null;var o=i?d:g,r=o(t).split(/\r|\n\r?/);e:for(var s=n.line,a=n.ch,l=e.firstLine()-1+r.length;s>=l;s--,a=-1){var f=e.getLine(s);a>-1&&(f=f.slice(0,a));var u=o(f);if(1==r.length){var h=u.lastIndexOf(r[0]);if(-1==h)continue e;return{from:m(s,c(f,u,h,o)),to:m(s,c(f,u,h+r[0].length,o))}}var p=r[r.length-1];if(u.slice(0,p.length)==p){for(var v=1,n=s-r.length+1;v<r.length-1;v++)if(o(e.getLine(n+v))!=r[v])continue e;var y=e.getLine(s+1-r.length),b=o(y);if(b.slice(b.length-r[0].length)==r[0])return{from:m(s+1-r.length,c(y,b,y.length-r[0].length,o)),to:m(s,c(f,u,p.length,o))}}}}function h(e,t,i,s){this.atOccurrence=!1,this.doc=e,i=i?e.clipPos(i):m(0,0),this.pos={from:i,to:i};var c;"object"==typeof s?c=s.caseFold:(c=s,s=null),"string"==typeof t?(null==c&&(c=!1),this.matches=function(n,i){return(n?u:f)(e,t,i,c)}):(t=n(t,"gm"),s&&!1===s.multiline?this.matches=function(n,i){return(n?a:o)(e,t,i)}:this.matches=function(n,i){return(n?l:r)(e,t,i)})}var d,g,m=e.Pos;String.prototype.normalize?(d=function(e){return e.normalize("NFD").toLowerCase()},g=function(e){return e.normalize("NFD")}):(d=function(e){return e.toLowerCase()},g=function(e){return e}),h.prototype={findNext:function(){return this.find(!1)},findPrevious:function(){return this.find(!0)},find:function(t){for(var n=this.matches(t,this.doc.clipPos(t?this.pos.from:this.pos.to));n&&0==e.cmpPos(n.from,n.to);)t?n.from.ch?n.from=m(n.from.line,n.from.ch-1):n=n.from.line==this.doc.firstLine()?null:this.matches(t,this.doc.clipPos(m(n.from.line-1))):n.to.ch<this.doc.getLine(n.to.line).length?n.to=m(n.to.line,n.to.ch+1):n=n.to.line==this.doc.lastLine()?null:this.matches(t,m(n.to.line+1,0));if(n)return this.pos=n,this.atOccurrence=!0,this.pos.match||!0;var i=m(t?this.doc.firstLine():this.doc.lastLine()+1,0);return this.pos={from:i,to:i},this.atOccurrence=!1},from:function(){if(this.atOccurrence)return this.pos.from},to:function(){if(this.atOccurrence)return this.pos.to},replace:function(t,n){if(this.atOccurrence){var i=e.splitLines(t);this.doc.replaceRange(i,this.pos.from,this.pos.to,n),this.pos.to=m(this.pos.from.line+i.length-1,i[i.length-1].length+(1==i.length?this.pos.from.ch:0))}}},e.defineExtension("getSearchCursor",function(e,t,n){return new h(this.doc,e,t,n)}),e.defineDocExtension("getSearchCursor",function(e,t,n){return new h(this,e,t,n)}),e.defineExtension("selectMatches",function(t,n){for(var i=[],o=this.getSearchCursor(t,this.getCursor("from"),n);o.findNext()&&!(e.cmpPos(o.to(),this.getCursor("to"))>0);)i.push({anchor:o.from(),head:o.to()});i.length&&this.setSelections(i,0)})}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define("addon/scroll/annotatescrollbar",["../../lib/codemirror"],e):e(CodeMirror)}(function(e){"use strict";function t(e,t){function n(e){clearTimeout(i.doRedraw),i.doRedraw=setTimeout(function(){i.redraw()},e)}this.cm=e,this.options=t,this.buttonHeight=t.scrollButtonHeight||e.getOption("scrollButtonHeight"),this.annotations=[],this.doRedraw=this.doUpdate=null,this.div=e.getWrapperElement().appendChild(document.createElement("div")),this.div.style.cssText="position: absolute; right: 0; top: 0; z-index: 7; pointer-events: none",this.computeScale();var i=this;e.on("refresh",this.resizeHandler=function(){clearTimeout(i.doUpdate),i.doUpdate=setTimeout(function(){i.computeScale()&&n(20)},100)}),e.on("markerAdded",this.resizeHandler),e.on("markerCleared",this.resizeHandler),!1!==t.listenForChanges&&e.on("change",this.changeHandler=function(){n(250)})}e.defineExtension("annotateScrollbar",function(e){return"string"==typeof e&&(e={className:e}),new t(this,e)}),e.defineOption("scrollButtonHeight",0),t.prototype.computeScale=function(){var e=this.cm,t=(e.getWrapperElement().clientHeight-e.display.barHeight-2*this.buttonHeight)/e.getScrollerElement().scrollHeight;if(t!=this.hScale)return this.hScale=t,!0},t.prototype.update=function(e){this.annotations=e,this.redraw()},t.prototype.redraw=function(e){function t(e,t){return l!=e.line&&(l=e.line,c=n.getLineHandle(l)),c.widgets&&c.widgets.length||s&&c.height>a?n.charCoords(e,"local")[t?"top":"bottom"]:n.heightAtLine(c,"local")+(t?0:c.height)}!1!==e&&this.computeScale();var n=this.cm,i=this.hScale,o=document.createDocumentFragment(),r=this.annotations,s=n.getOption("lineWrapping"),a=s&&1.5*n.defaultTextHeight(),l=null,c=null,f=n.lastLine();if(n.display.barWidth)for(var u,h=0;h<r.length;h++){var d=r[h];if(!(d.to.line>f)){for(var g=u||t(d.from,!0)*i,m=t(d.to,!1)*i;h<r.length-1&&!(r[h+1].to.line>f)&&!((u=t(r[h+1].from,!0)*i)>m+.9);)d=r[++h],m=t(d.to,!1)*i;if(m!=g){var p=Math.max(m-g,3),v=o.appendChild(document.createElement("div"));v.style.cssText="position: absolute; right: 0px; width: "+Math.max(n.display.barWidth-1,2)+"px; top: "+(g+this.buttonHeight)+"px; height: "+p+"px",v.className=this.options.className,d.id&&v.setAttribute("annotation-id",d.id)}}}this.div.textContent="",this.div.appendChild(o)},t.prototype.clear=function(){this.cm.off("refresh",this.resizeHandler),this.cm.off("markerAdded",this.resizeHandler),this.cm.off("markerCleared",this.resizeHandler),this.changeHandler&&this.cm.off("change",this.changeHandler),this.div.parentNode.removeChild(this.div)}}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror"),require("./searchcursor"),require("../scroll/annotatescrollbar")):"function"==typeof define&&define.amd?define("addon/search/matchesonscrollbar",["../../lib/codemirror","./searchcursor","../scroll/annotatescrollbar"],e):e(CodeMirror)}(function(e){"use strict";function t(e,t,n,i){this.cm=e,this.options=i;var o={listenForChanges:!1};for(var r in i)o[r]=i[r];o.className||(o.className="CodeMirror-search-match"),this.annotation=e.annotateScrollbar(o),this.query=t,this.caseFold=n,this.gap={from:e.firstLine(),to:e.lastLine()+1},this.matches=[],this.update=null,this.findMatches(),this.annotation.update(this.matches);var s=this;e.on("change",this.changeHandler=function(e,t){s.onChange(t)})}function n(e,t,n){return e<=t?e:Math.max(t,e+n)}e.defineExtension("showMatchesOnScrollbar",function(e,n,i){return"string"==typeof i&&(i={className:i}),i||(i={}),new t(this,e,n,i)});t.prototype.findMatches=function(){if(this.gap){for(var t=0;t<this.matches.length;t++){var n=this.matches[t];if(n.from.line>=this.gap.to)break;n.to.line>=this.gap.from&&this.matches.splice(t--,1)}for(var i=this.cm.getSearchCursor(this.query,e.Pos(this.gap.from,0),{caseFold:this.caseFold,multiline:this.options.multiline}),o=this.options&&this.options.maxMatches||1e3;i.findNext();){var n={from:i.from(),to:i.to()};if(n.from.line>=this.gap.to)break;if(this.matches.splice(t++,0,n),this.matches.length>o)break}this.gap=null}},t.prototype.onChange=function(t){var i=t.from.line,o=e.changeEnd(t).line,r=o-t.to.line;if(this.gap?(this.gap.from=Math.min(n(this.gap.from,i,r),t.from.line),this.gap.to=Math.max(n(this.gap.to,i,r),t.from.line)):this.gap={from:t.from.line,to:o+1},r)for(var s=0;s<this.matches.length;s++){var a=this.matches[s],l=n(a.from.line,i,r);l!=a.from.line&&(a.from=e.Pos(l,a.from.ch));var c=n(a.to.line,i,r);c!=a.to.line&&(a.to=e.Pos(c,a.to.ch))}clearTimeout(this.update);var f=this;this.update=setTimeout(function(){f.updateAfterChange()},250)},t.prototype.updateAfterChange=function(){this.findMatches(),this.annotation.update(this.matches)},t.prototype.clear=function(){this.cm.off("change",this.changeHandler),this.annotation.clear()}}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror"),require("./matchesonscrollbar")):"function"==typeof define&&define.amd?define("addon/search/match-highlighter.js",["../../lib/codemirror","./matchesonscrollbar"],e):e(CodeMirror)}(function(e){"use strict";function t(e){this.options={};for(var t in u)this.options[t]=(e&&e.hasOwnProperty(t)?e:u)[t];this.overlay=this.timeout=null,this.matchesonscroll=null,this.active=!1}function n(e){var t=e.state.matchHighlighter;(t.active||e.hasFocus())&&o(e,t)}function i(e){var t=e.state.matchHighlighter;t.active||(t.active=!0,o(e,t))}function o(e,t){clearTimeout(t.timeout),t.timeout=setTimeout(function(){a(e)},t.options.delay)}function r(e,t,n,i){var o=e.state.matchHighlighter;if(e.addOverlay(o.overlay=f(t,n,i)),o.options.annotateScrollbar&&e.showMatchesOnScrollbar){var r=n?new RegExp("\\b"+t.replace(/[\\\[.+*?(){|^$]/g,"\\$&")+"\\b"):t;o.matchesonscroll=e.showMatchesOnScrollbar(r,!1,{className:"CodeMirror-selection-highlight-scrollbar"})}}function s(e){var t=e.state.matchHighlighter;t.overlay&&(e.removeOverlay(t.overlay),t.overlay=null,t.matchesonscroll&&(t.matchesonscroll.clear(),t.matchesonscroll=null))}function a(e){e.operation(function(){var t=e.state.matchHighlighter;if(s(e),!e.somethingSelected()&&t.options.showToken){for(var n=!0===t.options.showToken?/[\w$]/:t.options.showToken,i=e.getCursor(),o=e.getLine(i.line),a=i.ch,c=a;a&&n.test(o.charAt(a-1));)--a;for(;c<o.length&&n.test(o.charAt(c));)++c;return void(a<c&&r(e,o.slice(a,c),n,t.options.style))}var f=e.getCursor("from"),u=e.getCursor("to");if(f.line==u.line&&(!t.options.wordsOnly||l(e,f,u))){var h=e.getRange(f,u);t.options.trim&&(h=h.replace(/^\s+|\s+$/g,"")),h.length>=t.options.minChars&&r(e,h,!1,t.options.style)}})}function l(e,t,n){if(null!==e.getRange(t,n).match(/^\w+$/)){if(t.ch>0){var i={line:t.line,ch:t.ch-1},o=e.getRange(i,t);if(null===o.match(/\W/))return!1}if(n.ch<e.getLine(t.line).length){var i={line:n.line,ch:n.ch+1},o=e.getRange(n,i);if(null===o.match(/\W/))return!1}return!0}return!1}function c(e,t){return!(e.start&&t.test(e.string.charAt(e.start-1))||e.pos!=e.string.length&&t.test(e.string.charAt(e.pos)))}function f(e,t,n){return{token:function(i){if(i.match(e)&&(!t||c(i,t)))return n;i.next(),i.skipTo(e.charAt(0))||i.skipToEnd()}}}var u={style:"matchhighlight",minChars:2,delay:100,wordsOnly:!1,annotateScrollbar:!1,showToken:!1,trim:!0};e.defineOption("highlightSelectionMatches",!1,function(o,r,l){if(l&&l!=e.Init&&(s(o),clearTimeout(o.state.matchHighlighter.timeout),o.state.matchHighlighter=null,o.off("cursorActivity",n),o.off("focus",i)),r){var c=o.state.matchHighlighter=new t(r);o.hasFocus()?(c.active=!0,a(o)):o.on("focus",i),o.on("cursorActivity",n)}})}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define("addon/mode/multiplex.js",["../../lib/codemirror"],e):e(CodeMirror)}(function(e){"use strict";e.multiplexingMode=function(t){function n(e,t,n,i){if("string"==typeof t){var o=e.indexOf(t,n);return i&&o>-1?o+t.length:o}var r=t.exec(n?e.slice(n):e);return r?r.index+n+(i?r[0].length:0):-1}var i=Array.prototype.slice.call(arguments,1);return{startState:function(){return{outer:e.startState(t),innerActive:null,inner:null}},copyState:function(n){return{outer:e.copyState(t,n.outer),innerActive:n.innerActive,inner:n.innerActive&&e.copyState(n.innerActive.mode,n.inner)}},token:function(o,r){if(r.innerActive){var s=r.innerActive,a=o.string;if(!s.close&&o.sol())return r.innerActive=r.inner=null,this.token(o,r);var l=s.close?n(a,s.close,o.pos,s.parseDelimiters):-1;if(l==o.pos&&!s.parseDelimiters)return o.match(s.close),r.innerActive=r.inner=null,s.delimStyle&&s.delimStyle+" "+s.delimStyle+"-close";l>-1&&(o.string=a.slice(0,l));var c=s.mode.token(o,r.inner);return l>-1&&(o.string=a),l==o.pos&&s.parseDelimiters&&(r.innerActive=r.inner=null),s.innerStyle&&(c=c?c+" "+s.innerStyle:s.innerStyle),c}for(var f=1/0,a=o.string,u=0;u<i.length;++u){var h=i[u],l=n(a,h.open,o.pos);if(l==o.pos){h.parseDelimiters||o.match(h.open),r.innerActive=h;var d=0;if(t.indent){var g=t.indent(r.outer,"","");g!==e.Pass&&(d=g)}return r.inner=e.startState(h.mode,d),h.delimStyle&&h.delimStyle+" "+h.delimStyle+"-open"}-1!=l&&l<f&&(f=l)}f!=1/0&&(o.string=a.slice(0,f));var m=t.token(o,r.outer);return f!=1/0&&(o.string=a),m},indent:function(n,i,o){var r=n.innerActive?n.innerActive.mode:t;return r.indent?r.indent(n.innerActive?n.inner:n.outer,i,o):e.Pass},blankLine:function(n){var o=n.innerActive?n.innerActive.mode:t;if(o.blankLine&&o.blankLine(n.innerActive?n.inner:n.outer),n.innerActive)"\n"===n.innerActive.close&&(n.innerActive=n.inner=null);else for(var r=0;r<i.length;++r){var s=i[r];"\n"===s.open&&(n.innerActive=s,n.inner=e.startState(s.mode,o.indent?o.indent(n.outer,"",""):0))}},electricChars:t.electricChars,innerMode:function(e){return e.inner?{state:e.inner,mode:e.innerActive.mode}:{state:e.outer,mode:t}}}}}),function(e){"function"==typeof e.define&&e.define("addons",["addon/comment/continuecomment.js","addon/edit/closebrackets.js","addon/edit/closetag.js","addon/edit/matchbrackets.js","addon/edit/matchtags.js","addon/edit/trailingspace.js","addon/fold/foldgutter.js","addon/fold/brace-fold.js","addon/fold/comment-fold.js","addon/fold/indent-fold.js","addon/hint/show-hint.js","addon/hint/javascript-hint.js","addon/format/autoFormatAll.js","addon/format/formatting.js","addon/selection/active-line.js","addon/search/match-highlighter.js","addon/mode/multiplex.js"],function(){})}(this);